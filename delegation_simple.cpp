#include <iostream>
#include <string>
#include <memory>

// =============================================================================
// 委托模式演示 - 简化版
// =============================================================================

// 1. 基础委托示例
class Calculator {
public:
    int add(int a, int b) const {
        std::cout << "Calculator: " << a << " + " << b << " = " << (a + b) << std::endl;
        return a + b;
    }
    
    int multiply(int a, int b) const {
        std::cout << "Calculator: " << a << " * " << b << " = " << (a * b) << std::endl;
        return a * b;
    }
};

// MathProcessor 委托给 Calculator 来完成计算
class MathProcessor {
private:
    Calculator calc_;  // 委托对象

public:
    // 委托给Calculator
    int processAddition(int x, int y) {
        std::cout << "MathProcessor: 委托计算加法" << std::endl;
        return calc_.add(x, y);  // 委托调用
    }
    
    int processMultiplication(int x, int y) {
        std::cout << "MathProcessor: 委托计算乘法" << std::endl;
        return calc_.multiply(x, y);  // 委托调用
    }
    
    // 组合多个委托调用
    int processComplex(int a, int b, int c) {
        std::cout << "MathProcessor: 执行复杂计算 (a+b)*c" << std::endl;
        int sum = calc_.add(a, b);      // 委托调用1
        int result = calc_.multiply(sum, c);  // 委托调用2
        return result;
    }
};

// =============================================================================
// 2. 策略委托示例
// =============================================================================

class Logger {
public:
    virtual void log(const std::string& message) const = 0;
    virtual ~Logger() = default;
};

class ConsoleLogger : public Logger {
public:
    void log(const std::string& message) const override {
        std::cout << "[CONSOLE] " << message << std::endl;
    }
};

class FileLogger : public Logger {
public:
    void log(const std::string& message) const override {
        std::cout << "[FILE] Writing to file: " << message << std::endl;
    }
};

// Service类委托给不同的Logger
class UserService {
private:
    std::unique_ptr<Logger> logger_;  // 委托对象

public:
    UserService(std::unique_ptr<Logger> logger) 
        : logger_(std::move(logger)) {}
    
    void createUser(const std::string& username) {
        // 业务逻辑
        std::cout << "Creating user: " << username << std::endl;
        
        // 委托给logger记录日志
        logger_->log("User " + username + " created successfully");
    }
    
    void deleteUser(const std::string& username) {
        // 业务逻辑
        std::cout << "Deleting user: " << username << std::endl;
        
        // 委托给logger记录日志
        logger_->log("User " + username + " deleted");
    }
    
    // 运行时更换委托对象
    void setLogger(std::unique_ptr<Logger> logger) {
        logger_ = std::move(logger);
        logger_->log("Logger changed");
    }
};

// =============================================================================
// 3. 装饰器委托示例
// =============================================================================

class TextProcessor {
public:
    virtual std::string process(const std::string& text) const = 0;
    virtual ~TextProcessor() = default;
};

class BasicProcessor : public TextProcessor {
public:
    std::string process(const std::string& text) const override {
        return text;
    }
};

// 大写装饰器 - 委托给其他处理器
class UpperCaseDecorator : public TextProcessor {
private:
    std::unique_ptr<TextProcessor> processor_;  // 委托对象

public:
    UpperCaseDecorator(std::unique_ptr<TextProcessor> processor)
        : processor_(std::move(processor)) {}
    
    std::string process(const std::string& text) const override {
        // 先委托给被装饰的处理器
        std::string result = processor_->process(text);
        
        // 然后添加自己的功能
        std::string upper = result;
        for (char& c : upper) {
            c = std::toupper(c);
        }
        
        std::cout << "UpperCase: " << result << " -> " << upper << std::endl;
        return upper;
    }
};

// 前缀装饰器
class PrefixDecorator : public TextProcessor {
private:
    std::unique_ptr<TextProcessor> processor_;  // 委托对象
    std::string prefix_;

public:
    PrefixDecorator(std::unique_ptr<TextProcessor> processor, const std::string& prefix)
        : processor_(std::move(processor)), prefix_(prefix) {}
    
    std::string process(const std::string& text) const override {
        // 委托给被装饰的处理器
        std::string result = processor_->process(text);
        
        // 添加前缀
        std::string prefixed = prefix_ + result;
        std::cout << "Prefix: " << result << " -> " << prefixed << std::endl;
        return prefixed;
    }
};

// =============================================================================
// 演示函数
// =============================================================================

void demonstrateBasicDelegation() {
    std::cout << "\n=== 基础委托演示 ===" << std::endl;
    
    MathProcessor processor;
    
    processor.processAddition(5, 3);
    processor.processMultiplication(4, 6);
    processor.processComplex(2, 3, 4);  // (2+3)*4 = 20
}

void demonstrateStrategyDelegation() {
    std::cout << "\n=== 策略委托演示 ===" << std::endl;
    
    // 使用控制台日志
    UserService service(std::make_unique<ConsoleLogger>());
    service.createUser("Alice");
    service.deleteUser("Bob");
    
    std::cout << "\n切换到文件日志:" << std::endl;
    // 运行时更换委托对象
    service.setLogger(std::make_unique<FileLogger>());
    service.createUser("Charlie");
}

void demonstrateDecoratorDelegation() {
    std::cout << "\n=== 装饰器委托演示 ===" << std::endl;
    
    // 基础处理器
    std::unique_ptr<TextProcessor> processor = std::make_unique<BasicProcessor>();
    
    std::string text = "hello world";
    std::cout << "原始文本: " << text << std::endl;
    
    // 添加大写装饰器
    processor = std::make_unique<UpperCaseDecorator>(std::move(processor));
    
    // 添加前缀装饰器
    processor = std::make_unique<PrefixDecorator>(std::move(processor), ">>> ");
    
    // 最终处理
    std::string result = processor->process(text);
    std::cout << "最终结果: " << result << std::endl;
}

int main() {
    std::cout << "委托模式演示" << std::endl;
    
    demonstrateBasicDelegation();
    demonstrateStrategyDelegation();
    demonstrateDecoratorDelegation();
    
    std::cout << "\n=== 委托 vs 继承 vs 组合 ===" << std::endl;
    std::cout << "继承 (IS-A): Dog IS-A Animal" << std::endl;
    std::cout << "组合 (HAS-A): Car HAS-A Engine" << std::endl;
    std::cout << "委托 (USES-A): MathProcessor USES-A Calculator" << std::endl;
    
    std::cout << "\n委托的特点:" << std::endl;
    std::cout << "1. 将任务交给其他对象完成" << std::endl;
    std::cout << "2. 运行时可以更换委托对象" << std::endl;
    std::cout << "3. 松耦合，职责分离" << std::endl;
    std::cout << "4. 支持装饰器、策略等设计模式" << std::endl;
    
    return 0;
}
