{"version": "0.2.0", "configurations": [{"name": "(gdb) Windows 上的 Bash 启动", "type": "cppdbg", "request": "launch", "program": "输入程序名称，例如 ${workspaceFolder}/a.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "pipeTransport": {"debuggerPath": "/usr/bin/gdb", "pipeProgram": "${env:windir}\\system32\\bash.exe", "pipeArgs": ["-c"], "pipeCwd": ""}, "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}]}, {"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "d:/Desktop/CPP", "program": "d:/Desktop/CPP/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}