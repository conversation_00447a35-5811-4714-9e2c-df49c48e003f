#include <iostream>
#include <string>
#include <vector>
#include <memory>

// =============================================================================
// IS-A 关系演示（继承关系）
// =============================================================================

// 基类：动物
class Animal {
protected:
    std::string name_;
    int age_;

public:
    Animal(const std::string& name, int age) : name_(name), age_(age) {}
    
    virtual void makeSound() const = 0;  // 纯虚函数
    virtual void move() const {
        std::cout << name_ << " is moving" << std::endl;
    }
    
    void eat() const {
        std::cout << name_ << " is eating" << std::endl;
    }
    
    virtual ~Animal() = default;
};

// 派生类：狗 - Dog IS-A Animal
class Dog : public Animal {
public:
    Dog(const std::string& name, int age) : Animal(name, age) {}
    
    void makeSound() const override {
        std::cout << name_ << " says: Woof! Woof!" << std::endl;
    }
    
    void move() const override {
        std::cout << name_ << " is running on four legs" << std::endl;
    }
    
    // 狗特有的行为
    void wagTail() const {
        std::cout << name_ << " is wagging tail happily!" << std::endl;
    }
};

// 派生类：鸟 - Bird IS-A Animal
class Bird : public Animal {
public:
    Bird(const std::string& name, int age) : Animal(name, age) {}
    
    void makeSound() const override {
        std::cout << name_ << " says: Tweet! Tweet!" << std::endl;
    }
    
    void move() const override {
        std::cout << name_ << " is flying in the sky" << std::endl;
    }
    
    // 鸟特有的行为
    void fly() const {
        std::cout << name_ << " is soaring high!" << std::endl;
    }
};

// =============================================================================
// HAS-A 关系演示（组合关系）
// =============================================================================

// 引擎类
class Engine {
private:
    std::string type_;
    int horsepower_;

public:
    Engine(const std::string& type, int hp) : type_(type), horsepower_(hp) {}
    
    void start() const {
        std::cout << type_ << " engine (" << horsepower_ << "HP) is starting..." << std::endl;
    }
    
    void stop() const {
        std::cout << type_ << " engine is stopping..." << std::endl;
    }
    
    std::string getType() const { return type_; }
    int getHorsepower() const { return horsepower_; }
};

// 轮子类
class Wheel {
private:
    int size_;  // 尺寸（英寸）
    std::string brand_;

public:
    Wheel(int size, const std::string& brand) : size_(size), brand_(brand) {}
    
    void rotate() const {
        std::cout << brand_ << " wheel (" << size_ << " inch) is rotating" << std::endl;
    }
    
    int getSize() const { return size_; }
    std::string getBrand() const { return brand_; }
};

// 汽车类 - Car HAS-A Engine, Car HAS-A Wheels
class Car {
private:
    std::string model_;
    Engine engine_;                    // HAS-A：汽车有一个引擎
    std::vector<Wheel> wheels_;        // HAS-A：汽车有多个轮子

public:
    Car(const std::string& model, const Engine& engine, const std::vector<Wheel>& wheels)
        : model_(model), engine_(engine), wheels_(wheels) {}
    
    void start() {
        std::cout << "Starting " << model_ << "..." << std::endl;
        engine_.start();  // 委托给引擎对象
        std::cout << model_ << " is ready to drive!" << std::endl;
    }
    
    void drive() {
        std::cout << model_ << " is driving..." << std::endl;
        for (const auto& wheel : wheels_) {
            wheel.rotate();  // 委托给轮子对象
        }
    }
    
    void stop() {
        std::cout << "Stopping " << model_ << "..." << std::endl;
        engine_.stop();  // 委托给引擎对象
        std::cout << model_ << " has stopped." << std::endl;
    }
    
    // 获取组件信息
    void showSpecs() const {
        std::cout << "\n=== " << model_ << " Specifications ===" << std::endl;
        std::cout << "Engine: " << engine_.getType() << " (" << engine_.getHorsepower() << "HP)" << std::endl;
        std::cout << "Wheels: " << wheels_.size() << " x " << wheels_[0].getSize() 
                  << " inch " << wheels_[0].getBrand() << std::endl;
    }
};

// =============================================================================
// 复杂示例：同时包含 IS-A 和 HAS-A 关系
// =============================================================================

// 人类基类
class Person {
protected:
    std::string name_;
    int age_;

public:
    Person(const std::string& name, int age) : name_(name), age_(age) {}

    virtual void introduce() const {
        std::cout << "Hi, I'm " << name_ << ", " << age_ << " years old." << std::endl;
    }

    // 添加公共访问方法
    std::string getName() const { return name_; }
    int getAge() const { return age_; }

    virtual ~Person() = default;
};

// 学生类 - Student IS-A Person
class Student : public Person {
private:
    std::string school_;
    std::vector<std::string> courses_;  // HAS-A：学生有多门课程

public:
    Student(const std::string& name, int age, const std::string& school)
        : Person(name, age), school_(school) {}
    
    void introduce() const override {
        Person::introduce();
        std::cout << "I'm a student at " << school_ << std::endl;
    }
    
    // HAS-A 关系：管理课程
    void addCourse(const std::string& course) {
        courses_.push_back(course);
        std::cout << name_ << " enrolled in " << course << std::endl;
    }
    
    void showCourses() const {
        std::cout << name_ << "'s courses: ";
        for (size_t i = 0; i < courses_.size(); ++i) {
            std::cout << courses_[i];
            if (i < courses_.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
    }
};

// 教师类 - Teacher IS-A Person
class Teacher : public Person {
private:
    std::string subject_;
    std::vector<std::unique_ptr<Student>> students_;  // HAS-A：教师有多个学生

public:
    Teacher(const std::string& name, int age, const std::string& subject)
        : Person(name, age), subject_(subject) {}
    
    void introduce() const override {
        Person::introduce();
        std::cout << "I teach " << subject_ << std::endl;
    }
    
    // HAS-A 关系：管理学生
    void addStudent(std::unique_ptr<Student> student) {
        std::cout << name_ << " is now teaching " << student->getName() << std::endl;
        students_.push_back(std::move(student));
    }

    void showStudents() const {
        std::cout << name_ << " teaches " << students_.size() << " students:" << std::endl;
        for (const auto& student : students_) {
            std::cout << "  - " << student->getName() << std::endl;
        }
    }
};

// =============================================================================
// 演示函数
// =============================================================================

void demonstrateIsA() {
    std::cout << "\n? ========== IS-A 关系演示 (继承) ==========" << std::endl;
    
    // 创建不同的动物对象
    Dog dog("旺财", 3);
    Bird bird("小黄", 1);
    
    // 多态性：通过基类指针调用
    std::vector<std::unique_ptr<Animal>> animals;
    animals.push_back(std::make_unique<Dog>("大黄", 5));
    animals.push_back(std::make_unique<Bird>("小白", 2));
    
    std::cout << "\n多态调用演示：" << std::endl;
    for (const auto& animal : animals) {
        animal->makeSound();  // 多态调用
        animal->move();       // 多态调用
        animal->eat();        // 基类方法
        std::cout << "---" << std::endl;
    }
    
    std::cout << "\n特有方法调用：" << std::endl;
    dog.wagTail();    // 狗特有的方法
    bird.fly();       // 鸟特有的方法
}

void demonstrateHasA() {
    std::cout << "\n? ========== HAS-A 关系演示 (组合) ==========" << std::endl;
    
    // 创建引擎
    Engine v8Engine("V8", 450);
    
    // 创建轮子
    std::vector<Wheel> wheels = {
        Wheel(18, "米其林"),
        Wheel(18, "米其林"),
        Wheel(18, "米其林"),
        Wheel(18, "米其林")
    };
    
    // 创建汽车（组合引擎和轮子）
    Car myCar("奔驰 AMG", v8Engine, wheels);
    
    // 使用汽车
    myCar.showSpecs();
    std::cout << std::endl;
    myCar.start();
    myCar.drive();
    myCar.stop();
}

void demonstrateComplexRelationship() {
    std::cout << "\n??? ========== 复杂关系演示 (IS-A + HAS-A) ==========" << std::endl;
    
    // 创建学生（IS-A Person，HAS-A courses）
    auto student1 = std::make_unique<Student>("张三", 20, "清华大学");
    auto student2 = std::make_unique<Student>("李四", 19, "清华大学");
    
    student1->addCourse("C++程序设计");
    student1->addCourse("数据结构");
    student1->showCourses();
    
    student2->addCourse("C++程序设计");
    student2->addCourse("算法分析");
    student2->showCourses();
    
    std::cout << std::endl;
    
    // 创建教师（IS-A Person，HAS-A students）
    Teacher teacher("王教授", 45, "计算机科学");
    teacher.introduce();
    
    // 注意：这里为了演示，我们需要重新创建学生对象
    // 因为之前的已经被移动了
    teacher.addStudent(std::make_unique<Student>("张三", 20, "清华大学"));
    teacher.addStudent(std::make_unique<Student>("李四", 19, "清华大学"));
    teacher.showStudents();
}

int main() {
    std::cout << "? ========== IS-A vs HAS-A 关系演示 ==========" << std::endl;
    
    demonstrateIsA();
    demonstrateHasA();
    demonstrateComplexRelationship();
    
    std::cout << "\n? ========== 总结 ==========" << std::endl;
    std::cout << "IS-A 关系 (继承)：" << std::endl;
    std::cout << "  - Dog IS-A Animal（狗是一种动物）" << std::endl;
    std::cout << "  - Student IS-A Person（学生是一种人）" << std::endl;
    std::cout << "  - 支持多态性，可以用基类指针指向派生类对象" << std::endl;
    
    std::cout << "\nHAS-A 关系 (组合)：" << std::endl;
    std::cout << "  - Car HAS-A Engine（汽车有一个引擎）" << std::endl;
    std::cout << "  - Student HAS-A courses（学生有多门课程）" << std::endl;
    std::cout << "  - 通过包含其他对象来实现功能" << std::endl;
    
    return 0;
}
