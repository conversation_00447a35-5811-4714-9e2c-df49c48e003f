#include <iostream>
#include <string>
#include <vector>
#include <cstring>

class ThisPointerDemo {
private:
    int value_;
    std::string name_;

public:
    ThisPointerDemo(int val, const std::string& name) 
        : value_(val), name_(name) {}

    // 成员函数的本质：编译器会自动添加this参数
    void setValue(int val) {
        value_ = val;          // 等价于 this->value_ = val
        this->value_ = val;    // 显式使用this指针（可选）
        
        std::cout << "对象地址: " << this << std::endl;
        std::cout << "设置值为: " << value_ << std::endl;
    }

    // 返回this指针的引用，支持链式调用
    ThisPointerDemo& setName(const std::string& name) {
        this->name_ = name;
        return *this;  // 返回当前对象的引用
    }

    ThisPointerDemo& setValue2(int val) {
        value_ = val;
        return *this;  // 返回*this支持链式调用
    }

    // 处理参数名与成员变量名冲突
    void setValueWithConflict(int value_) {
        this->value_ = value_;  // ✅ 正确：明确指定成员变量
    }

    // const成员函数中的this指针
    void printInfo() const {
        std::cout << "对象信息[地址:" << this << "]: ";
        std::cout << "name=" << name_ << ", value=" << value_ << std::endl;
    }

    // 静态成员函数没有this指针
    static void staticFunction() {
        std::cout << "静态函数中没有this指针" << std::endl;
    }

    void demonstrateThis() {
        std::cout << "\n=== this指针演示 ===" << std::endl;
        std::cout << "当前对象地址: " << this << std::endl;
        std::cout << "成员变量地址:" << std::endl;
        std::cout << "  &value_: " << &value_ << std::endl;
        std::cout << "  &name_:  " << &name_ << std::endl;
        std::cout << "通过this访问:" << std::endl;
        std::cout << "  &(this->value_): " << &(this->value_) << std::endl;
        std::cout << "  &(this->name_):  " << &(this->name_) << std::endl;
    }
};

void demonstrateThisPointer() {
    std::cout << "=== this指针工作机制演示 ===" << std::endl;

    // 创建对象
    ThisPointerDemo obj1(10, "对象1");
    ThisPointerDemo obj2(20, "对象2");

    std::cout << "\n1. 不同对象的this指针不同:" << std::endl;
    std::cout << "obj1地址: " << &obj1 << std::endl;
    obj1.setValue(100);  // this指向obj1
    
    std::cout << "obj2地址: " << &obj2 << std::endl;
    obj2.setValue(200);  // this指向obj2

    std::cout << "\n2. 链式调用演示:" << std::endl;
    obj1.setName("新名字").setValue2(999);  // 链式调用
    obj1.printInfo();

    std::cout << "\n3. 参数名冲突处理:" << std::endl;
    obj1.setValueWithConflict(888);
    obj1.printInfo();

    std::cout << "\n4. this指针详细信息:" << std::endl;
    obj1.demonstrateThis();
}

class ChainableBuilder {
private:
    std::string name_;
    int age_;
    std::string email_;

public:
    ChainableBuilder() : age_(0) {}

    // 返回*this支持链式调用
    ChainableBuilder& setName(const std::string& name) {
        name_ = name;
        return *this;  // 关键：返回当前对象的引用
    }

    ChainableBuilder& setAge(int age) {
        age_ = age;
        return *this;
    }

    ChainableBuilder& setEmail(const std::string& email) {
        email_ = email;
        return *this;
    }

    void show() const {
        std::cout << "Name: " << name_ << ", Age: " << age_ 
                  << ", Email: " << email_ << std::endl;
    }
};

void demonstrateChaining() {
    std::cout << "\n=== 链式调用演示 ===" << std::endl;
    ChainableBuilder builder;
    
    // 链式调用：每个函数都返回*this
    builder.setName("张三")
           .setAge(25)
           .setEmail("<EMAIL>");
    
    builder.show();
}

int main() {
    demonstrateThisPointer();
    demonstrateChaining();
    return 0;
}
