#include <iostream>
#include <string>
#include <utility>  // for std::move

// 完整的六大成员函数实现
class Person {
private:
    std::string name_;
    int age_;

public:
    // 1. 默认构造函数
    Person() : name_("Unknown"), age_(0) {
        std::cout << "默认构造函数被调用" << std::endl;
    }

    // 参数化构造函数
    Person(const std::string& name, int age) : name_(name), age_(age) {
        std::cout << "参数构造函数被调用: " << name_ << ", " << age_ << std::endl;
    }

    // 2. 析构函数
    ~Person() {
        std::cout << "析构函数被调用: " << name_ << std::endl;
    }

    // 3. 拷贝构造函数
    Person(const Person& other) : name_(other.name_), age_(other.age_) {
        std::cout << "拷贝构造函数被调用: " << name_ << std::endl;
    }

    // 4. 拷贝赋值运算符 ✅ 修复版本
    Person& operator=(const Person& other) {
        std::cout << "拷贝赋值运算符被调用: " << other.name_ << std::endl;
        
        // 自赋值检查
        if (this == &other) {
            std::cout << "检测到自赋值，直接返回" << std::endl;
            return *this;
        }

        // 赋值操作
        name_ = other.name_;
        age_ = other.age_;

        return *this;  // ✅ 关键：必须返回*this
    }

    // 5. 移动构造函数 (C++11)
    Person(Person&& other) noexcept : name_(std::move(other.name_)), age_(other.age_) {
        std::cout << "移动构造函数被调用: " << name_ << std::endl;
        
        // 将源对象置为安全状态
        other.age_ = 0;  // 对于int，这不是必须的，但是好习惯
    }

    // 6. 移动赋值运算符 (C++11)
    Person& operator=(Person&& other) noexcept {
        std::cout << "移动赋值运算符被调用: " << other.name_ << std::endl;
        
        // 自移动检查
        if (this == &other) {
            return *this;
        }

        // 移动资源
        name_ = std::move(other.name_);
        age_ = other.age_;

        // 将源对象置为安全状态
        other.age_ = 0;

        return *this;
    }

    // 辅助函数：用于测试和显示
    void print() const {
        std::cout << "Person: " << name_ << ", Age: " << age_ << std::endl;
    }

    const std::string& getName() const { return name_; }
    int getAge() const { return age_; }
};

// 测试函数：演示各种调用时机
Person createPerson(const std::string& name, int age) {
    return Person(name, age);  // 可能触发移动构造
}

void processPerson(Person p) {  // 按值传递，触发拷贝构造
    std::cout << "处理: ";
    p.print();
}

int main() {
    std::cout << "=== 六大成员函数演示 ===" << std::endl;

    // 1. 默认构造
    std::cout << "\n1. 默认构造:" << std::endl;
    Person p1;
    p1.print();

    // 2. 参数构造
    std::cout << "\n2. 参数构造:" << std::endl;
    Person p2("张三", 25);
    p2.print();

    // 3. 拷贝构造
    std::cout << "\n3. 拷贝构造:" << std::endl;
    Person p3 = p2;  // 调用拷贝构造函数
    p3.print();

    // 4. 拷贝赋值
    std::cout << "\n4. 拷贝赋值:" << std::endl;
    p1 = p2;  // 调用拷贝赋值运算符
    p1.print();

    // 5. 移动构造
    std::cout << "\n5. 移动构造:" << std::endl;
    Person p4 = createPerson("李四", 30);  // 可能调用移动构造
    p4.print();

    // 6. 移动赋值
    std::cout << "\n6. 移动赋值:" << std::endl;
    p1 = std::move(p4);  // 显式调用移动赋值
    p1.print();
    std::cout << "移动后的p4: ";
    p4.print();  // p4现在应该是空状态

    // 7. 自赋值测试
    std::cout << "\n7. 自赋值测试:" << std::endl;
    p1 = p1;  // 应该被自赋值检查拦截

    // 8. 函数参数传递
    std::cout << "\n8. 函数参数传递:" << std::endl;
    processPerson(p2);  // 按值传递，触发拷贝构造

    std::cout << "\n=== 程序结束，对象析构 ===" << std::endl;
    return 0;
}
