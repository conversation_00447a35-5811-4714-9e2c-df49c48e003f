#include <iostream>

// 真正的经典解法：利用数组构造函数
class FactorialSolution {
public:
    static int result;
    static int current;
    
    // 构造函数执行一次乘法运算
    FactorialSolution() {
        result *= current;
        current--;
    }
    
    static int factorial(int n) {
        result = 1;
        current = n;
        
        // 🔥 这就是真正的解法！创建n个对象的数组
        // 每个对象构造时都会调用构造函数执行一次乘法
        FactorialSolution factorialArray[n];
        
        return result;
    }
};

int FactorialSolution::result = 1;
int FactorialSolution::current = 0;

// 为了演示过程，我们创建一个带调试信息的版本
class FactorialDebug {
public:
    static int result;
    static int current;
    static int step_count;
    
    FactorialDebug() {
        step_count++;
        std::cout << "第" << step_count << "次构造: " 
                  << result << " * " << current << " = ";
        result *= current;
        current--;
        std::cout << result << ", current变为: " << current << std::endl;
    }
    
    static int factorial(int n) {
        result = 1;
        current = n;
        step_count = 0;
        
        std::cout << "开始计算 " << n << "!" << std::endl;
        std::cout << "初始状态: result=" << result << ", current=" << current << std::endl;
        
        // 创建n个对象的数组，每个构造函数都会执行
        FactorialDebug debugArray[n];
        
        std::cout << "计算完成: " << n << "! = " << result << std::endl;
        return result;
    }
};

int FactorialDebug::result = 1;
int FactorialDebug::current = 0;
int FactorialDebug::step_count = 0;

void demonstrateRealSolution() {
    std::cout << "=== 真正的经典解法：数组构造函数 ===" << std::endl;
    
    // 测试不同的n值
    for (int n = 1; n <= 6; n++) {
        int result = FactorialSolution::factorial(n);
        std::cout << n << "! = " << result << std::endl;
    }
}

void demonstrateWithDebug() {
    std::cout << "\n=== 详细执行过程演示 ===" << std::endl;
    
    // 演示5!的计算过程
    FactorialDebug::factorial(5);
}

void explainTheRealTrick() {
    std::cout << "\n=== 真正技巧的核心原理 ===" << std::endl;
    
    std::cout << "1. 核心代码：" << std::endl;
    std::cout << "   FactorialSolution factorialArray[n];" << std::endl;
    
    std::cout << "\n2. 工作原理：" << std::endl;
    std::cout << "   - 创建包含n个对象的数组" << std::endl;
    std::cout << "   - 每个对象构造时调用构造函数" << std::endl;
    std::cout << "   - 构造函数执行一次乘法运算" << std::endl;
    std::cout << "   - n次构造正好完成n!的计算" << std::endl;
    
    std::cout << "\n3. 为什么这么巧妙：" << std::endl;
    std::cout << "   - 无任何条件判断（连三目运算符都没有）" << std::endl;
    std::cout << "   - 无任何循环结构" << std::endl;
    std::cout << "   - 利用数组大小直接控制执行次数" << std::endl;
    std::cout << "   - 代码极其简洁，只有一行关键代码" << std::endl;
    
    std::cout << "\n4. 与我之前错误理解的对比：" << std::endl;
    std::cout << "   ❌ 我的错误：固定次数的函数调用" << std::endl;
    std::cout << "   ❌ 我的错误：使用三目运算符进行条件判断" << std::endl;
    std::cout << "   ✅ 正确解法：动态大小的数组构造" << std::endl;
    std::cout << "   ✅ 正确解法：完全无条件判断" << std::endl;
}

// 展示这个技巧的变体应用
class FibonacciArray {
public:
    static int a, b, temp;
    
    FibonacciArray() {
        temp = a + b;
        a = b;
        b = temp;
    }
    
    static int fibonacci(int n) {
        a = 0;
        b = 1;
        
        // 如果n=0，数组大小为0，不执行任何构造
        // 如果n>0，执行n次斐波那契步骤
        FibonacciArray fibArray[n];
        
        return a;
    }
};

int FibonacciArray::a = 0;
int FibonacciArray::b = 1;
int FibonacciArray::temp = 0;

int main() {
    demonstrateRealSolution();
    demonstrateWithDebug();
    explainTheRealTrick();
    
    std::cout << "\n=== 技巧的扩展应用：斐波那契数列 ===" << std::endl;
    for (int i = 0; i <= 10; i++) {
        std::cout << "fib(" << i << ") = " << FibonacciArray::fibonacci(i) << std::endl;
    }
    
    return 0;
}
