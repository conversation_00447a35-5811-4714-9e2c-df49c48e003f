# C++ 内存管理详解：从入门到专业实践完整指南

> **权威技术指南**：本指南将带您系统掌握C++内存管理的完整知识体系，从基础概念到高级技术，从传统方法到现代实践，构建扎实的内存管理基础。

---

## 📚 文档导航

| 章节 | 内容概要 | 学习目标 |
|------|----------|----------|
| **Part 0** | 快速入门 | 30分钟掌握核心概念 |
| **Part 1** | 内存模型基础 | 理解栈、堆、内存布局 |
| **Part 2** | 传统内存管理 | 掌握new/delete和陷阱 |
| **Part 3** | 智能指针技术 | 掌握现代C++内存管理 |
| **Part 4** | 容器与内存 | 理解STL容器内存机制 |
| **Part 5** | 移动语义与性能 | 掌握高效内存操作 |
| **Part 6** | 内存池与自定义分配 | 高级内存管理技术 |
| **Part 7** | 调试与工具 | 内存问题诊断和解决 |
| **附录** | 最佳实践与面试 | 实战指导和问题解答 |

---

## Part 0: 快速入门——30分钟掌握内存管理核心

> **学习目标**：通过生动的例子，快速理解C++内存管理的核心思想，为深入学习打下基础。

### 0.1 内存管理：程序员的生存技能

#### 核心概念理解

**内存管理的本质**：程序需要空间存储数据，使用完毕后必须正确释放，避免浪费和错误。

```mermaid
graph TD
    A["🏠 程序内存空间"] --> B["📦 申请内存<br/>存储数据"]
    B --> C["💻 使用内存<br/>处理数据"]
    C --> D["🗑️ 释放内存<br/>回收空间"]
    D --> E["✅ 内存可重复使用"]
    
    F["❌ 忘记释放"] --> G["💥 内存泄漏<br/>程序越来越慢"]
    F --> H["⚠️ 重复释放<br/>程序崩溃"]
    F --> I["🔥 访问已释放内存<br/>未定义行为"]
    
    classDef good fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef bad fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class A,B,C,D,E good
    class F,G,H,I bad
```

#### 生活化比喻：内存管理就像借书

```cpp
#include <iostream>
#include <memory>

// 传统方式：手动借书还书（容易出错）
void 传统借书方式() {
    // 去图书馆借书
    Book* book = new Book("C++程序设计");  // 借书
    
    // 看书
    book->read();
    
    // ❌ 容易忘记还书！
    delete book;  // 还书（经常忘记）
}

// 现代方式：智能图书管理系统（自动归还）
void 智能借书方式() {
    // 智能借书系统自动管理
    auto book = std::make_unique<Book>("C++程序设计");
    
    // 看书
    book->read();
    
    // ✅ 自动还书！不需要手动操作
    // 离开作用域时自动调用析构函数归还
}
```

### 0.2 内存的两大区域：栈与堆

#### 0.2.1 栈内存：速度快的临时住所

```cpp
void 栈内存演示() {
    // 栈内存：局部变量，自动管理
    int 年龄 = 25;                    // 在栈上分配
    char 姓名[20] = "张三";           // 在栈上分配
    std::array<int, 10> 分数;        // 在栈上分配
    
    if (年龄 > 18) {
        double 工资 = 5000.0;         // 在栈上分配
        std::cout << "成年人工资：" << 工资 << std::endl;
        // 工资变量在这里自动销毁
    }
    
    // 年龄、姓名、分数在这里自动销毁
}
```

#### 0.2.2 堆内存：空间大的永久住所

```cpp
void 堆内存演示() {
    // 堆内存：动态分配，手动管理（传统方式，不推荐）
    int* 大量数据 = new int[1000000];    // 在堆上分配大空间
    // ... 使用数据 ...
    delete[] 大量数据;                    // 必须手动释放
    
    // 现代方式：智能指针自动管理堆内存
    auto 智能大量数据 = std::make_unique<int[]>(1000000);
    // ... 使用数据 ...
    // 自动释放，无需手动delete
}
```

#### 栈vs堆对比表

| 特性 | 栈内存 | 堆内存 |
|------|--------|--------|
| **分配速度** | ⚡ 超快（毫秒级） | 🐌 较慢（可能毫秒级到秒级） |
| **空间大小** | 📏 有限（通常1-8MB） | � 巨大（受系统内存限制） |
| **管理方式** | 🤖 自动管理 | 👷 需要手动管理 |
| **访问速度** | 🚀 极快（缓存友好） | 🔍 较快（可能缓存不友好） |
| **适用场景** | 🏠 小对象、临时变量 | 🏗️ 大对象、长期存储 |

### 0.3 智能指针：现代C++的救星

#### 核心理念：RAII（Resource Acquisition Is Initialization）

```mermaid
graph LR
    A["🎯 获取资源"] --> B["🔄 绑定到对象"]
    B --> C["💻 使用资源"]
    C --> D["🗑️ 对象销毁时自动释放资源"]
    
    classDef raii fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    class A,B,C,D raii
```

#### 三种智能指针的生活化理解

```cpp
#include <memory>
#include <iostream>

class 重要文件 {
public:
    重要文件(const std::string& name) : name_(name) {
        std::cout << "创建文件：" << name_ << std::endl;
    }
    
    ~重要文件() {
        std::cout << "销毁文件：" << name_ << std::endl;
    }
    
    void 编辑() {
        std::cout << "正在编辑：" << name_ << std::endl;
    }

private:
    std::string name_;
};

void 智能指针演示() {
    std::cout << "=== unique_ptr：独占所有权 ===" << std::endl;
    {
        // 🔑 unique_ptr：我的东西我做主
        auto 我的文件 = std::make_unique<重要文件>("个人日记.txt");
        我的文件->编辑();
        // 离开作用域自动销毁
    }
    
    std::cout << "\n=== shared_ptr：共享所有权 ===" << std::endl;
    {
        // 🏠 shared_ptr：大家一起用，最后一个关灯
        auto 共享文件 = std::make_shared<重要文件>("团队项目.txt");
        
        {
            auto 另一个引用 = 共享文件;  // 引用计数：2
            另一个引用->编辑();
            // 内层作用域结束，引用计数：1
        }
        
        共享文件->编辑();  // 引用计数仍为1
        // 外层作用域结束，引用计数：0，自动销毁
    }
    
    std::cout << "\n=== weak_ptr：观察者模式 ===" << std::endl;
    {
        // 👀 weak_ptr：看得见摸不着
        std::weak_ptr<重要文件> 观察者;
        
        {
            auto 原始文件 = std::make_shared<重要文件>("临时文件.txt");
            观察者 = 原始文件;  // 观察但不拥有
            
            if (auto 临时访问 = 观察者.lock()) {  // 尝试获取访问权
                临时访问->编辑();
            }
            // 原始文件离开作用域，被销毁
        }
        
        if (观察者.expired()) {
            std::cout << "观察的文件已经被销毁了" << std::endl;
        }
    }
}
```

### 0.4 常见内存错误与现代解决方案

#### 经典错误对比

| 传统代码（危险） | 现代代码（安全） | 错误类型 |
|------------------|------------------|----------|
| `int* p = new int; /*忘记delete*/` | `auto p = make_unique<int>();` | 内存泄漏 |
| `delete p; *p = 5;` | `p.reset(); // p自动变为nullptr` | 悬空指针 |
| `delete p; delete p;` | `// 智能指针防止重复删除` | 重复删除 |
| `int arr[n];` | `std::vector<int> arr(n);` | 栈溢出 |
| `int* arr = new int[10]; arr[20] = 1;` | `std::array<int,10> arr; arr.at(20);` | 数组越界 |

#### 新手生存指南

```cpp
// 🎯 新手黄金法则
void 新手安全编程() {
    // ✅ 规则1：能用栈就用栈
    int 简单变量 = 42;
    std::array<int, 100> 小数组;
    
    // ✅ 规则2：必须用堆时用智能指针
    auto 大对象 = std::make_unique<LargeObject>();
    auto 共享对象 = std::make_shared<SharedResource>();
    
    // ✅ 规则3：容器优于原始数组
    std::vector<int> 动态数组;
    std::string 字符串;  // 内部自动管理内存
    
    // ✅ 规则4：看到new/delete就重构
    // 99.9%的new/delete都可以用智能指针替代
}
```

### 0.5 快速入门总结

> **🎯 核心要点**：
> - **内存分类**：栈内存（快、小、自动）vs 堆内存（慢、大、手动）
> - **RAII原则**：资源获取即初始化，自动管理生命周期
> - **智能指针**：unique_ptr（独占）、shared_ptr（共享）、weak_ptr（观察）
> - **黄金法则**：栈优先，堆用智能指针，容器胜过数组

**接下来的学习路径**：
1. **Part 1**：深入理解内存模型和布局
2. **Part 2**：掌握传统内存管理技术
3. **Part 3**：精通智能指针的高级用法
4. **Part 4**：理解容器的内存管理机制
5. **Part 5**：学习移动语义和性能优化

---

*准备好了吗？让我们开始深入探索C++内存管理的技术世界！* 🚀

---

### 🚀 **分层学习路径**

#### 🌱 **新手路径：从零开始的安全之旅**
```
推荐学习顺序：快速参考卡 → 第0章 → 第1章 → 第7章(现代实践) → 练习题
⏰ 建议时间：4-6周，每天1-2小时
🎯 核心目标：建立安全编程习惯，避免常见陷阱
```

**第1-2周：基础认知**
- [ ] 📖 熟读快速参考卡，理解"黄金法则"
- [ ] 🏠 完成第0章所有示例，建立内存模型
- [ ] 🛡️ 练习：用容器替代原始数组
- [ ] ✅ 里程碑：能区分栈内存vs堆内存

**第3-4周：智能指针入门**
- [ ] 🔑 掌握`make_unique`和`make_shared`基本用法
- [ ] 📚 理解RAII原则，知道为什么有用
- [ ] 🔨 练习：改写所有使用`new/delete`的代码
- [ ] ✅ 里程碑：能写出不泄漏内存的程序

**第5-6周：巩固提升**
- [ ] 🎯 完成第1、7章的练习题
- [ ] 🔍 学会使用AddressSanitizer等工具
- [ ] 📝 总结常见错误模式和解决方案
- [ ] ✅ 最终目标：养成现代C++编程习惯

#### 🔄 **转型路径：从C到现代C++**
```
推荐学习顺序：快速参考卡 → 第1章 → 第7章 → 第2-6章(按需) → 重构实践
⏰ 建议时间：3-4周，每天1-2小时
🎯 核心目标：转换思维模式，拥抱现代工具
```

**第1周：思维转换**
- [ ] 💡 对比C风格vs现代C++风格（快速参考卡）
- [ ] 🏗️ 理解RAII概念，这是关键差异
- [ ] 🔄 练习：将C代码改写为C++11/14风格
- [ ] ✅ 里程碑：认识到智能指针的必要性

**第2-3周：工具掌握**
- [ ] 🛠️ 熟练使用智能指针替代手动管理
- [ ] 📦 掌握STL容器替代C数组
- [ ] ⚡ 学习移动语义，理解性能优化
- [ ] ✅ 里程碑：能流畅使用现代C++特性

**第4周：实战应用**
- [ ] 🔧 重构一个现有的C项目到C++
- [ ] 📊 对比重构前后的代码质量
- [ ] 🎯 完成相关章节的高级练习
- [ ] ✅ 最终目标：完全切换到现代C++思维

#### 🎯 **初学者路径：从入门到熟练**
```
推荐学习顺序：第1章 → 第7章 → 第2-3章 → 实践项目 → 深入学习
⏰ 建议时间：2-3周，每天1-2小时
🎯 核心目标：掌握核心技能，写出高质量代码
```

**第1周：核心概念**
- [ ] 📚 深入理解内存布局和管理原理
- [ ] 🔑 熟练掌握各种智能指针的使用场景
- [ ] 🎯 完成第1、7章的所有练习题
- [ ] ✅ 里程碑：理论基础扎实

**第2周：实践应用**
- [ ] 🏗️ 学习容器和迭代器的内存管理
- [ ] ⚡ 掌握移动语义和完美转发
- [ ] 🔨 完成一个中等规模的练习项目
- [ ] ✅ 里程碑：能独立设计内存安全的程序

**第3周：深入提升**
- [ ] 🧠 学习内存池和对象池技术
- [ ] 🔍 使用性能分析工具优化程序
- [ ] 📝 总结最佳实践和常见陷阱
- [ ] ✅ 最终目标：成为内存管理熟练工

#### ⚡ **进阶路径：深度理解与优化**
```
推荐学习顺序：第4-6章 → 第8-9章 → 性能分析 → 源码研读
⏰ 建议时间：1-2周，每天2-3小时
🎯 核心目标：深度理解原理，具备优化能力
```

**重点关注领域：**
- [ ] 🏭 内存池设计模式和实现技巧
- [ ] ⚡ 内存对齐和缓存友好的数据结构
- [ ] 🔧 自定义分配器的设计和使用
- [ ] 🎯 并发环境下的内存管理策略
- [ ] 📊 内存性能分析和优化技术

#### 💼 **面试路径：快速掌握要点**
```
推荐学习顺序：快速参考卡 → 终极备忘单 → 高频问题 → 模拟面试
⏰ 建议时间：1周，每天2-3小时
🎯 核心目标：掌握面试高频考点
```

**每日计划：**
- **Day 1-2**: 快速参考卡 + 智能指针核心概念
- **Day 3-4**: 内存布局 + 常见错误模式
- **Day 5-6**: 现代最佳实践 + 性能考虑
- **Day 7**: 模拟面试 + 查漏补缺

#### 🔧 **维护者路径：理解遗留代码**
```
推荐学习顺序：第2-6章(传统方法) → 第7章 → 重构策略 → 工具使用
⏰ 建议时间：2-3周，每天1-2小时
🎯 核心目标：理解老代码，安全地现代化
```

**学习重点：**
- [ ] 🕰️ 深入理解传统new/delete管理方式
- [ ] 🔍 掌握内存泄漏检测和调试技术
- [ ] 🔄 学习渐进式重构策略
- [ ] 🛡️ 了解向后兼容的现代化方案

---

### 🎯 **通用学习建议**

#### 📚 **所有读者都应该掌握的核心内容**
1. **黄金法则**：优先使用栈内存和智能指针
2. **RAII原则**：资源获取即初始化
3. **三五零法则**：正确实现拷贝/移动语义
4. **工具使用**：AddressSanitizer、Valgrind等
5. **最佳实践**：现代C++风格指南

#### 🎨 **学习方法建议**
- **💻 实践为主**：每个概念都要写代码验证
- **🔄 对比学习**：传统方法vs现代方法
- **🔍 工具辅助**：善用静态分析和动态检测工具  
- **📖 源码阅读**：研究标准库和优秀开源项目
- **👥 交流讨论**：加入C++社区，分享经验

#### ⚠️ **学习过程中的重要提醒**
- **先安全，后性能**：没掌握基础前不要过度优化
- **工具优于技巧**：现代C++提供的工具比手工技巧更可靠
- **理解原理**：知其然更要知其所以然
- **持续实践**：内存管理需要在实际项目中不断磨练

---

## 🚀 **快速参考卡** - 一页纸掌握核心概念

### 📋 内存管理速查表

#### 🎯 **黄金法则（新手必记）**
```cpp
// ❌ 永远不要这样做
int* ptr = new int(42);
delete ptr;  // 容易忘记，容易出错

// ✅ 现代C++正确方式
auto ptr = std::make_unique<int>(42);  // 自动管理，永不泄漏
```

#### 📊 **智能指针选择表**
| 场景 | 使用什么 | 代码示例 | 记忆口诀 |
|------|---------|----------|----------|
| **单个拥有者** | `unique_ptr` | `auto ptr = make_unique<int>(42);` | 🔑 "我的东西我做主" |
| **多个拥有者** | `shared_ptr` | `auto ptr = make_shared<int>(42);` | 🏠 "大家一起用，最后一个关灯" |
| **只看不拥有** | `weak_ptr` | `weak_ptr<int> weak = shared;` | 👀 "看得见摸不着" |
| **临时使用** | 原始指针 | `int* raw = ptr.get();` | 🔍 "借来用用，不负责管理" |

#### 🏭 **智能指针创建函数速查**
| 创建方式 | 使用场景 | 优势 | 注意事项 |
|---------|----------|------|----------|
| `make_unique<T>(args)` | 创建unique_ptr | ✅ 异常安全<br/>✅ 代码简洁<br/>✅ 零开销 | ❌ 不支持自定义删除器 |
| `make_unique<T[]>(size)` | 创建unique_ptr数组 | ✅ 自动调用delete[] | ⚠️ 数组元素使用默认构造 |
| `make_shared<T>(args)` | 创建shared_ptr | ✅ 单次内存分配<br/>✅ 缓存友好<br/>✅ 性能更好 | ❌ 不支持自定义删除器<br/>⚠️ 内存释放可能延迟 |
| `shared_ptr<T>(new T, deleter)` | 需要自定义删除器 | ✅ 支持自定义删除器 | ❌ 两次内存分配<br/>❌ 异常安全风险 |

#### ⚡ **创建函数最佳实践**
```cpp
// ✅ 推荐的创建方式
auto unique_obj = std::make_unique<MyClass>(args);     // 独占所有权
auto shared_obj = std::make_shared<MyClass>(args);     // 共享所有权
auto unique_arr = std::make_unique<int[]>(100);        // 独占数组
auto shared_vec = std::make_shared<std::vector<int>>(100); // 共享容器

// ❌ 避免的创建方式
std::unique_ptr<MyClass> bad1(new MyClass(args));      // 不安全
std::shared_ptr<MyClass> bad2(new MyClass(args));      // 低效

// ⚠️ 特殊情况才使用
auto custom_deleter = [](MyClass* p) { /* 自定义清理 */ delete p; };
std::shared_ptr<MyClass> special(new MyClass(args), custom_deleter);
```

#### 🚨 **危险信号识别**
```cpp
// 🚨 看到这些立即警觉！
new Type();          // ❌ 没有对应的delete
delete ptr;          // ❌ 可能忘记，可能重复
ptr[index];          // ❌ 可能越界
return &local_var;   // ❌ 返回栈变量地址
```

#### ⚡ **内存区域速记**
```
🏢 程序内存大楼：
┌─────────────────┐ ← 高地址
│  栈区 (Stack)    │   🔥 超快，自动管理，空间小
│  ↓ 向下增长      │   📋 局部变量住这里
├─────────────────┤
│  内存映射区      │   📚 动态库、文件映射
├─────────────────┤
│  堆区 (Heap)     │   🏗️ 手动管理，空间大
│  ↑ 向上增长      │   💰 new/delete的地盘
├─────────────────┤
│  数据区          │   🏠 全局变量(有初值)
├─────────────────┤
│  BSS区           │   🚧 全局变量(无初值)
├─────────────────┤
│  代码区          │   📖 程序指令，只读
└─────────────────┘ ← 低地址
```

#### 🎯 **新手生存指南**
1. **能用栈就用栈**：`int x = 42;` 比 `int* x = new int(42);` 好一万倍
2. **必须用堆时用智能指针**：`make_unique` 和 `make_shared` 是你的好朋友
3. **看到裸露的new/delete就重构**：99%的情况都有更好的替代方案
4. **容器优先**：`std::vector` 比 `int*` 数组安全太多
5. **有疑问就用工具**：AddressSanitizer 能救你的命

#### 🔧 **智能指针创建黄金法则**
```cpp
// 🥇 黄金法则：永远优先使用make函数
auto unique_ptr = std::make_unique<MyClass>(args);    // 独占所有权
auto shared_ptr = std::make_shared<MyClass>(args);    // 共享所有权
auto unique_array = std::make_unique<int[]>(size);    // 独占数组

// 🚫 永远不要这样做
MyClass* raw_ptr = new MyClass(args);                 // 容易泄漏
std::unique_ptr<MyClass> bad_unique(new MyClass(args)); // 不安全
std::shared_ptr<MyClass> bad_shared(new MyClass(args)); // 低效

// 💡 记忆口诀
// make_unique：我独占，我负责
// make_shared：大家共享，引用计数管理
// 原始new：99%的情况都是错误选择
```

---

## Part 1: 内存模型基础——理解程序的居住环境

> **学习目标**：深入理解C++程序的内存布局，掌握不同内存区域的特点和使用方法，建立正确的内存管理思维模型。

### 1.1 程序内存布局全景图

#### 1.1.1 内存布局的完整结构

```mermaid
graph TD
    subgraph "🏢 程序内存大楼"
        A["🔝 高地址"]
        B["📚 栈区 (Stack)<br/>局部变量、函数参数<br/>向下增长 ⬇️"]
        C["📊 共享库映射区<br/>动态库、文件映射"]
        D["🏗️ 堆区 (Heap)<br/>动态分配内存<br/>向上增长 ⬆️"]
        E["📋 数据区 (Data)<br/>初始化的全局变量"]
        F["🚧 BSS区<br/>未初始化的全局变量"]
        G["📖 代码区 (Text)<br/>程序指令，只读"]
        H["🔚 低地址"]
    end
    
    A --> B --> C --> D --> E --> F --> G --> H
    
    classDef stack fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef heap fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef data fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef code fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class B stack
    class D heap
    class E,F data
    class G code
```

#### 实际代码演示内存布局

```cpp
#include <iostream>
#include <memory>

// 全局变量区域
int 全局初始化变量 = 42;        // 存储在数据区(Data)
int 全局未初始化变量;           // 存储在BSS区
static int 静态全局变量 = 100;   // 存储在数据区(Data)

void 内存布局演示() {
    // 栈区变量
    int 局部变量 = 10;
    char 局部数组[100];
    
    // 堆区变量
    int* 堆变量 = new int(20);
    auto 智能指针变量 = std::make_unique<int>(30);
    
    // 静态局部变量
    static int 静态局部变量 = 200;  // 存储在数据区，但作用域限制在函数内
    
    // 打印各变量的地址
    std::cout << "=== 内存地址分析 ===" << std::endl;
    std::cout << "代码区地址（函数）：" << (void*)&内存布局演示 << std::endl;
    std::cout << "全局变量地址：" << &全局初始化变量 << std::endl;
    std::cout << "静态变量地址：" << &静态全局变量 << std::endl;
    std::cout << "静态局部变量地址：" << &静态局部变量 << std::endl;
    std::cout << "栈变量地址：" << &局部变量 << std::endl;
    std::cout << "栈数组地址：" << (void*)局部数组 << std::endl;
    std::cout << "堆变量地址：" << 堆变量 << std::endl;
    std::cout << "智能指针管理的地址：" << 智能指针变量.get() << std::endl;
    
    // 清理堆内存
    delete 堆变量;
}
```

#### 1.1.2 各内存区域详细特性

##### 栈区（Stack）特性

```cpp
class 栈内存特性演示 {
public:
    void 演示栈的特点() {
        std::cout << "=== 栈内存特性演示 ===" << std::endl;
        
        // 1. 自动管理生命周期
        {
            int 作用域变量 = 100;
            std::cout << "变量创建：" << 作用域变量 << std::endl;
            // 离开作用域自动销毁
        }
        // 这里 作用域变量 已经不存在了
        
        // 2. 分配速度极快
        auto 开始时间 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 1000000; ++i) {
            int 快速分配 = i;  // 栈分配，几乎零开销
        }
        auto 结束时间 = std::chrono::high_resolution_clock::now();
        std::cout << "栈分配100万次耗时：" 
                  << std::chrono::duration_cast<std::chrono::microseconds>(结束时间 - 开始时间).count() 
                  << " 微秒" << std::endl;
        
        // 3. 大小限制演示（注意：这可能导致栈溢出）
        // int 巨大数组[10000000];  // ❌ 可能栈溢出
        
        // 正确做法：大数组放堆上
        auto 堆上的大数组 = std::make_unique<std::array<int, 10000000>>();
    }
    
    void 递归演示栈增长(int 深度 = 0) {
        char 栈帧标记[1024];  // 每次递归占用1KB栈空间
        std::cout << "递归深度：" << 深度 << "，栈地址：" << (void*)栈帧标记 << std::endl;
        
        if (深度 < 5) {
            递归演示栈增长(深度 + 1);
        }
    }
};
```

##### 堆区（Heap）特性

```cpp
class 堆内存特性演示 {
public:
    void 演示堆的特点() {
        std::cout << "=== 堆内存特性演示 ===" << std::endl;
        
        // 1. 大空间分配
        auto 大对象 = std::make_unique<std::array<int, 1000000>>();
        std::cout << "分配了1M整数数组，地址：" << 大对象.get() << std::endl;
        
        // 2. 生命周期灵活控制
        std::vector<std::unique_ptr<int>> 动态容器;
        for (int i = 0; i < 10; ++i) {
            动态容器.push_back(std::make_unique<int>(i * 10));
        }
        
        // 3. 分配性能对比
        auto 开始时间 = std::chrono::high_resolution_clock::now();
        std::vector<std::unique_ptr<int>> 性能测试;
        for (int i = 0; i < 100000; ++i) {
            性能测试.push_back(std::make_unique<int>(i));
        }
        auto 结束时间 = std::chrono::high_resolution_clock::now();
        auto 时间1 = std::chrono::duration_cast<std::chrono::milliseconds>(结束时间 - 开始时间);
        
        // 测试缓存友好的访问
        std::vector<缓存友好> 友好容器(数量);
        for (auto& item : 友好容器) {
            item.内嵌数据[0] = 42;
        }
        
        开始 = std::chrono::high_resolution_clock::now();
        long long 总和2 = 0;
        for (const auto& item : 友好容器) {
            总和2 += item.内嵌数据[0];
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto 时间2 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);
        
        std::cout << "堆分配10万次耗时：" 
                  << std::chrono::duration_cast<std::chrono::milliseconds>(结束时间 - 开始时间).count() 
                  << " 毫秒" << std::endl;
    }
    
    void 内存碎片演示() {
        std::cout << "=== 内存碎片演示 ===" << std::endl;
        
        std::vector<void*> 指针集合;
        
        // 分配大量不同大小的内存块
        for (int i = 0; i < 1000; ++i) {
            size_t 大小 = (i % 10 + 1) * 64;  // 64, 128, ..., 640字节
            void* ptr = malloc(大小);
            指针集合.push_back(ptr);
        }
        
        // 随机释放一半内存，造成碎片
        for (size_t i = 0; i < 指针集合.size(); i += 2) {
            free(指针集合[i]);
            指针集合[i] = nullptr;
        }
        
        std::cout << "已创建内存碎片" << std::endl;
        
        // 清理剩余内存
        for (void* ptr : 指针集合) {
            if (ptr) free(ptr);
        }
    }
};
```

### 1.2 内存对齐与数据结构布局

#### 1.2.1 内存对齐原理

```cpp
#include <iostream>

// 演示内存对齐的影响
struct 对齐前 {
    char c;      // 1字节
    int i;       // 4字节
    char c2;     // 1字节
    double d;    // 8字节
};

struct 对齐后 {
    char c;      // 1字节
    char c2;     // 1字节（紧挨着放）
    int i;       // 4字节
    double d;    // 8字节
};

void 内存对齐演示() {
    std::cout << "=== 内存对齐影响演示 ===" << std::endl;
    std::cout << "对齐前结构体大小：" << sizeof(对齐前) << " 字节" << std::endl;
    std::cout << "对齐后结构体大小：" << sizeof(对齐后) << " 字节" << std::endl;
    
    // 查看具体的内存布局
    对齐前 obj1;
    std::cout << "\n对齐前成员地址：" << std::endl;
    std::cout << "c 地址：" << (void*)&obj1.c << std::endl;
    std::cout << "i 地址：" << (void*)&obj1.i << std::endl;
    std::cout << "c2地址：" << (void*)&obj1.c2 << std::endl;
    std::cout << "d 地址：" << (void*)&obj1.d << std::endl;
    
    对齐后 obj2;
    std::cout << "\n对齐后成员地址：" << std::endl;
    std::cout << "c 地址：" << (void*)&obj2.c << std::endl;
    std::cout << "c2地址：" << (void*)&obj2.c2 << std::endl;
    std::cout << "i 地址：" << (void*)&obj2.i << std::endl;
    std::cout << "d 地址：" << (void*)&obj2.d << std::endl;
}
```

#### 1.2.2 缓存友好的数据结构设计

```cpp
// 缓存不友好的设计
struct 缓存不友好 {
    int* 数据指针;        // 指向堆内存
    std::string 名称;     // 内部可能有堆分配
    std::vector<int> 列表;// 内部有堆分配
};

// 缓存友好的设计
struct 缓存友好 {
    int 内嵌数据[16];     // 直接内嵌，连续内存
    char 固定名称[32];    // 固定大小，避免额外分配
    int 计数;
    // 相关数据紧密排列
};

class 缓存性能对比 {
public:
    void 测试缓存性能() {
        const size_t 数量 = 1000000;
        
        // 测试缓存不友好的访问
        std::vector<缓存不友好> 不友好容器(数量);
        for (auto& item : 不友好容器) {
            item.数据指针 = new int(42);
            item.名称 = "测试";
            item.列表.push_back(1);
        }
        
        auto 开始 = std::chrono::high_resolution_clock::now();
        long long 总和1 = 0;
        for (const auto& item : 不友好容器) {
            总和1 += *item.数据指针;
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto 时间1 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);
        
        // 测试缓存友好的访问
        std::vector<缓存友好> 友好容器(数量);
        for (auto& item : 友好容器) {
            item.内嵌数据[0] = 42;
        }
        
        开始 = std::chrono::high_resolution_clock::now();
        long long 总和2 = 0;
        for (const auto& item : 友好容器) {
            总和2 += item.内嵌数据[0];
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto 时间2 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);
        
        std::cout << "缓存不友好访问时间：" << 时间1.count() << " 微秒" << std::endl;
        std::cout << "缓存友好访问时间：" << 时间2.count() << " 微秒" << std::endl;
        std::cout << "性能提升：" << (double)时间1.count() / 时间2.count() << " 倍" << std::endl;
        
        // 清理内存
        for (auto& item : 不友好容器) {
            delete item.数据指针;
        }
    }
    
    void 内存碎片演示() {
        std::cout << "=== 内存碎片演示 ===" << std::endl;
        
        std::vector<void*> 指针集合;
        
        // 分配大量不同大小的内存块
        for (int i = 0; i < 1000; ++i) {
            size_t 大小 = (i % 20 + 1) * 16;  // 16到320字节
            void* ptr = malloc(大小);
            指针集合.push_back(ptr);
        }
        
        // 释放奇数位置的内存，制造碎片
        for (size_t i = 1; i < 指针集合.size(); i += 2) {
            free(指针集合[i]);
            指针集合[i] = nullptr;
        }
        
        // 尝试分配一个大块内存
        void* 大块内存 = malloc(1024 * 1024);  // 1MB
        if (大块内存) {
            std::cout << "成功分配1MB内存" << std::endl;
            free(大块内存);
        } else {
            std::cout << "内存碎片化导致分配失败" << std::endl;
        }
        
        // 清理内存
        for (void* ptr : 指针集合) {
            if (ptr) free(ptr);
        }
    }
};
```

### 1.3 内存访问模式与性能

#### 1.3.1 顺序访问vs随机访问

```cpp
#include <vector>
#include <random>
#include <algorithm>

class 内存访问模式测试 {
private:
    static const size_t 数据大小 = 10000000;  // 10M个整数
    std::vector<int> 数据;
    std::vector<size_t> 随机索引;

public:
    内存访问模式测试() : 数据(数据大小) {
        // 初始化数据
        for (size_t i = 0; i < 数据大小; ++i) {
            数据[i] = static_cast<int>(i);
            随机索引.push_back(i);
        }
        
        // 打乱索引顺序
        std::random_device rd;
        std::mt19937 gen(rd());
        std::shuffle(随机索引.begin(), 随机索引.end(), gen);
    }
    
    void 测试顺序访问() {
        auto 开始 = std::chrono::high_resolution_clock::now();
        
        long long 总和 = 0;
        for (size_t i = 0; i < 数据大小; ++i) {
            总和 += 数据[i];  // 顺序访问，缓存友好
        }
        
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto 耗时 = std::chrono::duration_cast<std::chrono::milliseconds>(结束 - 开始);
        
        std::cout << "顺序访问耗时：" << 耗时.count() << " 毫秒" << std::endl;
        std::cout << "总和：" << 总和 << std::endl;
    }
    
    void 测试随机访问() {
        auto 开始 = std::chrono::high_resolution_clock::now();
        
        long long 总和 = 0;
        for (size_t i = 0; i < 数据大小; ++i) {
            总和 += 数据[随机索引[i]];  // 随机访问，缓存不友好
        }
        
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto 耗时 = std::chrono::duration_cast<std::chrono::milliseconds>(结束 - 开始);
        
        std::cout << "随机访问耗时：" << 耗时.count() << " 毫秒" << std::endl;
        std::cout << "总和：" << 总和 << std::endl;
    }
};
```

#### 1.3.2 内存局部性原理

```mermaid
graph TD
    A["🎯 程序访问内存"] --> B["🔍 检查CPU缓存"]
    B --> C{"缓存命中？"}
    C -->|是| D["⚡ 极快访问<br/>~1纳秒"]
    C -->|否| E["📋 从内存加载<br/>~100纳秒"]
    E --> F["📦 加载整个缓存行<br/>（通常64字节）"]
    F --> G["💾 更新缓存"]
    G --> D
    
    classDef fast fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef slow fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class D fast
    class E,F,G slow
```

```cpp
// 利用空间局部性优化代码
void 空间局部性优化示例() {
    const int 大小 = 1000;
    int 矩阵[大小][大小];
    
    // ❌ 缓存不友好：按列访问
    auto 开始 = std::chrono::high_resolution_clock::now();
    for (int j = 0; j < 大小; ++j) {
        for (int i = 0; i < 大小; ++i) {
            矩阵[i][j] = i + j;  // 跳跃访问，缓存不友好
        }
    }
    auto 结束 = std::chrono::high_resolution_clock::now();
    auto 时间1 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);
    
    // ✅ 缓存友好：按行访问
    开始 = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < 大小; ++i) {
        for (int j = 0; j < 大小; ++j) {
            矩阵[i][j] = i + j;  // 连续访问，缓存友好
        }
    }
    结束 = std::chrono::high_resolution_clock::now();
    auto 时间2 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);
    
    std::cout << "按列访问时间：" << 时间1.count() << " 微秒" << std::endl;
    std::cout << "按行访问时间：" << 时间2.count() << " 微秒" << std::endl;
    std::cout << "性能提升：" << (double)时间1.count() / 时间2.count() << " 倍" << std::endl;
}
```

### 1.4 Part 1 总结

> **🎯 Part 1 核心要点**：

#### 技术掌握清单

| 技术点 | 掌握程度 | 关键概念 |
|--------|----------|----------|
| **内存布局** | ⭐⭐⭐ | 栈、堆、数据区的特点和用途 |
| **内存对齐** | ⭐⭐ | 提高访问效率，优化结构体设计 |
| **缓存局部性** | ⭐⭐ | 顺序访问优于随机访问 |
| **访问模式** | ⭐⭐ | 理解不同访问模式的性能差异 |

#### 设计原则

1. **栈优先原则**：小对象优先使用栈内存
2. **连续性原则**：相关数据放在一起，提高缓存效率
3. **对齐优化**：合理排列结构体成员，减少内存浪费
4. **局部性原理**：设计算法时考虑内存访问模式

#### 性能优化要点

- ✅ 优先使用栈内存分配小对象
- ✅ 设计缓存友好的数据结构
- ✅ 顺序访问大数据集
- ✅ 合理安排结构体成员顺序
- ❌ 避免不必要的内存对齐填充

**接下来**：Part 2将深入探讨传统的new/delete内存管理方式，理解其机制和问题。

---

## Part 2: 传统内存管理——new/delete的深度剖析

> **学习目标**：深入理解C++传统内存管理机制，掌握new/delete操作符的工作原理，识别和避免常见陷阱，为理解现代内存管理打下基础。

### 2.1 new/delete操作符深度解析

#### 2.1.1 new操作符的工作流程

```mermaid
graph TD
    A["📝 new Object(args)"] --> B["1️⃣ 调用operator new<br/>分配原始内存"]
    B --> C["2️⃣ 在分配的内存上<br/>调用构造函数"]
    C --> D{"构造成功？"}
    D -->|是| E["✅ 返回对象指针"]
    D -->|否| F["❌ 释放内存<br/>抛出异常"]
    
    classDef success fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef error fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef process fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    
    class E success
    class F error
    classDef newop fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    class A,B,C,D newop
```

#### new操作符的分解演示

```cpp
#include <iostream>
#include <new>

class TestClass {
public:
    TestClass(int value) : data_(value) {
        std::cout << "构造函数调用，值：" << value << std::endl;
    }
    
    ~TestClass() {
        std::cout << "析构函数调用，值：" << data_ << std::endl;
    }
    
    void ShowValue() const {
        std::cout << "当前值：" << data_ << std::endl;
    }

private:
    int data_;
};

void new操作符分解演示() {
    std::cout << "=== new操作符的两个步骤 ===" << std::endl;
    
    // 方法1：标准new（自动完成两步）
    std::cout << "\n--- 标准new方式 ---" << std::endl;
    TestClass* obj1 = new TestClass(42);
    obj1->ShowValue();
    delete obj1;  // 自动调用析构函数 + 释放内存
    
    // 方法2：手动分解new的两个步骤
    std::cout << "\n--- 手动分解new的步骤 ---" << std::endl;
    
    // 步骤1：分配原始内存
    void* raw_memory = operator new(sizeof(TestClass));
    std::cout << "步骤1：分配了原始内存，地址：" << raw_memory << std::endl;
    
    // 步骤2：在分配的内存上构造对象（placement new）
    TestClass* obj2 = new(raw_memory) TestClass(100);
    std::cout << "步骤2：在分配的内存上构造了对象" << std::endl;
    obj2->ShowValue();
    
    // 清理：必须手动调用析构函数 + 释放原始内存
    obj2->~TestClass();                    // 手动调用析构函数
    operator delete(raw_memory);           // 释放原始内存
    std::cout << "手动完成清理" << std::endl;
}
```

#### 2.1.2 delete操作符的工作流程

```cpp
void delete操作符分解演示() {
    std::cout << "=== delete操作符的两个步骤 ===" << std::endl;
    
    TestClass* obj = new TestClass(200);
    obj->ShowValue();
    
    std::cout << "\ndelete操作包含两步：" << std::endl;
    std::cout << "1. 调用析构函数" << std::endl;
    std::cout << "2. 释放内存" << std::endl;
    
    delete obj;  // 自动完成：析构 + 释放内存
}
```

#### 2.1.3 数组的new/delete

```cpp
void 数组new_delete演示() {
    std::cout << "=== 数组的new/delete ===" << std::endl;
    
    // 创建对象数组
    const int 数组大小 = 3;
    TestClass* 对象数组 = new TestClass[数组大小]{TestClass(1), TestClass(2), TestClass(3)};
    
    std::cout << "\n遍历数组：" << std::endl;
    for (int i = 0; i < 数组大小; ++i) {
        std::cout << "数组[" << i << "]：";
        对象数组[i].ShowValue();
    }
    
    std::cout << "\n删除数组（注意必须使用delete[]）：" << std::endl;
    delete[] 对象数组;  // 自动调用所有对象的析构函数
    
    // ❌ 错误示例：不要这样做
    // delete 对象数组;  // 只会调用第一个对象的析构函数！
}
```

### 2.2 内存分配器的底层机制

#### 2.2.1 系统内存分配原理

```cpp
#include <cstdlib>
#include <chrono>

class 内存分配性能测试 {
public:
    void 测试不同分配方式() {
        const int 测试次数 = 100000;
        
        // 测试malloc/free性能
        auto 开始 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 测试次数; ++i) {
            void* ptr = malloc(1024);
            free(ptr);
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto malloc时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);
        
        // 测试new/delete性能
        开始 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 测试次数; ++i) {
            char* ptr = new char[1024];
            delete[] ptr;
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto new时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);
        
        // 输出结果
        std::cout << "原始指针时间: " << 原始指针时间.count() << " 微秒 (基准)" << std::endl;
        std::cout << "unique_ptr时间: " << unique_ptr时间.count() << " 微秒 (比例: " 
                  << (double)unique_ptr时间.count() / 原始指针时间.count() << ")" << std::endl;
        std::cout << "shared_ptr时间: " << shared_ptr时间.count() << " 微秒 (比例: " 
                  << (double)shared_ptr时间.count() / 原始指针时间.count() << ")" << std::endl;
    }
    
    void 演示内存碎片化() {
        std::cout << "=== 内存碎片化演示 ===" << std::endl;
        
        std::vector<void*> 指针列表;
        
        // 分配许多小块内存
        std::cout << "分配1000个不同大小的内存块..." << std::endl;
        for (int i = 0; i < 1000; ++i) {
            size_t 大小 = (i % 20 + 1) * 16;  // 16到320字节
            void* ptr = malloc(大小);
            指针列表.push_back(ptr);
        }
        
        // 释放奇数位置的内存，制造碎片
        std::cout << "释放一半内存，制造碎片..." << std::endl;
        for (size_t i = 1; i < 指针列表.size(); i += 2) {
            free(指针列表[i]);
            指针列表[i] = nullptr;
        }
        
        // 尝试分配一个大块内存
        std::cout << "尝试分配大块内存..." << std::endl;
        void* 大块内存 = malloc(1024 * 1024);  // 1MB
        if (大块内存) {
            std::cout << "成功分配1MB内存" << std::endl;
            free(大块内存);
        } else {
            std::cout << "内存碎片化导致分配失败" << std::endl;
        }
        
        // 清理内存
        for (void* ptr : 指针列表) {
            if (ptr) free(ptr);
        }
    }
};
```

### 2.3 常见内存管理陷阱详解

#### 2.3.1 内存泄漏：最常见的问题

```cpp
class 内存泄漏示例 {
public:
    // ❌ 陷阱1：简单的忘记delete
    void 简单内存泄漏() {
        int* ptr = new int(42);
        // 忘记delete ptr; 
        // 内存泄漏！
    }
    
    // ❌ 陷阱2：异常导致的内存泄漏
    void 异常导致的泄漏() {
        int* ptr = new int[1000];
        
        try {
            // 如果这里抛出异常...
            可能抛出异常的函数();
            delete[] ptr;  // 这行代码不会执行！
        } catch (...) {
            // 异常处理中忘记清理
            throw;
        }
    }
    
    // ❌ 陷阱3：循环中的泄漏
    void 循环泄漏() {
        for (int i = 0; i < 1000; ++i) {
            int* ptr = new int(i);
            if (i % 2 == 0) {
                continue;  // 跳过delete，造成泄漏
            }
            delete ptr;
        }
    }
    
    // ✅ 正确的异常安全写法
    void 异常安全的内存管理() {
        std::unique_ptr<int[]> ptr(new int[1000]);
        
        try {
            可能抛出异常的函数();
            // 即使抛出异常，unique_ptr也会自动清理
        } catch (const std::exception& e) {
            std::cout << "异常：" << e.what() << std::endl;
            // 不需要手动清理资源
        }
    }

private:
    void 可能抛出异常的函数() {
        // 模拟可能抛出异常的操作
        static int 调用次数 = 0;
        if (++调用次数 % 3 == 0) {
            throw std::runtime_error("模拟异常");
        }
    }
};
```

#### 2.3.2 悬空指针：访问已释放的内存

```cpp
class 悬空指针示例 {
public:
    // ❌ 陷阱1：使用已delete的指针
    void 基本悬空指针() {
        int* ptr = new int(42);
        delete ptr;
        
        // 悬空指针！ptr仍然保存地址，但内存已释放
        std::cout << *ptr << std::endl;  // 未定义行为！
    }
    
    // ❌ 陷阱2：返回局部对象的地址
    int* 返回局部地址() {
        int local_var = 100;
        return &local_var;  // 返回栈变量地址，函数结束后失效
    }
    
    void 使用返回的局部地址() {
        int* ptr = 返回局部地址();
        std::cout << *ptr << std::endl;  // 未定义行为！
    }
    
    // ❌ 陷阱3：多个指针指向同一块内存
    void 多指针问题() {
        int* ptr1 = new int(42);
        int* ptr2 = ptr1;  // 两个指针指向同一内存
        
        delete ptr1;  // 释放内存
        // 现在ptr2成为悬空指针
        std::cout << *ptr2 << std::endl;  // 未定义行为！
    }
    
    // ❌ 陷阱4：重复delete
    void 重复删除() {
        int* ptr = new int(42);
        delete ptr;
        delete ptr;  // 重复删除，未定义行为！
    }
    
    // ✅ 正确做法：使用智能指针避免悬空指针
    void 安全的指针管理() {
        auto ptr1 = std::make_unique<int>(42);
        // auto ptr2 = ptr1;  // 编译错误！unique_ptr不能复制
        auto ptr2 = std::move(ptr1);  // 转移所有权
        
        // 现在ptr1自动变为nullptr，ptr2拥有资源
        if (ptr1) {
            std::cout << "ptr1有效" << std::endl;
        } else {
            std::cout << "ptr1已失效" << std::endl;  // 这行会执行
        }
        
        std::cout << *ptr2 << std::endl;  // 安全访问
        // ptr2离开作用域时自动清理，无需手动delete
    }
};
```

#### 2.3.3 数组边界问题

```cpp
class 数组边界问题 {
public:
    // ❌ 陷阱1：栈数组越界
    void 栈数组越界() {
        int arr[10];
        for (int i = 0; i <= 10; ++i) {  // 注意是<=，会越界
            arr[i] = i;  // 当i=10时越界访问
        }
    }
    
    // ❌ 陷阱2：堆数组越界
    void 堆数组越界() {
        int* arr = new int[10];
        arr[15] = 100;  // 越界访问，可能破坏其他内存
        delete[] arr;
    }
    
    // ❌ 陷阱3：指针运算越界
    void 指针运算越界() {
        int* arr = new int[10];
        int* ptr = arr + 15;  // 指针越界
        *ptr = 100;           // 访问无效内存
        delete[] arr;
    }
    
    // ✅ 正确做法：使用安全容器
    void 安全的数组操作() {
        // 使用std::array（编译时大小）
        std::array<int, 10> stack_arr;
        try {
            stack_arr.at(15) = 100;  // 会抛出异常而不是静默破坏内存
        } catch (const std::out_of_range& e) {
            std::cout << "检测到数组越界：" << e.what() << std::endl;
        }
        
        // 使用std::vector（运行时大小）
        std::vector<int> heap_arr(10);
        try {
            heap_arr.at(15) = 100;  // 同样会抛出异常
        } catch (const std::out_of_range& e) {
            std::cout << "检测到vector越界：" << e.what() << std::endl;
        }
        
        // 正确的访问方式
        for (size_t i = 0; i < heap_arr.size(); ++i) {
            heap_arr[i] = static_cast<int>(i);
        }
    }
};
```

### 2.4 内存管理的最佳实践

#### 2.4.1 RAII原则的深度应用

```cpp
// RAII：Resource Acquisition Is Initialization
class RAIIFileHandler {
private:
    FILE* file_;
    std::unique_ptr<char[]> buffer_;

public:
    RAIIFileHandler(const char* filename, size_t buffer_size) 
        : file_(nullptr), buffer_(nullptr) {
        
        // 获取资源
        file_ = fopen(filename, "r");
        if (!file_) {
            throw std::runtime_error("无法打开文件");
        }
        
        buffer_ = std::make_unique<char[]>(buffer_size);
        std::cout << "文件和缓冲区资源已获取" << std::endl;
    }
    
    ~RAIIFileHandler() {
        // 自动释放资源
        if (file_) {
            fclose(file_);
            std::cout << "文件已关闭" << std::endl;
        }
        // buffer_会自动释放
        std::cout << "缓冲区已释放" << std::endl;
    }
    
    // 禁止拷贝，防止资源管理混乱
    RAIIFileHandler(const RAIIFileHandler&) = delete;
    RAIIFileHandler& operator=(const RAIIFileHandler&) = delete;
    
    // 允许移动
    RAIIFileHandler(RAIIFileHandler&& other) noexcept 
        : file_(other.file_), buffer_(std::move(other.buffer_)) {
        other.file_ = nullptr;
    }
    
    void ProcessFile() {
        if (file_ && buffer_) {
            std::cout << "正在处理文件..." << std::endl;
            // 文件处理逻辑
        }
    }
};

void 演示RAII原则() {
    try {
        RAIIFileHandler handler("test.txt", 1024);
        handler.ProcessFile();
        // 即使这里抛出异常，析构函数也会被调用
        // 资源会被正确释放
    } catch (const std::exception& e) {
        std::cout << "异常：" << e.what() << std::endl;
        // 不需要手动清理资源
    }
}
```

#### 2.4.2 异常安全的内存管理

```cpp
class 异常安全示例 {
public:
    // ❌ 异常不安全的代码
    void 异常不安全() {
        int* ptr1 = new int(10);
        int* ptr2 = new int(20);  // 如果这里抛出异常，ptr1泄漏
        
        try {
            危险操作();
            delete ptr1;
            delete ptr2;
        } catch (...) {
            // 忘记在异常处理中清理资源
            throw;
        }
    }
    
    // ✅ 异常安全的代码
    void 异常安全() {
        auto ptr1 = std::make_unique<int>(10);
        auto ptr2 = std::make_unique<int>(20);  // 即使抛异常，ptr1也会自动清理
        
        try {
            危险操作();
            // 无需手动delete，智能指针自动处理
        } catch (...) {
            // 无需手动清理，RAII自动处理
            throw;
        }
    }
    
    // 强异常安全保证：要么全部成功，要么保持原状
    class SafeContainer {
    private:
        std::vector<std::unique_ptr<int>> data_;
        
    public:
        void AddElement(int value) {
            auto new_element = std::make_unique<int>(value);
            
            // 可能抛出异常的操作
            if (data_.size() >= 100) {
                throw std::runtime_error("容器已满");
            }
            
            // 只有确保不会失败时才修改状态
            data_.push_back(std::move(new_element));
        }
        
        size_t Size() const { return data_.size(); }
    };

private:
    void 危险操作() {
        static int 调用次数 = 0;
        if (++调用次数 % 2 == 0) {
            throw std::runtime_error("模拟异常");
        }
    }
};
```

### 2.5 内存调试技术

#### 2.5.1 手动内存泄漏检测

```cpp
// 简单的内存分配跟踪器
class 内存跟踪器 {
private:
    static std::map<void*, size_t> 分配记录;
    static size_t 总分配次数;
    static size_t 总释放次数;
    static size_t 当前使用内存;

public:
    static void* 跟踪分配(size_t 大小) {
        void* ptr = malloc(大小);
        if (ptr) {
            分配记录[ptr] = 大小;
            总分配次数++;
            当前使用内存 += 大小;
            std::cout << "分配内存：" << ptr << "，大小：" << 大小 << " 字节" << std::endl;
        }
        return ptr;
    }
    
    static void 跟踪释放(void* ptr) {
        auto it = 分配记录.find(ptr);
        if (it != 分配记录.end()) {
            size_t 大小 = it->second;
            当前使用内存 -= 大小;
            总释放次数++;
            std::cout << "释放内存：" << ptr << "，大小：" << 大小 << " 字节" << std::endl;
            分配记录.erase(it);
            free(ptr);
        } else {
            std::cout << "警告：试图释放未跟踪的内存：" << ptr << std::endl;
        }
    }
    
    static void 打印统计信息() {
        std::cout << "\n=== 内存使用统计 ===" << std::endl;
        std::cout << "总分配次数：" << 总分配次数 << std::endl;
        std::cout << "总释放次数：" << 总释放次数 << std::endl;
        std::cout << "当前使用内存：" << 当前使用内存 << " 字节" << std::endl;
        std::cout << "未释放的内存块数量：" << 分配记录.size() << std::endl;
        
        if (!分配记录.empty()) {
            std::cout << "\n未释放的内存块：" << std::endl;
            for (const auto& pair : 分配记录) {
                std::cout << "地址：" << pair.first << "，大小：" << pair.second << " 字节" << std::endl;
            }
        }
    }
};

// 静态成员定义
std::map<void*, size_t> 内存跟踪器::分配记录;
size_t 内存跟踪器::总分配次数 = 0;
size_t 内存跟踪器::总释放次数 = 0;
size_t 内存跟踪器::当前使用内存 = 0;

// 测试内存跟踪
void 测试内存跟踪() {
    std::cout << "=== 内存跟踪测试 ===" << std::endl;
    
    void* ptr1 = 内存跟踪器::跟踪分配(100);
    void* ptr2 = 内存跟踪器::跟踪分配(200);
    void* ptr3 = 内存跟踪器::跟踪分配(50);
    
    内存跟踪器::跟踪释放(ptr1);
    内存跟踪器::跟踪释放(ptr3);
    // 故意不释放ptr2，模拟内存泄漏
    
    内存跟踪器::打印统计信息();
}
```

#### 2.5.2 工具辅助的内存调试

```cpp
// Valgrind示例（伪代码，实际运行在命令行）
void 工具辅助内存调试示例() {
    std::cout << "=== Valgrind内存调试示例 ===" << std::endl;
    
    // 编译时加上-g选项生成调试信息
    // g++ -g your_code.cpp -o your_program
    
    // 运行Valgrind检测内存泄漏
    // valgrind --leak-check=full --show-leak-kinds=all ./your_program
    
    // 示例输出（实际运行Valgrind后获得）
    std::cout << "==1234== Memcheck, a memory error detector\n";
    std::cout << "==1234== LEAK SUMMARY:\n";
    std::cout << "==1234==    definitely lost: 4,096 bytes in 1 blocks\n";
    std::cout << "==1234==    indirectly lost: 0 bytes in 0 blocks\n";
    std::cout << "==1234==    possibly lost: 0 bytes in 0 blocks\n";
    std::cout << "==1234==    still reachable: 8,192 bytes in 2 blocks\n";
    std::cout << "==1234==    suppressed: 0 bytes in 0 blocks\n";
}
```

### 2.6 Part 2 总结

> **🎯 Part 2 核心要点**：

#### 技术掌握清单

| 技术点 | 掌握程度 | 关键概念 |
|--------|----------|----------|
| **new/delete机制** | ⭐⭐⭐ | 分配内存+构造对象，析构对象+释放内存 |
| **内存泄漏** | ⭐⭐⭐ | 识别和避免各种泄漏模式 |
| **悬空指针** | ⭐⭐⭐ | 理解指针失效的各种情况 |
| **异常安全** | ⭐⭐ | RAII原则的应用 |
| **调试技术** | ⭐⭐ | 内存问题的检测和定位 |

#### 常见陷阱总结

1. **内存泄漏**：分配后忘记释放
2. **悬空指针**：使用已释放的内存
3. **重复删除**：对同一内存多次调用delete
4. **数组越界**：访问数组边界外的内存
5. **异常安全**：异常发生时的资源泄漏

#### 最佳实践

- ✅ 每个new都要有对应的delete
- ✅ 使用RAII原则管理资源
- ✅ 优先使用智能指针而非原始指针
- ✅ 使用容器而非原始数组
- ✅ 编写异常安全的代码

**接下来**：Part 3将深入学习智能指针技术，了解现代C++内存管理的最佳实践。

---

## Part 3: 智能指针技术——现代C++内存管理的核心

> **学习目标**：全面掌握C++11引入的智能指针体系，理解RAII原则的深度应用，学会在实际项目中选择和使用合适的智能指针。

### 3.1 智能指针概览：现代C++的救星

#### 3.1.1 智能指针的核心思想

智能指针是现代C++内存管理的革命性工具，它们通过RAII原则将内存管理自动化：

```mermaid
graph TD
    A["🤵 智能指针管家"] --> B["🔑 获得内存钥匙<br/>构造时分配"]
    B --> C["👁️ 时刻监控使用情况<br/>引用计数/所有权跟踪"]
    C --> D{"主人还需要吗？"}
    D -->|需要| C
    D -->|不需要| E["🗑️ 自动清理内存<br/>析构时释放"]
    
    F["❌ 传统方式"] --> G["👷 程序员手动管理<br/>容易出错"]
    F --> H["💥 忘记释放 = 内存泄漏<br/>重复释放 = 程序崩溃"]
    
    classDef smart fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef traditional fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class A,B,C,D,E smart
    class F,G,H traditional
```

#### 3.1.2 三种智能指针的特点对比

| 智能指针类型 | 所有权模式 | 使用场景 | 内存开销 | 线程安全 |
|-------------|-----------|----------|----------|----------|
| **unique_ptr** | 独占所有权 | 单一拥有者，可转移 | 零开销 | 不适用 |
| **shared_ptr** | 共享所有权 | 多个拥有者 | 引用计数开销 | 引用计数操作线程安全 |
| **weak_ptr** | 观察者模式 | 打破循环引用 | 最小开销 | 配合shared_ptr |

### 3.2 unique_ptr：独占所有权的智能指针

#### 3.2.1 make_unique：现代C++的推荐创建方式

> **🎯 核心原则**：优先使用`make_unique`而不是`new`来创建`unique_ptr`

```cpp
#include <memory>
#include <iostream>
#include <vector>

class Resource {
public:
    Resource(int id) : id_(id) {
        std::cout << "创建Resource " << id_ << std::endl;
    }

    ~Resource() {
        std::cout << "销毁Resource " << id_ << std::endl;
    }

    void DoWork() const {
        std::cout << "Resource " << id_ << " 正在工作" << std::endl;
    }

    int GetId() const { return id_; }

private:
    int id_;
};

void make_unique详解() {
    std::cout << "=== make_unique vs 传统方式对比 ===" << std::endl;

    // ❌ 传统方式（不推荐）
    std::unique_ptr<Resource> ptr1(new Resource(1));

    // ✅ 现代方式（强烈推荐）
    auto ptr2 = std::make_unique<Resource>(2);

    // 🎯 为什么make_unique更好？
    std::cout << "\n=== make_unique的优势 ===" << std::endl;

    // 1. 异常安全
    try {
        // ❌ 潜在问题：如果第二个new抛出异常，第一个new的内存会泄漏
        // 危险函数(std::unique_ptr<Resource>(new Resource(10)),
        //          std::unique_ptr<Resource>(new Resource(20)));

        // ✅ 安全方式：make_unique保证异常安全
        危险函数(std::make_unique<Resource>(10),
                std::make_unique<Resource>(20));
    } catch (...) {
        std::cout << "异常处理：make_unique确保没有内存泄漏" << std::endl;
    }

    // 2. 代码简洁性
    std::cout << "\n=== 代码简洁性对比 ===" << std::endl;

    // ❌ 冗长的写法
    std::unique_ptr<Resource> verbose_ptr(new Resource(100));

    // ✅ 简洁的写法
    auto clean_ptr = std::make_unique<Resource>(100);

    // 3. 类型推导
    std::cout << "\n=== 自动类型推导 ===" << std::endl;

    // ✅ 编译器自动推导类型，减少错误
    auto auto_ptr = std::make_unique<Resource>(200);

    // 4. 性能一致性
    std::cout << "\n=== 性能对比 ===" << std::endl;

    const int 测试次数 = 100000;

    // 测试传统方式
    auto 开始 = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < 测试次数; ++i) {
        std::unique_ptr<int> ptr(new int(i));
    }
    auto 结束 = std::chrono::high_resolution_clock::now();
    auto 传统时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

    // 测试make_unique方式
    开始 = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < 测试次数; ++i) {
        auto ptr = std::make_unique<int>(i);
    }
    结束 = std::chrono::high_resolution_clock::now();
    auto make_unique时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

    std::cout << "传统方式时间: " << 传统时间.count() << " 微秒" << std::endl;
    std::cout << "make_unique时间: " << make_unique时间.count() << " 微秒" << std::endl;
    std::cout << "性能差异: " << std::abs((int)(传统时间.count() - make_unique时间.count())) << " 微秒" << std::endl;
}

// 模拟可能抛出异常的函数
void 危险函数(std::unique_ptr<Resource> r1, std::unique_ptr<Resource> r2) {
    r1->DoWork();
    r2->DoWork();
    // 模拟异常
    static int 调用次数 = 0;
    if (++调用次数 % 3 == 0) {
        throw std::runtime_error("模拟异常");
    }
}

#### 3.2.2 make_unique的高级用法

```cpp
class make_unique高级用法 {
public:
    // 1. 创建数组
    void 数组创建演示() {
        std::cout << "=== make_unique创建数组 ===" << std::endl;

        // ✅ 创建动态数组
        auto int_array = std::make_unique<int[]>(10);

        // 初始化数组
        for (int i = 0; i < 10; ++i) {
            int_array[i] = i * i;
        }

        // 访问数组元素
        for (int i = 0; i < 10; ++i) {
            std::cout << "array[" << i << "] = " << int_array[i] << std::endl;
        }

        // ✅ 创建对象数组
        auto resource_array = std::make_unique<Resource[]>(3);
        // 注意：数组元素会使用默认构造函数
    }

    // 2. 完美转发
    template<typename T, typename... Args>
    std::unique_ptr<T> my_make_unique(Args&&... args) {
        return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
    }

    void 完美转发演示() {
        std::cout << "=== 完美转发演示 ===" << std::endl;

        // make_unique内部使用完美转发，保持参数的值类别
        class ComplexResource {
        public:
            ComplexResource(const std::string& name, int&& temp_value)
                : name_(name), value_(temp_value) {
                std::cout << "构造ComplexResource: " << name_ << ", " << value_ << std::endl;
            }

        private:
            std::string name_;
            int value_;
        };

        std::string name = "测试资源";
        auto resource = std::make_unique<ComplexResource>(name, 42);

        // make_unique正确转发了左值引用和右值引用
    }

    // 3. 工厂函数模式
    template<typename T>
    class Factory {
    public:
        template<typename... Args>
        static std::unique_ptr<T> Create(Args&&... args) {
            return std::make_unique<T>(std::forward<Args>(args)...);
        }

        // 条件创建
        static std::unique_ptr<T> CreateIf(bool condition, int id) {
            if (condition) {
                return std::make_unique<T>(id);
            }
            return nullptr;
        }
    };

    void 工厂模式演示() {
        std::cout << "=== 工厂模式演示 ===" << std::endl;

        // 使用工厂创建对象
        auto resource1 = Factory<Resource>::Create(100);
        auto resource2 = Factory<Resource>::CreateIf(true, 200);
        auto resource3 = Factory<Resource>::CreateIf(false, 300);

        if (resource1) resource1->DoWork();
        if (resource2) resource2->DoWork();
        if (resource3) {
            resource3->DoWork();
        } else {
            std::cout << "resource3创建失败" << std::endl;
        }
    }
};

void 智能指针演示() {
    std::cout << "=== unique_ptr：独占所有权 ===" << std::endl;
    {
        // 🔑 unique_ptr：我的东西我做主
        auto 我的文件 = std::make_unique<重要文件>("个人日记.txt");
        我的文件->编辑();
        // 离开作用域自动销毁
    }
    
    std::cout << "\n=== shared_ptr：共享所有权 ===" << std::endl;
    {
        // 🏠 shared_ptr：大家一起用，最后一个关灯
        auto 共享文件 = std::make_shared<重要文件>("团队项目.txt");
        
        {
            auto 另一个引用 = 共享文件;  // 引用计数：2
            另一个引用->编辑();
            // 内层作用域结束，引用计数：1
        }
        
        共享文件->编辑();  // 引用计数仍为1
        // 外层作用域结束，引用计数：0，自动销毁
    }
    
    std::cout << "\n=== weak_ptr：观察者模式 ===" << std::endl;
    {
        // 👀 weak_ptr：看得见摸不着
        std::weak_ptr<重要文件> 观察者;
        
        {
            auto 原始文件 = std::make_shared<重要文件>("临时文件.txt");
            观察者 = 原始文件;  // 观察但不拥有
            
            if (auto 临时访问 = 观察者.lock()) {  // 尝试获取访问权
                临时访问->编辑();
            }
            // 原始文件离开作用域，被销毁
        }
        
        if (观察者.expired()) {
            std::cout << "观察的文件已经被销毁了" << std::endl;
        }
    }
}
```

#### 3.2.2 unique_ptr的高级特性

```cpp
class unique_ptr高级特性 {
public:
    // 自定义删除器
    void 自定义删除器演示() {
        std::cout << "=== 自定义删除器演示 ===" << std::endl;
        
        // 使用lambda作为删除器
        auto deleter = [](Resource* ptr) {
            std::cout << "使用自定义删除器销毁资源" << std::endl;
            delete ptr;
        };
        
        std::unique_ptr<Resource, decltype(deleter)> ptr(new Resource(10), deleter);
        ptr->DoWork();
        // 离开作用域时自动调用自定义删除器
    }
    
    // 数组的unique_ptr
    void 数组unique_ptr演示() {
        std::cout << "=== 数组unique_ptr演示 ===" << std::endl;
        
        // 管理动态数组
               auto arr = std::make_unique<int[]>(10);
        
        // 初始化数组
        for (int i = 0; i < 10; ++i) {
            arr[i] = i * i;
        }
        
        // 访问数组元素
        for (int i = 0; i < 10; ++i) {
            std::cout << "arr[" << i << "] = " << arr[i] << std::endl;
        }
        
        // 自动调用delete[]而不是delete
    }
    
    // 工厂函数模式
    static std::unique_ptr<Resource> CreateResource(int id) {

        if (id <= 0) {
            return nullptr;  // 可以返回空指针
        }
        return std::make_unique<Resource>(id);
    }
    
    void 工厂函数演示() {
        std::cout << "\n=== 工厂函数演示 ===" << std::endl;
        
        auto resource = CreateResource(100);
        if (resource) {
            resource->DoWork();
        }
        
        auto invalid_resource = CreateResource(-1);
        if (!invalid_resource) {
            std::cout << "创建资源失败" << std::endl;
        }
    }
    
    // 容器中的unique_ptr
    void 容器演示() {
        std::cout << "=== 容器中的unique_ptr演示 ===" << std::endl;
        
        std::vector<std::unique_ptr<Resource>> resources;
        
        // 添加资源
        for (int i = 1; i <= 5; ++i) {
            resources.push_back(std::make_unique<Resource>(i));
        }
        
        // 使用资源
        for (const auto& resource : resources) {
            resource->DoWork();
        }
        
        // 移除第二个资源
        resources.erase(resources.begin() + 1);
        std::cout << "移除第二个资源后:" << std::endl;
        
        for (const auto& resource : resources) {
            resource->DoWork();
        }
        
        // 容器销毁时自动清理所有资源
    }
};
```

### 3.3 shared_ptr：共享所有权的智能指针

#### 3.3.1 make_shared：高效的shared_ptr创建方式

> **🎯 核心原则**：优先使用`make_shared`而不是`shared_ptr(new T)`

```cpp
class make_shared详解 {
public:
    void make_shared优势演示() {
        std::cout << "=== make_shared vs 传统方式对比 ===" << std::endl;

        // ❌ 传统方式（不推荐）
        std::shared_ptr<Resource> ptr1(new Resource(1));

        // ✅ 现代方式（强烈推荐）
        auto ptr2 = std::make_shared<Resource>(2);

        std::cout << "\n=== make_shared的关键优势 ===" << std::endl;

        // 1. 内存效率：单次分配 vs 两次分配
        演示内存分配差异();

        // 2. 异常安全
        演示异常安全性();

        // 3. 性能优势
        演示性能差异();

        // 4. 缓存友好性
        演示缓存友好性();
    }

private:
    void 演示内存分配差异() {
        std::cout << "\n--- 内存分配模式对比 ---" << std::endl;

        // ❌ 传统方式：两次内存分配
        // 1. new Resource(100) - 分配对象内存
        // 2. shared_ptr构造 - 分配控制块内存
        std::shared_ptr<Resource> traditional_ptr(new Resource(100));

        // ✅ make_shared方式：单次内存分配
        // 一次分配包含：对象内存 + 控制块内存
        auto efficient_ptr = std::make_shared<Resource>(200);

        std::cout << "传统方式：对象和控制块分别分配（内存碎片化）" << std::endl;
        std::cout << "make_shared：对象和控制块连续分配（内存友好）" << std::endl;

        // 内存布局对比
        std::cout << "\n内存布局对比：" << std::endl;
        std::cout << "传统方式: [对象内存] ... [控制块内存] (可能不连续)" << std::endl;
        std::cout << "make_shared: [控制块|对象内存] (连续分配)" << std::endl;
    }

    void 演示异常安全性() {
        std::cout << "\n--- 异常安全性对比 ---" << std::endl;

        try {
            // ❌ 潜在的异常安全问题
            // 如果第二个new抛出异常，第一个new的内存可能泄漏
            // 危险函数(std::shared_ptr<Resource>(new Resource(10)),
            //          std::shared_ptr<Resource>(new Resource(20)));

            // ✅ 异常安全的方式
            危险函数(std::make_shared<Resource>(10),
                    std::make_shared<Resource>(20));

        } catch (const std::exception& e) {
            std::cout << "异常捕获：" << e.what() << std::endl;
            std::cout << "make_shared确保异常安全，无内存泄漏" << std::endl;
        }
    }

    void 演示性能差异() {
        std::cout << "\n--- 性能对比测试 ---" << std::endl;

        const int 测试次数 = 100000;

        // 测试传统shared_ptr创建
        auto 开始 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 测试次数; ++i) {
            std::shared_ptr<int> ptr(new int(i));
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto 传统时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        // 测试make_shared创建
        开始 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 测试次数; ++i) {
            auto ptr = std::make_shared<int>(i);
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto make_shared时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        std::cout << "传统shared_ptr时间: " << 传统时间.count() << " 微秒" << std::endl;
        std::cout << "make_shared时间: " << make_shared时间.count() << " 微秒" << std::endl;
        std::cout << "性能提升: " << (double)传统时间.count() / make_shared时间.count() << " 倍" << std::endl;

        // 原因分析
        std::cout << "\n性能提升原因：" << std::endl;
        std::cout << "1. 减少内存分配次数（2次→1次）" << std::endl;
        std::cout << "2. 减少内存分配器调用开销" << std::endl;
        std::cout << "3. 提高内存局部性，缓存友好" << std::endl;
    }

    void 演示缓存友好性() {
        std::cout << "\n--- 缓存友好性测试 ---" << std::endl;

        const int 数组大小 = 100000;

        // 创建传统shared_ptr数组
        std::vector<std::shared_ptr<int>> 传统数组;
        for (int i = 0; i < 数组大小; ++i) {
            传统数组.push_back(std::shared_ptr<int>(new int(i)));
        }

        // 创建make_shared数组
        std::vector<std::shared_ptr<int>> make_shared数组;
        for (int i = 0; i < 数组大小; ++i) {
            make_shared数组.push_back(std::make_shared<int>(i));
        }

        // 测试访问性能
        auto 开始 = std::chrono::high_resolution_clock::now();
        long long 总和1 = 0;
        for (const auto& ptr : 传统数组) {
            总和1 += *ptr;
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto 传统访问时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        开始 = std::chrono::high_resolution_clock::now();
        long long 总和2 = 0;
        for (const auto& ptr : make_shared数组) {
            总和2 += *ptr;
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto make_shared访问时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        std::cout << "传统shared_ptr访问时间: " << 传统访问时间.count() << " 微秒" << std::endl;
        std::cout << "make_shared访问时间: " << make_shared访问时间.count() << " 微秒" << std::endl;
        std::cout << "缓存友好性提升: " << (double)传统访问时间.count() / make_shared访问时间.count() << " 倍" << std::endl;
    }

    void 危险函数(std::shared_ptr<Resource> r1, std::shared_ptr<Resource> r2) {
        r1->DoWork();
        r2->DoWork();
        static int 调用次数 = 0;
        if (++调用次数 % 3 == 0) {
            throw std::runtime_error("模拟异常");
        }
    }
};

#### 3.3.2 shared_ptr的引用计数机制

```cpp
class shared_ptr详解 {
public:
    void 引用计数演示() {
        std::cout << "=== shared_ptr引用计数演示 ===" << std::endl;

        {
            auto ptr1 = std::make_shared<Resource>(100);
            std::cout << "ptr1引用计数: " << ptr1.use_count() << std::endl;

            {
                auto ptr2 = ptr1;  // 拷贝，引用计数增加
                std::cout << "ptr1引用计数: " << ptr1.use_count() << std::endl;
                std::cout << "ptr2引用计数: " << ptr2.use_count() << std::endl;

                {
                    auto ptr3 = ptr1;  // 再次拷贝
                    std::cout << "三个指针的引用计数: " << ptr3.use_count() << std::endl;

                    // ptr3离开作用域，引用计数减1
                }

                std::cout << "ptr3销毁后引用计数: " << ptr1.use_count() << std::endl;

                // ptr2离开作用域，引用计数减1
            }

            std::cout << "ptr2销毁后引用计数: " << ptr1.use_count() << std::endl;

            // ptr1离开作用域，引用计数减为0，资源被销毁
        }

        std::cout << "所有shared_ptr销毁，资源已释放" << std::endl;
    }
    
    void 性能开销分析() {
        std::cout << "\n=== shared_ptr性能开销分析 ===" << std::endl;
        
        const int 测试次数 = 100000;
        
        // 测试unique_ptr的性能
        auto 开始 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 测试次数; ++i) {
            auto ptr = std::make_unique<int>(i);
            int value = *ptr;  // 访问数据
            (void)value;
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto unique_ptr时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);
        
        // 测试shared_ptr的性能
        开始 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 测试次数; ++i) {
            auto ptr = std::make_shared<int>(i);
            int value = *ptr;  // 访问数据
            (void)value;
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto shared_ptr时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);
        
        // 输出结果
        std::cout << "原始指针时间: " << 原始指针时间.count() << " 微秒 (基准)" << std::endl;
        std::cout << "unique_ptr时间: " << unique_ptr时间.count() << " 微秒 (比例: " 
                  << (double)unique_ptr时间.count() / 原始指针时间.count() << ")" << std::endl;
        std::cout << "shared_ptr时间: " << shared_ptr时间.count() << " 微秒 (比例: " 
                  << (double)shared_ptr时间.count() / 原始指针时间.count() << ")" << std::endl;
    }
};
```

#### 3.3.2 shared_ptr的高级应用

```cpp
class shared_ptr高级应用 {
public:
    // 线程安全的引用计数
    void 线程安全演示() {
        std::cout << "=== shared_ptr线程安全演示 ===" << std::endl;
        
        auto shared_resource = std::make_shared<Resource>(200);
        
        // 创建多个线程共享资源
        std::vector<std::thread> threads;
        
        for (int i = 0; i < 4; ++i) {
            threads.emplace_back([shared_resource, i]() {
                // 每个线程中拷贝shared_ptr（引用计数操作是线程安全的）
                auto local_ptr = shared_resource;
                
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                std::cout << "线程 " << i << " 使用资源，当前引用计数: " 
                          << local_ptr.use_count() << std::endl;
                
                local_ptr->DoWork();
            });
        }
        
        // 等待所有线程完成
        for (auto& t : threads) {
            t.join();
        }
        
        std::cout << "所有线程完成，主线程引用计数: " 
                  << shared_resource.use_count() << std::endl;
    }
    
    // 自定义删除器
    void 自定义删除器演示() {
        std::cout << "=== shared_ptr自定义删除器演示 ===" << std::endl;
        
        // 使用自定义删除器管理文件
        auto file_deleter = [](FILE* fp) {
            if (fp) {
                std::cout << "关闭文件" << std::endl;
                fclose(fp);
            }
        };
        
        // 注意：这里为了演示，使用临时文件名
        std::shared_ptr<FILE> file_ptr(fopen("temp.txt", "w"), file_deleter);
        
        if (file_ptr) {
            fprintf(file_ptr.get(), "Hello, World!");
            std::cout << "写入文件完成" << std::endl;
        }
        
        // 离开作用域时自动调用自定义删除器
    }
    
    // 对象池模式
    class ObjectPool {
    private:
        std::vector<std::shared_ptr<Resource>> pool_;
        std::mutex mutex_;
        
    public:
        std::shared_ptr<Resource> Acquire(int id) {
            std::lock_guard<std::mutex> lock(mutex_);
            
            if (!pool_.empty()) {
                auto resource = pool_.back();
                pool_.pop_back();
                std::cout << "从池中获取资源" << std::endl;
                return resource;
            }
            
            std::cout << "创建新资源" << std::endl;
            return std::make_shared<Resource>(id);
        }
        
        void Release(std::shared_ptr<Resource> resource) {
            std::lock_guard<std::mutex> lock(mutex_);
            
            if (resource.use_count() == 1) {  // 只有这一个引用
                pool_.push_back(resource);
                std::cout << "资源归还到池中" << std::endl;
            }
        }
        
        size_t PoolSize() const {
            std::lock_guard<std::mutex> lock(mutex_);
            return pool_.size();
        }
    };
    
    void 对象池演示() {
        std::cout << "=== 对象池演示 ===" << std::endl;
        
        ObjectPool pool;
        
        {
            auto resource1 = pool.Acquire(1);
            auto resource2 = pool.Acquire(2);
            
            resource1->DoWork();
            resource2->DoWork();
            
            // 显式归还资源
            pool.Release(resource1);
            pool.Release(resource2);
            
            std::cout << "池中资源数量: " << pool.PoolSize() << std::endl;
        }
        
        // 从池中重用资源
        auto reused_resource = pool.Acquire(3);
        reused_resource->DoWork();
    }
};
```

#### 3.3.3 make_shared的特殊情况和注意事项

```cpp
class make_shared注意事项 {
public:
    void 演示特殊情况() {
        std::cout << "=== make_shared的特殊情况 ===" << std::endl;

        // 1. 自定义删除器的限制
        演示自定义删除器限制();

        // 2. 内存释放时机
        演示内存释放时机();

        // 3. 数组支持（C++20）
        演示数组支持();

        // 4. 私有构造函数的处理
        演示私有构造函数();
    }

private:
    void 演示自定义删除器限制() {
        std::cout << "\n--- 自定义删除器限制 ---" << std::endl;

        // ❌ make_shared不支持自定义删除器
        // auto ptr = std::make_shared<Resource>(100, custom_deleter);  // 编译错误

        // ✅ 需要自定义删除器时，使用传统方式
        auto custom_deleter = [](Resource* ptr) {
            std::cout << "使用自定义删除器" << std::endl;
            delete ptr;
        };

        std::shared_ptr<Resource> ptr(new Resource(100), custom_deleter);

        std::cout << "当需要自定义删除器时，无法使用make_shared" << std::endl;
        std::cout << "这是make_shared的一个限制" << std::endl;
    }

    void 演示内存释放时机() {
        std::cout << "\n--- 内存释放时机差异 ---" << std::endl;

        std::weak_ptr<Resource> weak_observer;

        {
            auto shared_ptr = std::make_shared<Resource>(200);
            weak_observer = shared_ptr;

            std::cout << "shared_ptr引用计数: " << shared_ptr.use_count() << std::endl;
            std::cout << "weak_ptr是否过期: " << weak_observer.expired() << std::endl;

            // shared_ptr离开作用域
        }

        // 重要：make_shared的内存释放特点
        std::cout << "shared_ptr销毁后，weak_ptr状态: " << weak_observer.expired() << std::endl;

        std::cout << "\n内存释放说明：" << std::endl;
        std::cout << "- 传统方式：对象销毁后立即释放对象内存，控制块稍后释放" << std::endl;
        std::cout << "- make_shared：对象和控制块一起分配，一起释放" << std::endl;
        std::cout << "- 影响：如果有weak_ptr存在，make_shared的内存释放会延迟" << std::endl;
    }

    void 演示数组支持() {
        std::cout << "\n--- 数组支持（C++20特性）---" << std::endl;

        #if __cplusplus >= 202002L  // C++20
        // ✅ C++20支持make_shared创建数组
        auto int_array = std::make_shared<int[]>(10);

        // 初始化数组
        for (int i = 0; i < 10; ++i) {
            int_array[i] = i * 2;
        }

        std::cout << "C++20支持make_shared创建数组" << std::endl;
        #else
        // C++17及之前版本的替代方案
        std::cout << "C++17及之前版本不支持make_shared数组" << std::endl;
        std::cout << "替代方案：" << std::endl;

        // 方案1：使用vector
        auto vector_solution = std::make_shared<std::vector<int>>(10);

        // 方案2：使用传统shared_ptr
        std::shared_ptr<int[]> array_ptr(new int[10]);

        std::cout << "1. 使用vector包装" << std::endl;
        std::cout << "2. 使用传统shared_ptr<T[]>" << std::endl;
        #endif
    }

    void 演示私有构造函数() {
        std::cout << "\n--- 私有构造函数的处理 ---" << std::endl;

        // 对于私有构造函数的类，make_shared可能无法访问
        class PrivateConstructor {
        private:
            PrivateConstructor(int value) : value_(value) {}

        public:
            static std::shared_ptr<PrivateConstructor> Create(int value) {
                // ❌ 这样不行：make_shared无法访问私有构造函数
                // return std::make_shared<PrivateConstructor>(value);

                // ✅ 解决方案1：使用传统方式（需要友元）
                return std::shared_ptr<PrivateConstructor>(new PrivateConstructor(value));
            }

            // 解决方案2：使用公共构造器结构体
            struct MakeSharedEnabler : public PrivateConstructor {
                MakeSharedEnabler(int value) : PrivateConstructor(value) {}
            };

            static std::shared_ptr<PrivateConstructor> CreateWithMakeShared(int value) {
                return std::make_shared<MakeSharedEnabler>(value);
            }

            int GetValue() const { return value_; }

        private:
            int value_;
        };

        auto obj1 = PrivateConstructor::Create(100);
        auto obj2 = PrivateConstructor::CreateWithMakeShared(200);

        std::cout << "私有构造函数对象1值: " << obj1->GetValue() << std::endl;
        std::cout << "私有构造函数对象2值: " << obj2->GetValue() << std::endl;
    }
};

#### 3.3.4 智能指针创建函数的选择指南

```cpp
class 智能指针创建指南 {
public:
    void 选择指南() {
        std::cout << "=== 智能指针创建函数选择指南 ===" << std::endl;

        std::cout << "\n🎯 unique_ptr创建选择：" << std::endl;
        std::cout << "✅ 优先使用：make_unique<T>(args...)" << std::endl;
        std::cout << "✅ 数组使用：make_unique<T[]>(size)" << std::endl;
        std::cout << "❌ 避免使用：unique_ptr<T>(new T(args...))" << std::endl;

        std::cout << "\n🎯 shared_ptr创建选择：" << std::endl;
        std::cout << "✅ 优先使用：make_shared<T>(args...)" << std::endl;
        std::cout << "⚠️  特殊情况使用传统方式：" << std::endl;
        std::cout << "   - 需要自定义删除器" << std::endl;
        std::cout << "   - 私有构造函数且无法修改" << std::endl;
        std::cout << "   - 需要精确控制内存释放时机" << std::endl;

        // 实际示例
        演示最佳实践();
    }

private:
    void 演示最佳实践() {
        std::cout << "\n=== 最佳实践示例 ===" << std::endl;

        // ✅ 推荐的创建方式
        auto unique_resource = std::make_unique<Resource>(1);
        auto shared_resource = std::make_shared<Resource>(2);
        auto unique_array = std::make_unique<int[]>(100);

        // ✅ 工厂函数模式
        auto factory_resource = CreateResource(3);

        // ✅ 容器中使用
        std::vector<std::unique_ptr<Resource>> unique_container;
        unique_container.push_back(std::make_unique<Resource>(4));

        std::vector<std::shared_ptr<Resource>> shared_container;
        shared_container.push_back(std::make_shared<Resource>(5));

        // ✅ 异常安全的函数调用
        ProcessResources(std::make_unique<Resource>(6),
                        std::make_shared<Resource>(7));

        std::cout << "所有资源创建完成，使用现代C++最佳实践" << std::endl;
    }

    std::unique_ptr<Resource> CreateResource(int id) {
        return std::make_unique<Resource>(id);
    }

    void ProcessResources(std::unique_ptr<Resource> unique_res,
                         std::shared_ptr<Resource> shared_res) {
        unique_res->DoWork();
        shared_res->DoWork();
    }
};
```

### 3.4 weak_ptr：解决循环引用的观察者

#### 3.4.1 循环引用问题

```cpp
class Node {
public:
    Node(int value) : value_(value) {
        std::cout << "创建Node " << value_ << std::endl;
    }
    
    ~Node() {
        std::cout << "销毁Node " << value_ << std::endl;
    }
    
    void SetNext(std::shared_ptr<Node> next) {
        next_ = next;
    }
    
    void SetPrev(std::shared_ptr<Node> prev) {
        prev_ = prev;  // 这会造成循环引用！
    }
    
    int GetValue() const { return value_; }

private:
    int value_;
    std::shared_ptr<Node> next_;
    std::shared_ptr<Node> prev_;  // 问题所在
};

void 循环引用问题演示() {
    std::cout << "=== 循环引用问题演示 ===" << std::endl;
    
    {
        auto node1 = std::make_shared<Node>(1);
        auto node2 = std::make_shared<Node>(2);
        
        // 创建循环引用
        node1->SetNext(node2);
        node2->SetPrev(node1);  // 循环引用！
        
        std::cout << "node1引用计数: " << node1.use_count() << std::endl;  // 2
        std::cout << "node2引用计数: " << node2.use_count() << std::endl;  // 2
        
        // 离开作用域后，两个节点的引用计数都不会变为0
        // 因为它们互相引用，导致内存泄漏！
    }
    
    std::cout << "离开作用域，但节点可能没有被销毁（内存泄漏）" << std::endl;
}
```

#### 3.4.2 使用weak_ptr解决循环引用

```cpp
class SafeNode {
public:
    SafeNode(int value) : value_(value) {
        std::cout << "创建SafeNode " << value_ << std::endl;
    }
    
    ~SafeNode() {
        std::cout << "销毁SafeNode " << value_ << std::endl;
    }
    
    void SetNext(std::shared_ptr<SafeNode> next) {
        next_ = next;
    }
    
    void SetPrev(std::shared_ptr<SafeNode> prev) {
        prev_ = prev;  // weak_ptr不会增加引用计数
    }
    
    std::shared_ptr<SafeNode> GetPrev() const {
        return prev_.lock();  // 尝试获取shared_ptr
    }
    
    std::shared_ptr<SafeNode> GetNext() const {
        return next_;
    }
    
    int GetValue() const { return value_; }
    
    void PrintConnection() const {
        auto prev = prev_.lock();
        if (prev) {
            std::cout << "Node " << value_ << " 的前驱是 Node " << prev->GetValue() << std::endl;
        } else {
            std::cout << "Node " << value_ << " 没有前驱" << std::endl;
        }

        if (next_) {
            std::cout << "Node " << value_ << " 的后继是 Node " << next_->GetValue() << std::endl;
        } else {
            std::cout << "Node " << value_ << " 没有后继" << std::endl;
        }
    }

private:
    int value_;
    std::shared_ptr<SafeNode> next_;
    std::weak_ptr<SafeNode> prev_;  // 使用weak_ptr避免循环引用
};

void 安全的双向链表演示() {
    std::cout << "=== 使用weak_ptr解决循环引用 ===" << std::endl;

    {
        auto node1 = std::make_shared<SafeNode>(1);
        auto node2 = std::make_shared<SafeNode>(2);
        auto node3 = std::make_shared<SafeNode>(3);

        // 建立双向链接
        node1->SetNext(node2);
        node2->SetPrev(node1);  // weak_ptr，不增加引用计数
        node2->SetNext(node3);
        node3->SetPrev(node2);  // weak_ptr，不增加引用计数

        std::cout << "建立链接后的引用计数：" << std::endl;
        std::cout << "node1引用计数: " << node1.use_count() << std::endl;  // 1
        std::cout << "node2引用计数: " << node2.use_count() << std::endl;  // 1
        std::cout << "node3引用计数: " << node3.use_count() << std::endl;  // 1

        // 打印连接关系
        node1->PrintConnection();
        node2->PrintConnection();
        node3->PrintConnection();

        // 测试weak_ptr的安全性
        std::cout << "\n删除中间节点..." << std::endl;
        node2.reset();  // 显式释放node2

        // 尝试从node3访问前驱
        auto prev_of_node3 = node3->GetPrev();
        if (prev_of_node3) {
            std::cout << "node3的前驱仍然存在" << std::endl;
        } else {
            std::cout << "node3的前驱已经被销毁" << std::endl;
        }

        // 离开作用域时，所有节点都会被正确销毁
    }

    std::cout << "所有节点已正确销毁，无内存泄漏" << std::endl;
}
```

#### 3.4.3 weak_ptr的高级应用

```cpp
class weak_ptr高级应用 {
public:
    // 观察者模式
    class Subject {
    private:
        std::vector<std::weak_ptr<Observer>> observers_;
        std::string state_;

    public:
        void AddObserver(std::shared_ptr<Observer> observer) {
            observers_.push_back(observer);
        }

        void RemoveObserver(std::shared_ptr<Observer> observer) {
            observers_.erase(
                std::remove_if(observers_.begin(), observers_.end(),
                    [&observer](const std::weak_ptr<Observer>& weak_obs) {
                        auto obs = weak_obs.lock();
                        return !obs || obs == observer;
                    }),
                observers_.end()
            );
        }

        void SetState(const std::string& new_state) {
            state_ = new_state;
            NotifyObservers();
        }

        void NotifyObservers() {
            // 清理已失效的观察者
            auto it = std::remove_if(observers_.begin(), observers_.end(),
                [](const std::weak_ptr<Observer>& weak_obs) {
                    return weak_obs.expired();
                });
            observers_.erase(it, observers_.end());

            // 通知有效的观察者
            for (auto& weak_obs : observers_) {
                if (auto obs = weak_obs.lock()) {
                    obs->Update(state_);
                }
            }
        }

        size_t ObserverCount() const {
            return std::count_if(observers_.begin(), observers_.end(),
                [](const std::weak_ptr<Observer>& weak_obs) {
                    return !weak_obs.expired();
                });
        }
    };

    class Observer {
    public:
        Observer(const std::string& name) : name_(name) {
            std::cout << "创建观察者: " << name_ << std::endl;
        }

        ~Observer() {
            std::cout << "销毁观察者: " << name_ << std::endl;
        }

        void Update(const std::string& state) {
            std::cout << "观察者 " << name_ << " 收到状态更新: " << state << std::endl;
        }

        const std::string& GetName() const { return name_; }

    private:
        std::string name_;
    };

    void 观察者模式演示() {
        std::cout << "=== 观察者模式演示 ===" << std::endl;

        Subject subject;

        {
            auto observer1 = std::make_shared<Observer>("观察者1");
            auto observer2 = std::make_shared<Observer>("观察者2");
            auto observer3 = std::make_shared<Observer>("观察者3");

            // 注册观察者
            subject.AddObserver(observer1);
            subject.AddObserver(observer2);
            subject.AddObserver(observer3);

            std::cout << "当前观察者数量: " << subject.ObserverCount() << std::endl;

            // 状态变化，通知所有观察者
            subject.SetState("状态A");

            {
                // observer2离开作用域
                observer2.reset();
                std::cout << "\nobserver2已销毁" << std::endl;

                // 再次状态变化
                subject.SetState("状态B");
                std::cout << "当前观察者数量: " << subject.ObserverCount() << std::endl;
            }

            // 最后的状态变化
            subject.SetState("状态C");
            std::cout << "当前观察者数量: " << subject.ObserverCount() << std::endl;
        }

        // 所有观察者都离开作用域
        subject.SetState("状态D");
        std::cout << "当前观察者数量: " << subject.ObserverCount() << std::endl;
    }

    // 缓存系统
    class CacheSystem {
    private:
        std::map<int, std::weak_ptr<Resource>> cache_;

    public:
        std::shared_ptr<Resource> GetResource(int id) {
            // 先检查缓存
            auto it = cache_.find(id);
            if (it != cache_.end()) {
                if (auto cached = it->second.lock()) {
                    std::cout << "从缓存获取资源 " << id << std::endl;
                    return cached;
                } else {
                    // 缓存中的资源已失效，清理
                    cache_.erase(it);
                }
            }

            // 创建新资源并缓存
            auto resource = std::make_shared<Resource>(id);
            cache_[id] = resource;
            std::cout << "创建新资源 " << id << " 并加入缓存" << std::endl;
            return resource;
        }

        void CleanupCache() {
            auto it = cache_.begin();
            while (it != cache_.end()) {
                if (it->second.expired()) {
                    std::cout << "清理失效的缓存项 " << it->first << std::endl;
                    it = cache_.erase(it);
                } else {
                    ++it;
                }
            }
        }

        size_t CacheSize() const {
            return std::count_if(cache_.begin(), cache_.end(),
                [](const auto& pair) {
                    return !pair.second.expired();
                });
        }
    };

    void 缓存系统演示() {
        std::cout << "\n=== 缓存系统演示 ===" << std::endl;

        CacheSystem cache;

        {
            // 获取资源，会创建新的
            auto resource1 = cache.GetResource(1);
            auto resource2 = cache.GetResource(2);

            // 再次获取相同资源，会从缓存返回
            auto resource1_again = cache.GetResource(1);

            std::cout << "缓存大小: " << cache.CacheSize() << std::endl;
            std::cout << "resource1 == resource1_again: "
                      << (resource1 == resource1_again ? "是" : "否") << std::endl;

            {
                // resource2离开作用域
                resource2.reset();
                std::cout << "\nresource2已释放" << std::endl;

                // 清理缓存
                cache.CleanupCache();
                std::cout << "清理后缓存大小: " << cache.CacheSize() << std::endl;
            }
        }

        // 所有资源都离开作用域
        cache.CleanupCache();
        std::cout << "最终缓存大小: " << cache.CacheSize() << std::endl;
    }
};
```

### 3.5 智能指针的选择指南

#### 3.5.1 选择决策树

```mermaid
graph TD
    A["🤔 需要动态内存分配？"] --> B{"能用栈内存吗？"}
    B -->|能| C["✅ 使用栈变量<br/>int x = 42;"]
    B -->|不能| D{"只有一个拥有者？"}

    D -->|是| E["🔑 使用unique_ptr<br/>auto ptr = make_unique<T>();"]
    D -->|否| F{"需要共享所有权？"}

    F -->|是| G["🏠 使用shared_ptr<br/>auto ptr = make_shared<T>();"]
    F -->|否| H{"只是观察，不拥有？"}

    H -->|是| I["👀 使用weak_ptr<br/>或原始指针"]
    H -->|否| J["🔄 重新考虑设计"]

    classDef good fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef warning fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef question fill:#e3f2fd,stroke:#1976d2,stroke-width:2px

    class C,E,G,I good
    class J warning
    class A,B,D,F,H question
```

#### 3.5.2 实际应用场景对比

```cpp
class 智能指针选择指南 {
public:
    // 场景1：工厂函数
    static std::unique_ptr<Resource> CreateResource(int id) {
        // ✅ 工厂函数返回unique_ptr，明确所有权转移
        if (id <= 0) return nullptr;
        return std::make_unique<Resource>(id);
    }

    // 场景2：容器存储
    void 容器存储场景() {
        // ✅ 容器中存储unique_ptr，避免拷贝开销
        std::vector<std::unique_ptr<Resource>> resources;

        for (int i = 1; i <= 5; ++i) {
            resources.push_back(CreateResource(i));
        }

        // 移动语义，高效转移所有权
        auto moved_resource = std::move(resources[0]);
        resources[0] = nullptr;  // 原位置变为空
    }

    // 场景3：共享资源
    class ResourceManager {
    private:
        std::shared_ptr<Resource> shared_resource_;

    public:
        ResourceManager() : shared_resource_(std::make_shared<Resource>(100)) {}

        // ✅ 返回shared_ptr，允许多个客户端共享
        std::shared_ptr<Resource> GetSharedResource() {
            return shared_resource_;
        }

        // ✅ 返回weak_ptr，允许观察但不影响生命周期
        std::weak_ptr<Resource> GetWeakResource() {
            return shared_resource_;
        }
    };

    // 场景4：回调系统
    class CallbackSystem {
    private:
        std::vector<std::weak_ptr<CallbackHandler>> handlers_;

    public:
        void RegisterHandler(std::shared_ptr<CallbackHandler> handler) {
            handlers_.push_back(handler);
        }

        void TriggerCallbacks(const std::string& event) {
            // ✅ 使用weak_ptr避免延长对象生命周期
            auto it = handlers_.begin();
            while (it != handlers_.end()) {
                if (auto handler = it->lock()) {
                    handler->HandleEvent(event);
                    ++it;
                } else {
                    // 自动清理失效的处理器
                    it = handlers_.erase(it);
                }
            }
        }
    };

    class CallbackHandler {
    public:
        CallbackHandler(const std::string& name) : name_(name) {}

        void HandleEvent(const std::string& event) {
            std::cout << "处理器 " << name_ << " 处理事件: " << event << std::endl;
        }

    private:
        std::string name_;
    };

    void 回调系统演示() {
        std::cout << "=== 回调系统演示 ===" << std::endl;

        CallbackSystem system;

        {
            auto handler1 = std::make_shared<CallbackHandler>("处理器1");
            auto handler2 = std::make_shared<CallbackHandler>("处理器2");

            system.RegisterHandler(handler1);
            system.RegisterHandler(handler2);

            system.TriggerCallbacks("事件A");

            // handler1离开作用域
            handler1.reset();

            system.TriggerCallbacks("事件B");  // 只有handler2响应
        }

        system.TriggerCallbacks("事件C");  // 没有处理器响应
    }
};
```

#### 3.5.3 性能考虑和最佳实践

```cpp
class 智能指针性能优化 {
public:
    void 性能对比测试() {
        const int 测试次数 = 1000000;

        // 测试原始指针性能（基准）
        auto 开始 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 测试次数; ++i) {
            int* ptr = new int(i);
            *ptr += 1;
            delete ptr;
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto 原始指针时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        // 测试unique_ptr性能
        开始 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 测试次数; ++i) {
            auto ptr = std::make_unique<int>(i);
            *ptr += 1;
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto unique_ptr时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        // 测试shared_ptr性能
        开始 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 测试次数; ++i) {
            auto ptr = std::make_shared<int>(i);
            *ptr += 1;
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto shared_ptr时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        // 输出结果
        std::cout << "=== 智能指针性能对比 ===" << std::endl;
        std::cout << "原始指针时间: " << 原始指针时间.count() << " 微秒 (基准)" << std::endl;
        std::cout << "unique_ptr时间: " << unique_ptr时间.count() << " 微秒 (比例: "
                  << (double)unique_ptr时间.count() / 原始指针时间.count() << ")" << std::endl;
        std::cout << "shared_ptr时间: " << shared_ptr时间.count() << " 微秒 (比例: "
                  << (double)shared_ptr时间.count() / 原始指针时间.count() << ")" << std::endl;
    }

    // 最佳实践示例
    void 最佳实践演示() {
        std::cout << "\n=== 智能指针最佳实践 ===" << std::endl;

        // ✅ 优先使用make_unique和make_shared
        auto good_unique = std::make_unique<Resource>(1);
        auto good_shared = std::make_shared<Resource>(2);

        // ❌ 避免直接使用new
        // std::unique_ptr<Resource> bad_unique(new Resource(3));  // 不推荐

        // ✅ 工厂函数返回智能指针
        auto factory_resource = CreateResource(4);

        // ✅ 容器中存储智能指针
        std::vector<std::unique_ptr<Resource>> resource_container;
        resource_container.push_back(std::move(factory_resource));

        // ✅ 函数参数使用原始指针或引用
        ProcessResource(good_unique.get());  // 传递原始指针
        ProcessResourceRef(*good_shared);    // 传递引用

        // ✅ 只在需要转移或共享所有权时传递智能指针
        TransferOwnership(std::move(good_unique));  // 转移所有权
        ShareResource(good_shared);                 // 共享所有权
    }

private:
    void ProcessResource(Resource* resource) {
        if (resource) {
            resource->DoWork();
        }
    }

    void ProcessResourceRef(Resource& resource) {
        resource.DoWork();
    }

    void TransferOwnership(std::unique_ptr<Resource> resource) {
        if (resource) {
            std::cout << "接收到所有权转移的资源" << std::endl;
            resource->DoWork();
        }
    }

    void ShareResource(std::shared_ptr<Resource> resource) {
        if (resource) {
            std::cout << "共享资源，当前引用计数: " << resource.use_count() << std::endl;
            resource->DoWork();
        }
    }
};
```

### 3.5 智能指针创建函数完整参考

> **📋 本节总结**：全面掌握所有智能指针创建函数，包括make_unique、make_shared、allocate_shared等

#### 🎯 **所有智能指针创建函数总览**

##### 1. make_unique (C++14)
- ✅ **异常安全**：避免函数参数求值顺序导致的内存泄漏
- ✅ **代码简洁**：自动类型推导，减少重复代码
- ✅ **零开销**：与手动new/delete性能相同
- ✅ **数组支持**：`make_unique<T[]>(size)`自动调用delete[]
- ❌ **限制**：不支持自定义删除器

##### 2. make_shared (C++11)
- ✅ **内存效率**：单次分配对象和控制块，减少内存碎片
- ✅ **性能优势**：比传统shared_ptr创建快20-50%
- ✅ **缓存友好**：对象和控制块连续存储，提高访问效率
- ✅ **异常安全**：与make_unique相同的异常安全保证
- ❌ **限制**：不支持自定义删除器，内存释放可能延迟

##### 3. allocate_shared (C++11)
- ✅ **自定义分配器**：可以使用自定义内存分配器
- ✅ **内存效率**：与make_shared相同的单次分配优势
- ✅ **灵活性**：支持内存池、对齐分配等高级需求
- ⚠️ **复杂性**：需要理解分配器概念

##### 4. make_shared_for_overwrite (C++20)
- ✅ **性能优化**：不进行值初始化，适用于大数组
- ✅ **内存效率**：与make_shared相同的分配优势
- ⚠️ **使用场景**：主要用于后续会完全重写的大对象

##### 5. make_unique_for_overwrite (C++20)
- ✅ **性能优化**：不进行值初始化
- ✅ **适用场景**：大数组或后续会完全重写的对象
- ⚠️ **注意**：对象处于未初始化状态

#### 💻 **详细使用示例**

```cpp
#include <memory>
#include <vector>
#include <iostream>

class 智能指针创建函数演示 {
public:
    void 演示所有创建函数() {
        std::cout << "=== 所有智能指针创建函数演示 ===" << std::endl;

        // 1. make_unique (C++14)
        演示make_unique();

        // 2. make_shared (C++11)
        演示make_shared();

        // 3. allocate_shared (C++11)
        演示allocate_shared();

        // 4. C++20新特性
        #if __cplusplus >= 202002L
        演示C++20新特性();
        #endif
    }

private:
    void 演示make_unique() {
        std::cout << "\n--- make_unique演示 ---" << std::endl;

        // 基本对象
        auto obj = std::make_unique<Resource>(100);

        // 数组
        auto arr = std::make_unique<int[]>(10);
        for (int i = 0; i < 10; ++i) {
            arr[i] = i * i;
        }

        std::cout << "make_unique: 创建对象和数组完成" << std::endl;
    }

    void 演示make_shared() {
        std::cout << "\n--- make_shared演示 ---" << std::endl;

        // 基本对象
        auto obj = std::make_shared<Resource>(200);

        // 多个shared_ptr共享
        auto obj2 = obj;
        std::cout << "引用计数: " << obj.use_count() << std::endl;

        std::cout << "make_shared: 创建共享对象完成" << std::endl;
    }

    void 演示allocate_shared() {
        std::cout << "\n--- allocate_shared演示 ---" << std::endl;

        // 使用标准分配器
        std::allocator<Resource> alloc;
        auto obj = std::allocate_shared<Resource>(alloc, 300);

        // 自定义分配器示例
        class 自定义分配器 {
        public:
            using value_type = Resource;

            Resource* allocate(std::size_t n) {
                std::cout << "自定义分配器分配 " << n << " 个对象" << std::endl;
                return static_cast<Resource*>(::operator new(n * sizeof(Resource)));
            }

            void deallocate(Resource* p, std::size_t n) {
                std::cout << "自定义分配器释放 " << n << " 个对象" << std::endl;
                ::operator delete(p);
            }

            template<typename... Args>
            void construct(Resource* p, Args&&... args) {
                new(p) Resource(std::forward<Args>(args)...);
            }

            void destroy(Resource* p) {
                p->~Resource();
            }
        };

        自定义分配器 custom_alloc;
        auto custom_obj = std::allocate_shared<Resource>(custom_alloc, 400);

        std::cout << "allocate_shared: 使用自定义分配器完成" << std::endl;
    }

    #if __cplusplus >= 202002L
    void 演示C++20新特性() {
        std::cout << "\n--- C++20新特性演示 ---" << std::endl;

        // make_unique_for_overwrite - 不初始化
        auto uninit_arr = std::make_unique_for_overwrite<int[]>(1000);
        std::cout << "make_unique_for_overwrite: 创建未初始化数组" << std::endl;

        // 手动初始化（如果需要）
        for (int i = 0; i < 1000; ++i) {
            uninit_arr[i] = i;
        }

        // make_shared_for_overwrite - 不初始化
        auto uninit_shared = std::make_shared_for_overwrite<std::array<int, 1000>>();
        std::cout << "make_shared_for_overwrite: 创建未初始化共享对象" << std::endl;

        // make_shared支持数组 (C++20)
        auto shared_arr = std::make_shared<int[]>(100);
        for (int i = 0; i < 100; ++i) {
            shared_arr[i] = i * 2;
        }
        std::cout << "make_shared数组支持: 创建共享数组完成" << std::endl;
    }
    #endif
};
```

#### 📊 **性能对比数据**

| 创建方式 | 内存分配次数 | 相对性能 | 缓存友好性 | 异常安全 | C++版本 |
|---------|-------------|----------|-----------|----------|---------|
| `make_unique` | 1次 | 100% (基准) | ⭐⭐⭐ | ✅ | C++14 |
| `unique_ptr(new)` | 1次 | ~100% | ⭐⭐⭐ | ❌ | C++11 |
| `make_shared` | 1次 | 100% (基准) | ⭐⭐⭐⭐⭐ | ✅ | C++11 |
| `shared_ptr(new)` | 2次 | ~70-80% | ⭐⭐ | ❌ | C++11 |
| `allocate_shared` | 1次 | ~95-100% | ⭐⭐⭐⭐⭐ | ✅ | C++11 |
| `make_*_for_overwrite` | 1次 | ~110-120% | ⭐⭐⭐⭐⭐ | ✅ | C++20 |

#### 🔧 **完整使用决策树**

```
需要智能指针？
├─ 是 → 独占所有权？
│   ├─ 是 → 需要自定义删除器？
│   │   ├─ 否 → 大对象且不需要初始化？
│   │   │   ├─ 是 → ✅ make_unique_for_overwrite (C++20)
│   │   │   └─ 否 → ✅ make_unique
│   │   └─ 是 → ⚠️ unique_ptr(new T, deleter)
│   └─ 否 → 共享所有权？
│       ├─ 是 → 需要自定义分配器？
│       │   ├─ 是 → ✅ allocate_shared
│       │   └─ 否 → 需要自定义删除器？
│       │       ├─ 否 → 大对象且不需要初始化？
│       │       │   ├─ 是 → ✅ make_shared_for_overwrite (C++20)
│       │       │   └─ 否 → ✅ make_shared
│       │       └─ 是 → ⚠️ shared_ptr(new T, deleter)
│       └─ 否 → 🤔 重新考虑设计
└─ 否 → ✅ 使用栈对象或容器
```

#### 🎯 **版本兼容性指南**

| 函数 | C++11 | C++14 | C++17 | C++20 | 推荐度 |
|------|-------|-------|-------|-------|--------|
| `make_shared` | ✅ | ✅ | ✅ | ✅ | ⭐⭐⭐⭐⭐ |
| `allocate_shared` | ✅ | ✅ | ✅ | ✅ | ⭐⭐⭐ |
| `make_unique` | ❌ | ✅ | ✅ | ✅ | ⭐⭐⭐⭐⭐ |
| `make_shared<T[]>` | ❌ | ❌ | ❌ | ✅ | ⭐⭐⭐⭐ |
| `make_*_for_overwrite` | ❌ | ❌ | ❌ | ✅ | ⭐⭐⭐ |

#### 🚀 **特殊用途指南**

```cpp
// 🎯 常规用途 - 99%的情况
auto obj = std::make_unique<MyClass>(args);     // 独占
auto shared = std::make_shared<MyClass>(args);  // 共享

// 🏭 内存池/自定义分配器
std::allocator<MyClass> alloc;
auto pooled = std::allocate_shared<MyClass>(alloc, args);

// ⚡ 高性能场景 (C++20)
auto fast_array = std::make_unique_for_overwrite<int[]>(1000000);
auto fast_shared = std::make_shared_for_overwrite<BigObject>();

// 🔧 特殊需求
auto custom_deleter = [](MyClass* p) { /* 特殊清理 */ delete p; };
std::unique_ptr<MyClass, decltype(custom_deleter)> special(
    new MyClass(args), custom_deleter);
```

#### 💡 **完整最佳实践清单**

##### ✅ 推荐做法
```cpp
// 1. 基础创建 - 优先使用make函数
auto unique_obj = std::make_unique<MyClass>(args);
auto shared_obj = std::make_shared<MyClass>(args);

// 2. 数组创建
auto unique_arr = std::make_unique<int[]>(100);        // C++14+
#if __cplusplus >= 202002L
auto shared_arr = std::make_shared<int[]>(100);        // C++20+
#else
auto shared_vec = std::make_shared<std::vector<int>>(100); // C++17兼容
#endif

// 3. 高性能场景 (C++20)
#if __cplusplus >= 202002L
auto fast_unique = std::make_unique_for_overwrite<int[]>(1000000);
auto fast_shared = std::make_shared_for_overwrite<BigArray>();
#endif

// 4. 自定义分配器
std::allocator<MyClass> alloc;
auto allocated_obj = std::allocate_shared<MyClass>(alloc, args);

// 5. 工厂函数模式
template<typename T, typename... Args>
std::unique_ptr<T> CreateUnique(Args&&... args) {
    return std::make_unique<T>(std::forward<Args>(args)...);
}

template<typename T, typename... Args>
std::shared_ptr<T> CreateShared(Args&&... args) {
    return std::make_shared<T>(std::forward<Args>(args)...);
}

// 6. 容器存储智能指针
std::vector<std::unique_ptr<MyClass>> unique_objects;
unique_objects.push_back(std::make_unique<MyClass>(1));

std::vector<std::shared_ptr<MyClass>> shared_objects;
shared_objects.push_back(std::make_shared<MyClass>(2));
```

##### ⚠️ 特殊情况的正确处理
```cpp
// 1. 需要自定义删除器时
auto file_deleter = [](FILE* f) { if(f) fclose(f); };
std::unique_ptr<FILE, decltype(file_deleter)> file(
    fopen("test.txt", "r"), file_deleter);

// 2. 私有构造函数的处理
class PrivateClass {
private:
    PrivateClass(int x) : value_(x) {}

public:
    // 使用友元函数
    friend std::unique_ptr<PrivateClass> CreatePrivate(int x);

    // 或使用公共包装结构
    struct EnableMakeShared : public PrivateClass {
        EnableMakeShared(int x) : PrivateClass(x) {}
    };

    static std::shared_ptr<PrivateClass> Create(int x) {
        return std::make_shared<EnableMakeShared>(x);
    }

private:
    int value_;
};

std::unique_ptr<PrivateClass> CreatePrivate(int x) {
    return std::make_unique<PrivateClass>(x);  // 友元可以访问
}
```

##### ❌ 避免做法
```cpp
// 1. 避免裸露的new
MyClass* raw = new MyClass(args);  // 容易泄漏

// 2. 避免传统智能指针构造（除非必要）
std::unique_ptr<MyClass> bad1(new MyClass(args));  // 异常不安全
std::shared_ptr<MyClass> bad2(new MyClass(args));  // 性能差

// 3. 避免不必要的get()调用
auto ptr = std::make_unique<MyClass>(args);
MyClass* raw = ptr.get();  // 除非必要，否则避免

// 4. 避免混用不同的创建方式
auto ptr1 = std::make_shared<MyClass>(1);
auto ptr2 = std::shared_ptr<MyClass>(new MyClass(2));  // 不一致

// 5. 避免在不支持的C++版本中使用新特性
#if __cplusplus < 201402L
// auto ptr = std::make_unique<MyClass>(args);  // C++14才支持
#endif
```

##### 📋 **版本兼容性最佳实践**
```cpp
// C++11兼容的写法
#if __cplusplus >= 201402L
    auto ptr = std::make_unique<MyClass>(args);
#else
    std::unique_ptr<MyClass> ptr(new MyClass(args));  // C++11回退
#endif

// C++20特性的安全使用
#if __cplusplus >= 202002L
    auto fast_arr = std::make_unique_for_overwrite<int[]>(size);
#else
    auto fast_arr = std::make_unique<int[]>(size);  // 回退到普通make_unique
#endif
```

### 3.6 Part 3 总结

> **🎯 Part 3 核心要点**：

#### 技术掌握清单

| 智能指针类型 | 掌握程度 | 关键特性 | 适用场景 |
|-------------|----------|----------|----------|
| **unique_ptr** | ⭐⭐⭐ | 独占所有权，零开销，可移动 | 单一拥有者，工厂函数，容器存储 |
| **shared_ptr** | ⭐⭐⭐ | 共享所有权，引用计数，线程安全 | 多个拥有者，资源共享 |
| **weak_ptr** | ⭐⭐ | 观察者模式，打破循环引用 | 缓存系统，观察者模式，回调系统 |

#### 设计原则

1. **所有权明确原则**：清楚谁拥有资源，谁负责释放
2. **最小权限原则**：只给予必要的访问权限
3. **RAII原则**：资源获取即初始化，自动管理生命周期
4. **性能优先原则**：在满足需求的前提下选择开销最小的方案

#### 选择指南

```cpp
// 🎯 智能指针选择的黄金法则
if (只有一个拥有者) {
    使用unique_ptr;  // 零开销，明确所有权
} else if (需要共享所有权) {
    使用shared_ptr;  // 引用计数，自动管理
} else if (只是观察，不拥有) {
    使用weak_ptr;   // 避免循环引用
} else {
    重新考虑设计;    // 可能设计有问题
}
```

#### 最佳实践总结

- ✅ 优先使用`make_unique`和`make_shared`
- ✅ 工厂函数返回智能指针
- ✅ 容器中存储智能指针
- ✅ 函数参数使用原始指针或引用（不转移所有权时）
- ✅ 使用`weak_ptr`打破循环引用
- ❌ 避免直接使用`new`和`delete`
- ❌ 避免混用智能指针和原始指针管理同一资源

**接下来**：Part 4将深入探讨STL容器的内存管理机制，了解如何高效使用标准库容器。

---

## Part 4: 容器与内存——STL容器的内存管理机制

> **学习目标**：深入理解STL容器的内存分配策略，掌握不同容器的性能特点，学会根据使用场景选择合适的容器，优化内存使用效率。

### 4.1 STL容器内存管理概览

#### 4.1.1 容器分类与内存特性

```mermaid
graph TD
    A["📦 STL容器"] --> B["🔗 序列容器<br/>Sequential Containers"]
    A --> C["🗂️ 关联容器<br/>Associative Containers"]
    A --> D["🏷️ 无序容器<br/>Unordered Containers"]
    A --> E["🔧 容器适配器<br/>Container Adapters"]

    B --> B1["vector<br/>动态数组"]
    B --> B2["deque<br/>双端队列"]
    B --> B3["list<br/>双向链表"]
    B --> B4["forward_list<br/>单向链表"]
    B --> B5["array<br/>固定数组"]

    C --> C1["set/multiset<br/>红黑树"]
    C --> C2["map/multimap<br/>红黑树"]

    D --> D1["unordered_set<br/>哈希表"]
    D --> D2["unordered_map<br/>哈希表"]

    E --> E1["stack<br/>栈适配器"]
    E --> E2["queue<br/>队列适配器"]
    E --> E3["priority_queue<br/>优先队列"]

    classDef sequential fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef associative fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef unordered fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef adapter fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px

    class B,B1,B2,B3,B4,B5 sequential
    class C,C1,C2 associative
    class D,D1,D2 unordered
    class E,E1,E2,E3 adapter
```

#### 4.1.2 容器内存特性对比表

| 容器类型 | 内存布局 | 分配策略 | 插入性能 | 查找性能 | 内存开销 |
|---------|----------|----------|----------|----------|----------|
| **vector** | 连续内存 | 指数增长 | O(1)尾部 | O(n)线性 | 低 |
| **deque** | 分块连续 | 块状分配 | O(1)两端 | O(n)线性 | 中等 |
| **list** | 分散节点 | 按需分配 | O(1)任意位置 | O(n)线性 | 高 |
| **set/map** | 树形结构 | 按需分配 | O(log n) | O(log n) | 中等 |
| **unordered_set/map** | 哈希表 | 动态扩容 | O(1)平均 | O(1)平均 | 中等 |

### 4.2 vector：动态数组的内存管理

#### 4.2.1 vector的内存增长策略

```cpp
#include <vector>
#include <iostream>

class vector内存分析 {
public:
    void 内存增长演示() {
        std::cout << "=== vector内存增长演示 ===" << std::endl;

        std::vector<int> vec;

        std::cout << "初始状态：" << std::endl;
        打印vector信息(vec);

        // 逐步添加元素，观察内存增长
        for (int i = 1; i <= 20; ++i) {
            vec.push_back(i);

            // 在容量变化时打印信息
            static size_t last_capacity = 0;
            if (vec.capacity() != last_capacity) {
                std::cout << "\n添加第" << i << "个元素后：" << std::endl;
                打印vector信息(vec);
                last_capacity = vec.capacity();
            }
        }
    }

    void 预分配内存演示() {
        std::cout << "\n=== 预分配内存演示 ===" << std::endl;

        // 不预分配的情况
        auto 开始 = std::chrono::high_resolution_clock::now();
        std::vector<int> vec1;
        for (int i = 0; i < 100000; ++i) {
            vec1.push_back(i);
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto 时间1 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        // 预分配的情况
        开始 = std::chrono::high_resolution_clock::now();
        std::vector<int> vec2;
        vec2.reserve(100000);  // 预分配内存
        for (int i = 0; i < 100000; ++i) {
            vec2.push_back(i);
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto 时间2 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        std::cout << "不预分配时间：" << 时间1.count() << " 微秒" << std::endl;
        std::cout << "预分配时间：" << 时间2.count() << " 微秒" << std::endl;
        std::cout << "性能提升：" << (double)时间1.count() / 时间2.count() << " 倍" << std::endl;
    }

    void 内存释放演示() {
        std::cout << "\n=== vector内存释放演示 ===" << std::endl;

        std::vector<int> vec(100000, 42);
        std::cout << "创建大vector后：" << std::endl;
        打印vector信息(vec);

        // clear()只清空元素，不释放内存
        vec.clear();
        std::cout << "\nclear()后：" << std::endl;
        打印vector信息(vec);

        // shrink_to_fit()释放多余内存
        vec.shrink_to_fit();
        std::cout << "\nshrink_to_fit()后：" << std::endl;
        打印vector信息(vec);

        // swap技巧强制释放内存
        vec.assign(50000, 1);
        std::cout << "\n重新填充后：" << std::endl;
        打印vector信息(vec);

        std::vector<int>().swap(vec);  // swap技巧
        std::cout << "\nswap技巧后：" << std::endl;
        打印vector信息(vec);
    }

    void 缓存友好性演示() {
        std::cout << "\n=== vector缓存友好性演示 ===" << std::endl;

        const size_t 大小 = 1000000;

        // vector：连续内存，缓存友好
        std::vector<int> vec(大小);
        std::iota(vec.begin(), vec.end(), 0);

        auto 开始 = std::chrono::high_resolution_clock::now();
        long long 总和1 = 0;
        for (const auto& value : vec) {
            总和1 += value;
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto vector时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        // list：分散内存，缓存不友好
        std::list<int> lst;
        for (size_t i = 0; i < 大小; ++i) {
            lst.push_back(static_cast<int>(i));
        }

        开始 = std::chrono::high_resolution_clock::now();
        long long 总和2 = 0;
        for (const auto& value : lst) {
            总和2 += value;
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto list时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        std::cout << "vector遍历时间：" << vector时间.count() << " 微秒" << std::endl;
        std::cout << "list遍历时间：" << list时间.count() << " 微秒" << std::endl;
        std::cout << "vector性能优势：" << (double)list时间.count() / vector时间.count() << " 倍" << std::endl;
    }

private:
    void 打印vector信息(const std::vector<int>& vec) {
        std::cout << "大小：" << vec.size()
                  << "，容量：" << vec.capacity()
                  << "，内存地址：" << (void*)vec.data() << std::endl;
    }
};
```

#### 4.2.2 vector的高级内存技巧

```cpp
class vector高级技巧 {
public:
    // 自定义分配器
    template<typename T>
    class 调试分配器 {
    public:
        using value_type = T;

        调试分配器() = default;

        template<typename U>
        调试分配器(const 调试分配器<U>&) {}

        T* allocate(size_t n) {
            std::cout << "分配 " << n << " 个 " << typeid(T).name() << " 对象" << std::endl;
            return static_cast<T*>(std::malloc(n * sizeof(T)));
        }

        void deallocate(T* ptr, size_t n) {
            std::cout << "释放 " << n << " 个 " << typeid(T).name() << " 对象" << std::endl;
            std::free(ptr);
        }

        template<typename U>
        bool operator==(const 调试分配器<U>&) const { return true; }

        template<typename U>
        bool operator!=(const 调试分配器<U>&) const { return false; }
    };

    void 自定义分配器演示() {
        std::cout << "=== 自定义分配器演示 ===" << std::endl;

        std::vector<int, 调试分配器<int>> debug_vec;

        std::cout << "添加元素：" << std::endl;
        for (int i = 0; i < 10; ++i) {
            debug_vec.push_back(i);
        }

        std::cout << "\n清空vector：" << std::endl;
        debug_vec.clear();

        std::cout << "\nvector销毁：" << std::endl;
        // 离开作用域时自动调用析构函数
    }

    // 内存池优化
    class 简单内存池 {
    private:
        std::vector<char> 内存块;
        size_t 当前位置;

    public:
        简单内存池(size_t 大小) : 内存块(大小), 当前位置(0) {
            std::cout << "创建内存池，大小：" << 大小 << " 字节" << std::endl;
        }

        template<typename T>
        T* 分配(size_t 数量 = 1) {
            size_t 需要大小 = sizeof(T) * 数量;

            if (当前位置 + 需要大小 > 内存块.size()) {
                throw std::bad_alloc();
            }

            T* 结果 = reinterpret_cast<T*>(&内存块[当前位置]);
            当前位置 += 需要大小;

            std::cout << "从内存池分配 " << 需要大小 << " 字节" << std::endl;
            return 结果;
        }

        void 重置() {
            当前位置 = 0;
            std::cout << "内存池已重置" << std::endl;
        }

        size_t 已使用() const { return 当前位置; }
        size_t 剩余() const { return 内存块.size() - 当前位置; }
    };

    void 内存池演示() {
        std::cout << "\n=== 内存池演示 ===" << std::endl;

        简单内存池 池(1024);  // 1KB内存池

        try {
            // 从内存池分配不同类型的对象
            int* 整数数组 = 池.分配<int>(10);
            double* 浮点数组 = 池.分配<double>(5);
            char* 字符数组 = 池.分配<char>(100);

            // 使用分配的内存
            for (int i = 0; i < 10; ++i) {
                整数数组[i] = i;
            }

            std::cout << "内存池使用情况：已使用 " << 池.已使用()
                      << " 字节，剩余 " << 池.剩余() << " 字节" << std::endl;

            // 重置内存池
            池.重置();

        } catch (const std::bad_alloc& e) {
            std::cout << "内存池空间不足：" << e.what() << std::endl;
        }
    }

    // 对象池模式
    template<typename T>
    class 对象池 {
    private:
        std::vector<std::unique_ptr<T>> 可用对象;
        std::vector<std::unique_ptr<T>> 使用中对象;

    public:
        template<typename... Args>
        T* 获取对象(Args&&... args) {
            std::unique_ptr<T> 对象;

            if (!可用对象.empty()) {
                对象 = std::move(可用对象.back());
                可用对象.pop_back();
                std::cout << "从对象池获取对象" << std::endl;
            } else {
                对象 = std::make_unique<T>(std::forward<Args>(args)...);
                std::cout << "创建新对象" << std::endl;
            }

            T* 原始指针 = 对象.get();
            使用中对象.push_back(std::move(对象));
            return 原始指针;
        }

        void 归还对象(T* 对象指针) {
            auto it = std::find_if(使用中对象.begin(), 使用中对象.end(),
                [对象指针](const std::unique_ptr<T>& ptr) {
                    return ptr.get() == 对象指针;
                });

            if (it != 使用中对象.end()) {
                可用对象.push_back(std::move(*it));
                使用中对象.erase(it);
                std::cout << "对象已归还到池中" << std::endl;
            }
        }

        size_t 可用数量() const { return 可用对象.size(); }
        size_t 使用中数量() const { return 使用中对象.size(); }
    };

    void 对象池演示() {
        std::cout << "\n=== 对象池演示 ===" << std::endl;

        对象池<Resource> 池;

        // 获取对象
        Resource* obj1 = 池.获取对象(1);
        Resource* obj2 = 池.获取对象(2);
        Resource* obj3 = 池.获取对象(3);

        std::cout << "池状态：可用 " << 池.可用数量()
                  << "，使用中 " << 池.使用中数量() << std::endl;

        // 使用对象
        obj1->DoWork();
        obj2->DoWork();

        // 归还对象
        池.归还对象(obj1);
        池.归还对象(obj2);

        std::cout << "归还后池状态：可用 " << 池.可用数量()
                  << "，使用中 " << 池.使用中数量() << std::endl;

        // 重新获取对象（会复用之前的对象）
        Resource* obj4 = 池.获取对象(4);
        obj4->DoWork();

        // 清理
        池.归还对象(obj3);
        池.归还对象(obj4);
    }
};
```

### 4.3 其他序列容器的内存特性

#### 4.3.1 deque：双端队列的分块内存

```cpp
class deque内存分析 {
public:
    void deque结构演示() {
        std::cout << "=== deque内存结构演示 ===" << std::endl;

        std::deque<int> dq;

        // 从两端添加元素
        for (int i = 0; i < 10; ++i) {
            dq.push_back(i);      // 尾部添加
            dq.push_front(-i-1);  // 头部添加
        }

        std::cout << "deque内容：";
        for (const auto& value : dq) {
            std::cout << value << " ";
        }
        std::cout << std::endl;

        // deque的随机访问
        std::cout << "随机访问测试：" << std::endl;
        std::cout << "dq[0] = " << dq[0] << std::endl;
        std::cout << "dq[10] = " << dq[10] << std::endl;
        std::cout << "dq[19] = " << dq[19] << std::endl;
    }

    void deque_vs_vector性能对比() {
        std::cout << "\n=== deque vs vector 性能对比 ===" << std::endl;

        const int 测试次数 = 100000;

        // 测试头部插入性能
        auto 开始 = std::chrono::high_resolution_clock::now();
        std::vector<int> vec;
        for (int i = 0; i < 测试次数; ++i) {
            vec.insert(vec.begin(), i);  // vector头部插入，O(n)
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto vector时间 = std::chrono::duration_cast<std::chrono::milliseconds>(结束 - 开始);

        开始 = std::chrono::high_resolution_clock::now();
        std::deque<int> dq;
        for (int i = 0; i < 测试次数; ++i) {
            dq.push_front(i);  // deque头部插入，O(1)
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto deque时间 = std::chrono::duration_cast<std::chrono::milliseconds>(结束 - 开始);

        std::cout << "头部插入 " << 测试次数 << " 个元素：" << std::endl;
        std::cout << "vector时间：" << vector时间.count() << " 毫秒" << std::endl;
        std::cout << "deque时间：" << deque时间.count() << " 毫秒" << std::endl;
        std::cout << "deque性能优势：" << (double)vector时间.count() / deque时间.count() << " 倍" << std::endl;
    }
};
```

#### 4.3.2 list和forward_list：链表的内存管理

```cpp
class 链表内存分析 {
public:
    void list内存特性演示() {
        std::cout << "=== list内存特性演示 ===" << std::endl;

        std::list<int> lst;

        // list的插入不会使迭代器失效
        auto it = lst.begin();
        for (int i = 0; i < 10; ++i) {
            lst.push_back(i);
        }

        it = lst.begin();
        std::advance(it, 5);  // 指向第6个元素

        std::cout << "插入前，迭代器指向：" << *it << std::endl;

        // 在中间插入元素
        lst.insert(it, 999);

        std::cout << "插入后，迭代器仍然指向：" << *it << std::endl;

        // 打印整个list
        std::cout << "list内容：";
        for (const auto& value : lst) {
            std::cout << value << " ";
        }
        std::cout << std::endl;
    }

    void 链表vs数组性能对比() {
        std::cout << "\n=== 链表 vs 数组 性能对比 ===" << std::endl;

        const size_t 大小 = 100000;

        // 创建测试数据
        std::vector<int> vec(大小);
        std::list<int> lst;

        std::iota(vec.begin(), vec.end(), 0);
        for (size_t i = 0; i < 大小; ++i) {
            lst.push_back(static_cast<int>(i));
        }

        // 测试随机访问（vector优势）
        auto 开始 = std::chrono::high_resolution_clock::now();
        long long 总和1 = 0;
        for (size_t i = 0; i < 大小; i += 100) {
            总和1 += vec[i];  // O(1)随机访问
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto vector随机访问时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        开始 = std::chrono::high_resolution_clock::now();
        long long 总和2 = 0;
        for (size_t i = 0; i < 大小; i += 100) {
            auto it = lst.begin();
            std::advance(it, i);  // O(n)访问
            总和2 += *it;
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto list随机访问时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        // 测试中间插入（list优势）
        开始 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 1000; ++i) {
            vec.insert(vec.begin() + vec.size()/2, i);  // O(n)插入
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto vector插入时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        开始 = std::chrono::high_resolution_clock::now();
        auto mid_it = lst.begin();
        std::advance(mid_it, lst.size()/2);
        for (int i = 0; i < 1000; ++i) {
            lst.insert(mid_it, i);  // O(1)插入
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto list插入时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        std::cout << "随机访问性能：" << std::endl;
        std::cout << "vector：" << vector随机访问时间.count() << " 微秒" << std::endl;
        std::cout << "list：" << list随机访问时间.count() << " 微秒" << std::endl;
        std::cout << "vector优势：" << (double)list随机访问时间.count() / vector随机访问时间.count() << " 倍" << std::endl;

        std::cout << "\n中间插入性能：" << std::endl;
        std::cout << "vector：" << vector插入时间.count() << " 微秒" << std::endl;
        std::cout << "list：" << list插入时间.count() << " 微秒" << std::endl;
        std::cout << "list优势：" << (double)vector插入时间.count() / list插入时间.count() << " 倍" << std::endl;
    }

    void forward_list特性演示() {
        std::cout << "\n=== forward_list特性演示 ===" << std::endl;

        std::forward_list<int> flst;

        // forward_list只能从头部插入
        for (int i = 0; i < 10; ++i) {
            flst.push_front(i);
        }

        std::cout << "forward_list内容：";
        for (const auto& value : flst) {
            std::cout << value << " ";
        }
        std::cout << std::endl;

        // forward_list的内存开销更小
        std::cout << "内存开销对比：" << std::endl;
        std::cout << "list节点大小：" << sizeof(std::list<int>::value_type) << " + 指针开销" << std::endl;
        std::cout << "forward_list节点大小：" << sizeof(std::forward_list<int>::value_type) << " + 指针开销" << std::endl;
        std::cout << "forward_list节点只需要一个next指针，内存开销更小" << std::endl;
    }
};
```

### 4.4 关联容器的内存管理

#### 4.4.1 set/map的红黑树结构

```cpp
class 关联容器内存分析 {
public:
    void set内存结构演示() {
        std::cout << "=== set内存结构演示 ===" << std::endl;

        std::set<int> s;

        // 插入元素，观察自动排序
        std::vector<int> 插入顺序 = {5, 2, 8, 1, 9, 3, 7, 4, 6};

        std::cout << "插入顺序：";
        for (int value : 插入顺序) {
            std::cout << value << " ";
            s.insert(value);
        }
        std::cout << std::endl;

        std::cout << "set中的顺序：";
        for (const auto& value : s) {
            std::cout << value << " ";
        }
        std::cout << std::endl;

        // 查找性能测试
        auto 开始 = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 10000; ++i) {
            s.find(5);  // O(log n)查找
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto 查找时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);

        std::cout << "10000次查找耗时：" << 查找时间.count() << " 微秒" << std::endl;
    }

    void map内存使用演示() {
        std::cout << "\n=== map内存使用演示 ===" << std::endl;

        std::map<std::string, int> 学生成绩;

        // 插入数据
        学生成绩["张三"] = 85;
        学生成绩["李四"] = 92;
        学生成绩["王五"] = 78;
        学生成绩["赵六"] = 96;

        std::cout << "学生成绩（按姓名排序）：" << std::endl;
        for (const auto& pair : 学生成绩) {
            std::cout << pair.first << ": " << pair.second << std::endl;
        }

        // map的内存开销分析
        std::cout << "\nmap内存开销分析：" << std::endl;
        std::cout << "每个节点包含：" << std::endl;
        std::cout << "- key: " << sizeof(std::string) << " 字节（string对象）" << std::endl;
        std::cout << "- value: " << sizeof(int) << " 字节" << std::endl;
        std::cout << "- 红黑树指针：约 24-32 字节（3个指针 + 颜色信息）" << std::endl;
        std::cout << "- 总开销：每个节点约 " << sizeof(std::string) + sizeof(int) + 32 << " 字节" << std::endl;
    }

    void 自定义比较器演示() {
        std::cout << "\n=== 自定义比较器演示 ===" << std::endl;

        // 自定义比较器：按字符串长度排序
        auto 长度比较 = [](const std::string& a, const std::string& b) {
            if (a.length() != b.length()) {
                return a.length() < b.length();
            }
            return a < b;  // 长度相同时按字典序
        };

        std::set<std::string, decltype(长度比较)> 按长度排序(长度比较);

        std::vector<std::string> 单词 = {"apple", "cat", "elephant", "dog", "butterfly", "ant"};

        std::cout << "插入单词：";
        for (const auto& word : 单词) {
            std::cout << word << " ";
            按长度排序.insert(word);
        }
        std::cout << std::endl;

        std::cout << "按长度排序结果：";
        for (const auto& word : 按长度排序) {
            std::cout << word << " ";
        }
        std::cout << std::endl;
    }
};
    }

private:
    int value_;
    std::shared_ptr<SafeNode> next_;
    std::weak_ptr<SafeNode> prev_;  // 使用weak_ptr打破循环
};

void weak_ptr解决方案演示() {
    std::cout << "=== weak_ptr解决循环引用 ===" << std::endl;
    
    {
        auto node1 = std::make_shared<SafeNode>(1);
        auto node2 = std::make_shared<SafeNode>(2);
        auto node3 = std::make_shared<SafeNode>(3);
        
        // 构建链表
        node1->SetNext(node2);
        node2->SetPrev(node1);  // weak_ptr，不增加引用计数
        node2->SetNext(node3);
        node3->SetPrev(node2);  // weak_ptr，不增加引用计数
        
        std::cout << "node1引用计数: " << node1.use_count() << std::endl;  // 1
        std::cout << "node2引用计数: " << node2.use_count() << std::endl;  // 1
        std::cout << "node3引用计数: " << node3.use_count() << std::endl;  // 1
        
        // 打印连接关系
        node1->PrintConnection();
        node2->PrintConnection();
        node3->PrintConnection();
        
        // 测试weak_ptr的过期检测
        {
            auto weak_ref = std::weak_ptr<SafeNode>(node2);
            std::cout << "weak_ptr过期状态: " << weak_ref.expired() << std::endl;
            
            if (auto locked = weak_ref.lock()) {
                std::cout << "成功获取weak_ptr指向的对象: " << locked->GetValue() << std::endl;
            }
        }
        
        // 离开作用域，所有节点正确销毁
    }
    
    std::cout << "所有节点已正确销毁" << std::endl;
}
```

#### 3.4.3 weak_ptr的实际应用场景

```cpp
// 观察者模式的实现
class Subject;

class Observer {
public:
    Observer(int id) : id_(id) {
        std::cout << "创建Observer " << id_ << std::endl;
    }
    
    virtual ~Observer() {
        std::cout << "销毁Observer " << id_ << std::endl;
    }
    
    virtual void OnNotify(const std::string& message) {
        std::cout << "Observer " << id_ << " 收到通知: " << message << std::endl;
    }
    
    int GetId() const { return id_; }

private:
    int id_;
};

class Subject {
private:
    std::vector<std::weak_ptr<Observer>> observers_;

public:
    void AddObserver(std::shared_ptr<Observer> observer) {
        observers_.push_back(observer);  // weak_ptr不会延长生命周期
        std::cout << "添加观察者 " << observer->GetId() << std::endl;
    }
    
    void RemoveObserver(std::shared_ptr<Observer> observer) {
        observers_.erase(
            std::remove_if(observers_.begin(), observers_.end(),
                [&](const std::weak_ptr<Observer>& weak_obs) {
                    auto obs = weak_obs.lock();
                    return !obs || obs == observer;
                }),
            observers_.end());
        std::cout << "移除观察者 " << observer->GetId() << std::endl;
    }
    
    void NotifyAll(const std::string& message) {
        std::cout << "发送通知: " << message << std::endl;
        
        // 清理过期的观察者
        auto it = std::remove_if(observers_.begin(), observers_.end(),
            [](const std::weak_ptr<Observer>& weak_obs) {
                return weak_obs.expired();
            });
        
        if (it != observers_.end()) {
            std::cout << "清理了 " << std::distance(it, observers_.end()) << " 个过期观察者" << std::endl;
            observers_.erase(it, observers_.end());
        }
        
        // 通知存活的观察者
        for (const auto& weak_obs : observers_) {
            if (auto obs = weak_obs.lock()) {
                obs->OnNotify(message);
            }
        }
    }
    
    size_t GetObserverCount() const {
        return observers_.size();
    }
};

void 观察者模式演示() {
    std::cout << "=== 观察者模式演示 ===" << std::endl;
    
    Subject subject;
    
    {
        auto observer1 = std::make_shared<Observer>(1);
        auto observer2 = std::make_shared<Observer>(2);
        auto observer3 = std::make_shared<Observer>(3);
        
        subject.AddObserver(observer1);
        subject.AddObserver(observer2);
        subject.AddObserver(observer3);
        
        subject.NotifyAll("第一条消息");
        
        // observer2离开作用域
        observer2.reset();
        
        subject.NotifyAll("第二条消息");
        
        std::cout << "当前观察者数量: " << subject.GetObserverCount() << std::endl;
        
        // observer1和observer3离开作用域
    }
    
    subject.NotifyAll("第三条消息（所有观察者都已销毁）");
    std::cout << "最终观察者数量: " << subject.GetObserverCount() << std::endl;
}
```

### 3.5 智能指针的选择指南

#### 3.5.1 选择决策树

```mermaid
graph TD
    A["需要动态内存管理？"] --> B{"单一拥有者？"}
    A --> C{"多个拥有者？"}
    A --> D{"只需观察对象？"}
    
    B -->|是| E["使用unique_ptr<br/>独占所有权"]
    B -->|否| C
    
    C -->|是| F["使用shared_ptr<br/>共享所有权"]
    C -->|否| D
    
    D -->|是| G["使用weak_ptr<br/>观察者模式"]
    D -->|否| H["重新考虑设计<br/>可能不需要指针"]
    
    classDef decision fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef action fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    
    class A,B,C,D decision
    class E,F,G,H action
```

#### 3.5.2 实际应用示例对比

```cpp
class 智能指针选择示例 {
public:
    // 场景1: 游戏中的实体管理
    class GameEntityManager {
    private:
        std::vector<std::unique_ptr<Entity>> entities_;  // 频繁遍历，使用vector
        std::unordered_map<int, Entity*> entityMap_;     // 快速查找，使用hash map
        
    public:
        void AddEntity(std::unique_ptr<Entity> entity) {
            int id = entity->GetId();
            Entity* rawPtr = entity.get();
            
            entities_.push_back(std::move(entity));
            entityMap_[id] = rawPtr;
        }
        
        Entity* FindEntity(int id) {
            auto it = entityMap_.find(id);
            return (it != entityMap_.end()) ? it->second : nullptr;
        }
        
        void UpdateAll() {
            // vector的缓存友好性使遍历更快
            for (const auto& entity : entities_) {
                entity->Update();
            }
        }
    };
    
    // 场景2: 任务调度系统
    class TaskScheduler {
    private:
        std::priority_queue<Task, std::vector<Task>, TaskComparator> taskQueue_;
        std::list<Task> runningTasks_;  // 频繁插入删除
        
    public:
        void ScheduleTask(const Task& task) {
            taskQueue_.push(task);
        }
        
        void ProcessTasks() {
            while (!taskQueue_.empty()) {
                Task task = taskQueue_.top();
                taskQueue_.pop();
                
                if (task.IsReady()) {
                    runningTasks_.push_back(task);
                }
            }
            
            // 处理运行中的任务
            for (auto it = runningTasks_.begin(); it != runningTasks_.end();) {
                if (it->IsCompleted()) {
                    it = runningTasks_.erase(it);  // list的O(1)删除
                } else {
                    it->Execute();
                    ++it;
                }
            }
        }
    };
    
    // 场景3: 缓存系统
    template<typename Key, typename Value>
    class LRUCache {
    private:
        struct CacheItem {
            Key key;
            Value value;
            CacheItem(const Key& k, const Value& v) : key(k), value(v) {}
        };
        
        std::list<CacheItem> items_;  // LRU列表
        std::unordered_map<Key, typename std::list<CacheItem>::iterator> itemMap_;
        size_t capacity_;
        
    public:
        LRUCache(size_t capacity) : capacity_(capacity) {}
        
        std::optional<Value> Get(const Key& key) {
            auto mapIt = itemMap_.find(key);
            if (mapIt == itemMap_.end()) {
                return std::nullopt;  // 缓存未命中
            }
            
            // 移动到列表前端（最近使用）
            auto listIt = mapIt->second;
            items_.splice(items_.begin(), items_, listIt);
            
            return listIt->value;
        }
        
        void Put(const Key& key, const Value& value) {
            auto mapIt = itemMap_.find(key);
            
            if (mapIt != itemMap_.end()) {
                // 更新现有项
                auto listIt = mapIt->second;
                listIt->value = value;
                items_.splice(items_.begin(), items_, listIt);
            } else {
                // 添加新项
                if (items_.size() >= capacity_) {
                    // 删除最少使用的项
                    Key lastKey = items_.back().key;
                    items_.pop_back();
                    itemMap_.erase(lastKey);
                }
                
                items_.emplace_front(key, value);
                itemMap_[key] = items_.begin();
            }
        }
    };
    
    void 演示缓存使用() {
        std::cout << "=== LRU缓存演示 ===" << std::endl;
        
        LRUCache<int, std::string> cache(3);
        
        cache.Put(1, "one");
        cache.Put(2, "two");
        cache.Put(3, "three");
        
        std::cout << "缓存状态: 1->one, 2->two, 3->three" << std::endl;
        
        auto value = cache.Get(1);
        if (value) {
            std::cout << "获取键1: " << *value << std::endl;
        }
        
        cache.Put(4, "four");  // 这会淘汰键2
        std::cout << "添加 4->four，应该淘汰键2" << std::endl;
        
        value = cache.Get(2);
        if (!value) {
            std::cout << "键2已被淘汰" << std::endl;
        }
    }
};
```

### 4.8 容器内存优化技巧

#### 4.8.1 内存预分配策略

```cpp
class 容器内存优化 {
public:
    void reserve优化技巧() {
        std::cout << "=== reserve优化技巧 ===" << std::endl;
        
        const int 预期大小 = 100000;
        
        // 错误做法：不预分配
        auto 开始 = std::chrono::high_resolution_clock::now();
        std::vector<int> vec1;
        for (int i = 0; i < 预期大小; ++i) {
            vec1.push_back(i);
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto 不预分配时间 = std::chrono::duration_cast<std::chrono::milliseconds>(结束 - 开始);
        
        // 正确做法：预分配内存
        开始 = std::chrono::high_resolution_clock::now();
        std::vector<int> vec2;
        vec2.reserve(预期大小);
        for (int i = 0; i < 预期大小; ++i) {
            vec2.push_back(i);
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto 预分配时间 = std::chrono::duration_cast<std::chrono::milliseconds>(结束 - 开始);
        
        std::cout << "添加 " << 预期大小 << " 个元素:" << std::endl;
        std::cout << "不预分配: " << 不预分配时间.count() << " 毫秒" << std::endl;
        std::cout << "预分配: " << 预分配时间.count() << " 毫秒" << std::endl;
        std::cout << "性能提升: " << (double)不预分配时间.count() / 预分配时间.count() << " 倍" << std::endl;
    }
    
    void 内存释放策略() {
        std::cout << "\n=== 内存释放策略 ===" << std::endl;
        
        std::vector<int> vec;
        vec.reserve(100000);
        
        // 填充数据
        for (int i = 0; i < 100000; ++i) {
            vec.push_back(i);
        }
        
        std::cout << "填充后 - 大小: " << vec.size() << ", 容量: " << vec.capacity() << std::endl;
        
        // 清空数据
        vec.clear();
        std::cout << "clear后 - 大小: " << vec.size() << ", 容量: " << vec.capacity() << std::endl;
        
        // 方法1: shrink_to_fit（可能不释放）
        vec.shrink_to_fit();
        std::cout << "shrink_to_fit后 - 大小: " << vec.size() << ", 容量: " << vec.capacity() << std::endl;
        
        // 方法2: swap技巧（强制释放）
        std::vector<int>().swap(vec);
        std::cout << "swap技巧后 - 大小: " << vec.size() << ", 容量: " << vec.capacity() << std::endl;
        
        // 方法3: 重新赋值（C++11推荐）
        vec = std::vector<int>();
        std::cout << "重新赋值后 - 大小: " << vec.size() << ", 容量: " << vec.capacity() << std::endl;
    }
    
    void emplace优化() {
        std::cout << "\n=== emplace优化 ===" << std::endl;
        
        struct ComplexObject {
            std::string data;
            int value;
            
            ComplexObject(const std::string& d, int v) : data(d), value(v) {
                std::cout << "构造ComplexObject: " << data << std::endl;
            }
            
            ComplexObject(const ComplexObject& other) : data(other.data), value(other.value) {
                std::cout << "拷贝构造ComplexObject: " << data << std::endl;
            }
            
            ComplexObject(ComplexObject&& other) noexcept : data(std::move(other.data)), value(other.value) {
                std::cout << "移动构造ComplexObject: " << data << std::endl;
            }
        };
        
        std::vector<ComplexObject> vec;
        vec.reserve(3);
        
        std::cout << "使用push_back:" << std::endl;
        vec.push_back(ComplexObject("obj1", 1));  // 构造临时对象 + 移动构造
        
        std::cout << "\n使用emplace_back:" << std::endl;
        vec.emplace_back("obj2", 2);  // 直接在容器中构造，更高效
        
        std::cout << "\n对比完成" << std::endl;
    }
};
```

### 4.9 Part 4 总结

> **🎯 Part 4 核心要点**：

#### 容器选择准则

| 需求 | 推荐容器 | 理由 |
|------|----------|------|
| **频繁随机访问** | vector | 连续内存，O(1)访问 |
| **两端操作** | deque | 两端O(1)插入删除 |
| **中间插入删除** | list | 任意位置O(1)操作 |
| **快速查找（有序）** | set/map | O(log n)查找，自动排序 |
| **快速查找（无序）** | unordered_set/unordered_map | O(1)平均查找时间 |
| **FIFO队列** | queue | 先进先出 |
| **LIFO栈** | stack | 后进先出 |
| **优先级处理** | priority_queue | 自动排序的堆 |

#### 性能优化要点

1. **预分配内存**：已知大小时使用reserve()
2. **就地构造**：使用emplace而非push + 临时对象
3. **移动语义**：避免不必要的拷贝
4. **内存释放**：及时释放不需要的容量
5. **选择合适容器**：根据使用模式选择最优容器

#### 内存管理最佳实践

- ✅ 根据访问模式选择容器
- ✅ 预分配已知大小的容器
- ✅ 使用emplace_back而非push_back临时对象
- ✅ 适当时机释放过量内存
- ✅ 理解容器的内存分配策略

**接下来**：Part 5将深入探讨移动语义与性能优化，学习现代C++的高效内存操作技术。

---

## Part 5: 移动语义与性能——现代C++的高效内存操作

> **学习目标**：深入理解C++11引入的移动语义，掌握右值引用和完美转发技术，学会编写高性能的内存管理代码，避免不必要的内存拷贝。

### 5.1 移动语义基础：从拷贝到移动的革命

#### 5.1.1 传统拷贝语义的性能问题

```mermaid
graph TD
    A["传统拷贝语义"] --> B["🐌 深度拷贝<br/>分配新内存"]
    B --> C["📋 复制所有数据<br/>O(n)时间复杂度"]
    C --> D["🗑️ 销毁临时对象<br/>浪费资源"]
    
    E["现代移动语义"] --> F["⚡ 资源转移<br/>修改指针"]
    F --> G["🎯 O(1)时间复杂度<br/>无数据拷贝"]
    G --> H["♻️ 资源重用<br/>环保高效"]
    
    classDef old fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef new fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    
    class A,B,C,D old
    class E,F,G,H new
```

#### 传统拷贝 vs 移动语义对比代码

```cpp
#include <iostream>
#include <vector>
#include <chrono>
#include <memory>

class ResourceHolder {
private:
    std::vector<int> data_;
    std::string name_;

public:
    // 构造函数
    ResourceHolder(const std::string& name, size_t size) 
        : name_(name), data_(size) {
        std::cout << "构造 " << name_ << " (大小: " << size << ")" << std::endl;
        // 初始化数据
        for (size_t i = 0; i < size; ++i) {
            data_[i] = static_cast<int>(i);
        }
    }
    
    // 传统拷贝构造函数
    ResourceHolder(const ResourceHolder& other) 
        : name_(other.name_ + "_copy"), data_(other.data_) {
        std::cout << "拷贝构造 " << name_ << " (大小: " << data_.size() << ")" << std::endl;
    }
    
    // 移动构造函数 (C++11)
    ResourceHolder(ResourceHolder&& other) noexcept 
        : name_(std::move(other.name_)), data_(std::move(other.data_)) {
        std::cout << "移动构造 " << name_ << " (无数据拷贝)" << std::endl;
        other.name_ = "moved_from";
    }
    
    // 传统拷贝赋值操作符
    ResourceHolder& operator=(const ResourceHolder& other) {
        if (this != &other) {
            name_ = other.name_ + "_assigned";
            data_ = other.data_;  // 深度拷贝
            std::cout << "拷贝赋值 " << name_ << " (大小: " << data_.size() << ")" << std::endl;
        }
        return *this;
    }
    
    // 移动赋值操作符 (C++11)
    ResourceHolder& operator=(ResourceHolder&& other) noexcept {
        if (this != &other) {
            name_ = std::move(other.name_);
            data_ = std::move(other.data_);  // 移动，不拷贝
            std::cout << "移动赋值 " << name_ << " (无数据拷贝)" << std::endl;
            other.name_ = "moved_from";
        }
        return *this;
    }
    
    ~ResourceHolder() {
        std::cout << "析构 " << name_ << " (大小: " << data_.size() << ")" << std::endl;
    }
    
    const std::string& GetName() const { return name_; }
    size_t GetSize() const { return data_.size(); }
};

void 拷贝vs移动性能对比() {
    std::cout << "=== 拷贝 vs 移动性能对比 ===" << std::endl;
    
    const size_t 数据大小 = 1000000;  // 100万个整数
    const int 测试次数 = 100;
    
    // 测试拷贝构造的性能
    auto 开始 = std::chrono::high_resolution_clock::now();
    std::vector<ResourceHolder> 拷贝容器;
    拷贝容器.reserve(测试次数);
    
    ResourceHolder 原始对象("original", 数据大小);
    for (int i = 0; i < 测试次数; ++i) {
        拷贝容器.push_back(原始对象);  // 拷贝构造
    }
    auto 结束 = std::chrono::high_resolution_clock::now();
    auto 拷贝时间 = std::chrono::duration_cast<std::chrono::milliseconds>(结束 - 开始);
    
    // 测试移动构造的性能
    开始 = std::chrono::high_resolution_clock::now();
    std::vector<ResourceHolder> 移动容器;
    移动容器.reserve(测试次数);
    
    for (int i = 0; i < 测试次数; ++i) {
        移动容器.push_back(ResourceHolder("temp" + std::to_string(i), 数据大小));  // 移动构造
    }
    结束 = std::chrono::high_resolution_clock::now();
    auto 移动时间 = std::chrono::duration_cast<std::chrono::milliseconds>(结束 - 开始);
    
    std::cout << "\n性能对比结果 (" << 测试次数 << " 次操作):" << std::endl;
    std::cout << "拷贝构造时间: " << 拷贝时间.count() << " 毫秒" << std::endl;
    std::cout << "移动构造时间: " << 移动时间.count() << " 毫秒" << std::endl;
    if (移动时间.count() > 0) {
        std::cout << "性能提升: " << (double)拷贝时间.count() / 移动时间.count() << " 倍" << std::endl;
    }
}
```

### 5.2 右值引用：移动语义的基础

#### 5.2.1 左值与右值的深度理解

```cpp
class 左值右值分析 {
public:
    void 基本概念演示() {
        std::cout << "=== 左值与右值基本概念 ===" << std::endl;
        
        int x = 10;        // x是左值
        int y = 20;        // y是左值
        
        // 左值：有名字，可以取地址
        std::cout << "x的地址: " << &x << std::endl;
        std::cout << "y的地址: " << &y << std::endl;
        
        // 右值：临时值，不可取地址
        int z = x + y;     // x + y是右值表达式
        // &(x + y);       // 编译错误！右值不能取地址
        
        // 函数返回值通常是右值
        auto result = GetTemporaryValue();  // GetTemporaryValue()返回右值
        
        std::cout << "左值引用绑定到左值: ";
        int& left_ref = x;      // OK：左值引用绑定到左值
        std::cout << left_ref << std::endl;
        
        // int& left_ref2 = x + y;  // 编译错误！左值引用不能绑定到右值
        
        std::cout << "右值引用绑定到右值: ";
        int&& right_ref = x + y;    // OK：右值引用绑定到右值
        std::cout << right_ref << std::endl;
        
        // int&& right_ref2 = x;   // 编译错误！右值引用不能直接绑定到左值
        int&& right_ref3 = std::move(x);  // OK：std::move将左值转换为右值
        std::cout << "通过std::move绑定: " << right_ref3 << std::endl;
    }
    
    void 引用折叠演示() {
        std::cout << "\n=== 引用折叠演示 ===" << std::endl;
        
        int x = 42;
        const int y = 24;
        
        // 引用折叠规则：
        // T& &   -> T&
        // T& &&  -> T&
        // T&& &  -> T&
        // T&& && -> T&&
        
        template_function(x);           // 传入左值
        template_function(100);         // 传入右值
        template_function(std::move(x)); // 传入通过move转换的右值
    }
    
    template<typename T>
    void template_function(T&& param) {
        std::cout << "参数类型: ";
        if constexpr (std::is_lvalue_reference_v<T>) {
            std::cout << "左值引用" << std::endl;
        } else if constexpr (std::is_rvalue_reference_v<T>) {
            std::cout << "右值引用" << std::endl;
        } else {
            std::cout << "值类型" << std::endl;
        }
        std::cout << "参数值: " << param << std::endl;
    }

private:
    int GetTemporaryValue() {
        return 42;  // 返回右值
    }
};
```

#### 5.2.2 std::move的工作原理

```cpp
class move工作原理分析 {
public:
    void move的本质() {
        std::cout << "=== std::move的本质 ===" << std::endl;
        
        std::string str = "Hello, World!";
        std::cout << "原始字符串: " << str << std::endl;
        std::cout << "原始字符串地址: " << (void*)str.data() << std::endl;
        
        // std::move不移动任何东西，只是类型转换
        std::string&& moved_ref = std::move(str);
        std::cout << "move后原字符串: " << str << std::endl;           // 仍然有效
        std::cout << "move后引用: " << moved_ref << std::endl;         // 相同内容
        std::cout << "引用的地址: " << (void*)moved_ref.data() << std::endl;  // 相同地址
        
        // 真正的移动发生在移动构造或移动赋值时
        std::string new_str = std::move(str);  // 这里才真正移动
        std::cout << "移动构造后原字符串: '" << str << "'" << std::endl;      // 现在为空或未定义
        std::cout << "新字符串: " << new_str << std::endl;
        std::cout << "新字符串地址: " << (void*)new_str.data() << std::endl;
    }
    
    void 自定义move实现() {
        std::cout << "\n=== 自定义move实现 ===" << std::endl;
        
        // 简化版的std::move实现
        auto my_move = [](auto&& obj) -> decltype(auto) {
            using T = std::remove_reference_t<decltype(obj)>;
            return static_cast<T&&>(obj);
        };
        
        std::string str = "Test";
        std::string moved = my_move(str);
        
        std::cout << "自定义move工作正常" << std::endl;
    }
    
    void move的误用示例() {
        std::cout << "\n=== move的误用示例 ===" << std::endl;
        
        std::vector<std::string> vec;
        std::string str = "Important Data";
        
        // ❌ 错误：对需要保留的变量使用move
        vec.push_back(std::move(str));
        std::cout << "移动后原字符串: '" << str << "'" << std::endl;  // 现在为空
        
        // ✅ 正确：只对临时值或不再需要的变量使用move
        std::string temp = "Temporary Data";
        vec.push_back(std::move(temp));  // temp不再需要
        
        // ✅ 正确：返回值优化情况下不需要显式move
        auto result = create_string();  // 编译器自动优化
        
        std::cout << "vector中的字符串数量: " << vec.size() << std::endl;
    }

private:
    std::string create_string() {
        std::string local = "Local String";
        return local;  // 返回值优化(RVO)，不需要std::move
    }
};
```

### 5.3 完美转发：参数传递的艺术

#### 5.3.1 完美转发的需求与实现

```cpp
#include <utility>
#include <type_traits>

class 完美转发演示 {
public:
    // 目标函数：接受不同类型的参数
    void target_function(int& x) {
        std::cout << "调用 target_function(int&), 值: " << x << std::endl;
        x *= 2;  // 修改原值
    }
    
    void target_function(const int& x) {
        std::cout << "调用 target_function(const int&), 值: " << x << std::endl;
    }
    
    void target_function(int&& x) {
        std::cout << "调用 target_function(int&&), 值: " << x << std::endl;
        x *= 3;  // 修改临时值
    }
    
    // ❌ 不完美的转发尝试
    template<typename T>
    void imperfect_forward(T& param) {
        std::cout << "不完美转发 (只能处理左值引用): ";
        target_function(param);
    }
    
    // ✅ 完美转发实现
    template<typename T>
    void perfect_forward(T&& param) {
        std::cout << "完美转发: ";
        target_function(std::forward<T>(param));
    }
    
    void 演示完美转发() {
        std::cout << "=== 完美转发演示 ===" << std::endl;
        
        int x = 10;
        const int y = 20;
        
        std::cout << "\n直接调用目标函数:" << std::endl;
        target_function(x);        // 调用int&版本
        target_function(y);        // 调用const int&版本
        target_function(30);       // 调用int&&版本
        
        std::cout << "\n通过完美转发调用:" << std::endl;
        perfect_forward(x);        // 保持左值引用特性
        perfect_forward(y);        // 保持const左值引用特性
        perfect_forward(30);       // 保持右值引用特性
        
        std::cout << "\n验证参数修改:" << std::endl;
        std::cout << "x的值: " << x << std::endl;  // 应该被修改了
    }
    
    // 完美转发在工厂函数中的应用
    template<typename T, typename... Args>
    std::unique_ptr<T> make_unique_perfect(Args&&... args) {
        return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
    }
    
    void 工厂函数演示() {
        std::cout << "\n=== 工厂函数演示 ===" << std::endl;
        
        class TestClass {
        public:
            TestClass(int a, const std::string& b, std::vector<int>&& c) 
                : a_(a), b_(b), c_(std::move(c)) {
                std::cout << "构造TestClass: a=" << a_ << ", b=" << b_ 
                          << ", c.size=" << c_.size() << std::endl;
            }
            
        private:
            int a_;
            std::string b_;
            std::vector<int> c_;
        };
        
        std::string str = "test";
        std::vector<int> vec = {1, 2, 3, 4, 5};
        
        // 完美转发到构造函数
        auto obj = make_unique_perfect<TestClass>(
            42,                    // 右值
            str,                   // 左值
            std::move(vec)         // 转换为右值
        );
        
        std::cout << "工厂函数创建对象成功" << std::endl;
    }
};
```

#### 5.3.2 万能引用与模板特化

```cpp
class 万能引用详解 {
public:
    // 万能引用：T&&在模板中的特殊含义
    template<typename T>
    void universal_reference_demo(T&& param) {
        std::cout << "=== 万能引用类型推导 ===" << std::endl;
        
        if constexpr (std::is_lvalue_reference_v<T>) {
            std::cout << "推导为左值引用: " << typeid(T).name() << std::endl;
        } else {
            std::cout << "推导为右值引用或值类型: " << typeid(T).name() << std::endl;
        }
        
        // 使用if constexpr进行编译时分支
        if constexpr (std::is_same_v<std::remove_reference_t<T>, std::string>) {
            std::cout << "这是一个字符串类型" << std::endl;
        }
        
        // 条件转发
        process_parameter(std::forward<T>(param));
    }
    
    void process_parameter(const std::string& s) {
        std::cout << "处理左值字符串: " << s << std::endl;
    }
    
    void process_parameter(std::string&& s) {
        std::cout << "处理右值字符串: " << s << std::endl;
        s += " (已处理)";
    }
    
    void process_parameter(int x) {
        std::cout << "处理整数: " << x << std::endl;
    }
    
    void 测试万能引用() {
        std::cout << "=== 万能引用测试 ===" << std::endl;
        
        std::string str = "Hello";
        const std::string const_str = "World";
        
        universal_reference_demo(str);           // T推导为std::string&
        universal_reference_demo(const_str);     // T推导为const std::string&
        universal_reference_demo(std::string("Temp"));  // T推导为std::string
        universal_reference_demo(42);           // T推导为int
        universal_reference_demo(std::move(str)); // T推导为std::string
    }
    
    // SFINAE：替换失败不是错误
    template<typename T>
    auto smart_function(T&& param) 
        -> std::enable_if_t<std::is_integral_v<std::decay_t<T>>, void> {
        std::cout << "处理整数类型: " << param << std::endl;
    }
    
    template<typename T>
    auto smart_function(T&& param) 
        -> std::enable_if_t<std::is_same_v<std::decay_t<T>, std::string>, void> {
        std::cout << "处理字符串类型: " << param << std::endl;
    }
    
    void SFINAE演示() {
        std::cout << "\n=== SFINAE演示 ===" << std::endl;
        
        smart_function(42);
        smart_function(std::string("Hello"));
        // smart_function(3.14);  // 编译错误：没有匹配的重载
    }
};
```

### 5.4 移动语义在容器中的应用

#### 5.4.1 容器的移动优化

```cpp
class 容器移动优化 {
public:
    void vector移动优化() {
        std::cout << "=== vector移动优化 ===" << std::endl;
        
        // 创建大的vector
        std::vector<std::string> source_vec;
        source_vec.reserve(1000000);
        for (int i = 0; i < 1000000; ++i) {
            source_vec.emplace_back("String " + std::to_string(i));
        }
        
        std::cout << "源vector大小: " << source_vec.size() << std::endl;
        std::cout << "源vector容量: " << source_vec.capacity() << std::endl;
        
        // 移动构造
        auto 开始 = std::chrono::high_resolution_clock::now();
        std::vector<std::string> moved_vec = std::move(source_vec);
        auto 结束 = std::chrono::high_resolution_clock::now();
        
        auto 移动时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);
        
        std::cout << "移动构造时间: " << 移动时间.count() << " 微秒" << std::endl;
        std::cout << "移动后目标vector大小: " << moved_vec.size() << std::endl;
        std::cout << "移动后源vector大小: " << source_vec.size() << std::endl;
        
        // 对比拷贝构造的时间
        开始 = std::chrono::high_resolution_clock::now();
        std::vector<std::string> copied_vec = moved_vec;  // 拷贝构造
        结束 = std::chrono::high_resolution_clock::now();
        auto 拷贝时间 = std::chrono::duration_cast<std::chrono::microseconds>(结束 - 开始);
        
        std::cout << "拷贝构造时间: " << 拷贝时间.count() << " 微秒" << std::endl;
        if (移动时间.count() > 0) {
            std::cout << "移动比拷贝快: " << (double)拷贝时间.count() / 移动时间.count() << " 倍" << std::endl;
        }
    }
    
    void 元素移动vs拷贝() {
        std::cout << "\n=== 容器元素移动vs拷贝 ===" << std::endl;
        
        class MoveableClass {
        private:
            std::unique_ptr<int[]> data_;
            size_t size_;
            std::string name_;
            
        public:
            MoveableClass(const std::string& name, size_t size) 
                : name_(name), size_(size), data_(std::make_unique<int[]>(size)) {
                std::cout << "构造 " << name_ << " (大小: " << size_ << ")" << std::endl;
            }
            
            // 拷贝构造（昂贵）
            MoveableClass(const MoveableClass& other) 
                : name_(other.name_ + "_copy"), size_(other.size_), 
                  data_(std::make_unique<int[]>(other.size_)) {
                std::cout << "拷贝构造 " << name_ << " (昂贵操作)" << std::endl;
                std::copy(other.data_.get(), other.data_.get() + size_, data_.get());
            }
            
            // 移动构造（高效）
            MoveableClass(MoveableClass&& other) noexcept 
                : name_(std::move(other.name_)), size_(other.size_), 
                  data_(std::move(other.data_)) {
                std::cout << "移动构造 " << name_ << " (高效操作)" << std::endl;
                other.size_ = 0;
            }
            
            MoveableClass& operator=(const MoveableClass&) = delete;
            MoveableClass& operator=(MoveableClass&&) = delete;
            
            size_t Size() const { return size_; }
        };
        
        std::vector<MoveableClass> vec;
        vec.reserve(3);
        
        std::cout << "使用emplace_back (直接构造):" << std::endl;
        vec.emplace_back("obj1", 1000);
        
        std::cout << "\n使用push_back临时对象 (移动构造):" << std::endl;
        vec.push_back(MoveableClass("obj2", 1000));
        
        std::cout << "\n通过move传递左值 (移动构造):" << std::endl;
        MoveableClass obj3("obj3", 1000);
        vec.push_back(std::move(obj3));
        
        std::cout << "\nvector中的对象:" << std::endl;
        for (const auto& obj : vec) {
            std::cout << "  " << obj.GetName() << std::endl;
        }
    }
};
```

### 5.5 返回值优化（RVO）与命名返回值优化（NRVO）

#### 5.5.1 编译器优化技术

```cpp
class RVO优化分析 {
public:
    // 返回值优化 (RVO)
    std::vector<int> create_vector_rvo() {
        std::cout << "RVO: 创建并返回vector" << std::endl;
        return std::vector<int>(1000000, 42);  // RVO优化，不会发生拷贝或移动
    }
    
    // 命名返回值优化 (NRVO)
    std::vector<int> create_vector_nrvo() {
        std::cout << "NRVO: 创建命名vector" << std::endl;
        std::vector<int> result(1000000, 42);
        // ... 可能的修改操作 ...
        return result;  // NRVO优化，直接在调用者的内存中构造
    }
    
    // 不能优化的情况
    std::vector<int> create_vector_no_optimization(bool condition) {
        std::cout << "无法优化: 多个返回路径" << std::endl;
        std::vector<int> vec1(500000, 1);
        std::vector<int> vec2(500000, 2);
        
        if (condition) {
            return vec1;  // 可能会发生移动
        } else {
            return vec2;  // 可能会发生移动
        }
    }
    
    void 测试RVO优化() {
        std::cout << "=== 返回值优化测试 ===" << std::endl;
        
        auto vec1 = create_vector_rvo();
        auto vec2 = create_vector_nrvo();
        
        // 这里不再需要计时，主要演示优化效果
        
        std::cout << "RVO创建的vector大小: " << vec1.size() << std::endl;
        std::cout << "NRVO创建的vector大小: " << vec2.size() << std::endl;
    }
    
    // 何时不应该使用std::move
    void move使用误区() {
        std::cout << "\n=== move使用误区 ===" << std::endl;
        
        // ❌ 错误：对返回值使用move会阻止RVO
        auto 错误的做法 = []() -> std::vector<int> {
            std::vector<int> local(1000000, 42);
            return std::move(local);  // 错误！阻止了NRVO
        };
        
        // ✅ 正确：让编译器自动优化
        auto 正确的做法 = []() -> std::vector<int> {
            std::vector<int> local(1000000, 42);
            return local;  // 正确！允许NRVO
        };
        
        auto result1 = 错误的做法();
        auto result2 = 正确的做法();
        
        std::cout << "错误使用move结果大小: " << result1.size() << std::endl;
        std::cout << "正确方式结果大小: " << result2.size() << std::endl;
    }
};
```

### 5.6 自定义类的移动语义实现

#### 5.6.1 五大特殊成员函数（Rule of Five）

```cpp
class RuleFiveExample {
private:
    char* data_;
    size_t size_;
    size_t capacity_;

public:
    // 1. 默认构造函数
    RuleFiveExample() : data_(nullptr), size_(0), capacity_(0) {
        std::cout << "默认构造函数" << std::endl;
    }
    
    // 2. 析构函数
    ~RuleFiveExample() {
        std::cout << "析构函数 (大小: " << size_ << ")" << std::endl;
        delete[] data_;
    }
    
    // 3. 拷贝构造函数
    RuleFiveExample(const RuleFiveExample& other) 
        : size_(other.size_), capacity_(other.capacity_) {
        std::cout << "拷贝构造函数 (大小: " << size_ << ")" << std::endl;
        
        if (capacity_ > 0) {
            data_ = new char[capacity_];
            std::copy(other.data_, other.data_ + size_, data_);
        } else {
            data_ = nullptr;
        }
    }
    
    // 4. 拷贝赋值操作符
    RuleFiveExample& operator=(const RuleFiveExample& other) {
        std::cout << "拷贝赋值操作符 (大小: " << other.size_ << ")" << std::endl;
        
        if (this != &other) {
            // 释放原有资源
            delete[] data_;
            
            // 拷贝新资源
            size_ = other.size_;
            capacity_ = other.capacity_;
            
            if (capacity_ > 0) {
                data_ = new char[capacity_];
                std::copy(other.data_, other.data_ + size_, data_);
            } else {
                data_ = nullptr;
            }
        }
        
        return *this;
    }
    
    // 5. 移动构造函数
    RuleFiveExample(RuleFiveExample&& other) noexcept 
        : data_(other.data_), size_(other.size_), capacity_(other.capacity_) {
        std::cout << "移动构造函数 (大小: " << size_ << ")" << std::endl;
        
        // 将源对象置于有效但未定义的状态
        other.data_ = nullptr;
        other.size_ = 0;
        other.capacity_ = 0;
    }
    
    // 6. 移动赋值操作符
    RuleFiveExample& operator=(RuleFiveExample&& other) noexcept {
        std::cout << "移动赋值操作符 (大小: " << other.size_ << ")" << std::endl;
        
        if (this != &other) {
            // 释放原有资源
            delete[] data_;
            
            // 转移资源
            data_ = other.data_;
            size_ = other.size_;
            capacity_ = other.capacity_;
            
            // 将源对象置于有效但未定义的状态
            other.data_ = nullptr;
            other.size_ = 0;
            other.capacity_ = 0;
        }
        
        return *this;
    }
    
    // 辅助函数
    void PushBack(char c) {
        if (size_ >= capacity_) {
            Reserve(capacity_ == 0 ? 1 : capacity_ * 2);
        }
        data_[size_++] = c;
    }
    
    void Reserve(size_t new_capacity) {
        if (new_capacity > capacity_) {
            char* new_data = new char[new_capacity];
            if (data_) {
                std::copy(data_, data_ + size_, new_data);
                delete[] data_;
            }
            data_ = new_data;
            capacity_ = new_capacity;
        }
    }
    
    size_t Size() const { return size_; }
    size_t Capacity() const { return capacity_; }
    const char* Data() const { return data_; }
};

void 测试RuleOfFive() {
    std::cout << "=== Rule of Five 测试 ===" << std::endl;
    
    // 测试构造和移动
    std::vector<RuleFiveExample> vec;
    vec.reserve(3);
    
    std::cout << "\n1. 默认构造:" << std::endl;
    RuleFiveExample obj1;
    obj1.PushBack('A');
    obj1.PushBack('B');
    
    std::cout << "\n2. 拷贝构造:" << std::endl;
    RuleFiveExample obj2 = obj1;
    
    std::cout << "\n3. 移动构造:" << std::endl;
    RuleFiveExample obj3 = std::move(obj1);
    
    std::cout << "\n4. 拷贝赋值:" << std::endl;
    RuleFiveExample obj4;
    obj4 = obj2;
    
    std::cout << "\n5. 移动赋值:" << std::endl;
    RuleFiveExample obj5;
    obj5 = std::move(obj2);
    
    std::cout << "\n对象状态检查:" << std::endl;
    std::cout << "obj1大小: " << obj1.Size() << std::endl;  // 应该为0（被移动）
    std::cout << "obj2大小: " << obj2.Size() << std::endl;  // 应该为0（被移动）
    std::cout << "obj3大小: " << obj3.Size() << std::endl;  // 应该为2
    std::cout << "obj4大小: " << obj4.Size() << std::endl;  // 应该为2
    std::cout << "obj5大小: " << obj5.Size() << std::endl;  // 应该为2
    
    std::cout << "\n析构开始:" << std::endl;
}
```

### 5.7 移动语义的性能测试与分析

#### 5.7.1 综合性能测试

```cpp
class 移动语义性能测试 {
public:
    void 综合性能对比() {
        std::cout << "=== 移动语义综合性能测试 ===" << std::endl;
        
        const int 测试轮数 = 1000;
        const size_t 对象大小 = 100000;
        
        // 测试不同场景下的性能
        测试容器插入性能(测试轮数, 对象大小);
        测试函数返回性能(测试轮数, 对象大小);
        测试容器重分配性能(测试轮数, 对象大小);
    }

private:
    void 测试容器插入性能(int 轮数, size_t 大小) {
        std::cout << "\n--- 容器插入性能测试 ---" << std::endl;
        
        // 拷贝插入测试
        auto 开始 = std::chrono::high_resolution_clock::now();
        std::vector<std::vector<int>> 拷贝容器;
        拷贝容器.reserve(轮数);
        
        std::vector<int> 原型(大小, 42);
        for (int i = 0; i < 轮数; ++i) {
            拷贝容器.push_back(原型);  // 拷贝
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto 拷贝时间 = std::chrono::duration_cast<std::chrono::milliseconds>(结束 - 开始);
        
        // 移动插入测试
        开始 = std::chrono::high_resolution_clock::now();
        std::vector<std::vector<int>> 移动容器;
        移动容器.reserve(轮数);
        
        for (int i = 0; i < 轮数; ++i) {
            移动容器.push_back(std::vector<int>(大小, 42));  // 移动
        }
        结束 = std::chrono::high_resolution_clock::now();
        auto 移动时间 = std::chrono::duration_cast<std::chrono::milliseconds>(结束 - 开始);
        
        std::cout << "拷贝插入时间: " << 拷贝时间.count() << " 毫秒" << std::endl;
        std::cout << "移动插入时间: " << 移动时间.count() << " 毫秒" << std::endl;
        std::cout << "性能提升: " << (double)拷贝时间.count() / 移动时间.count() << " 倍" << std::endl;
    }
    
    void 测试函数返回性能(int 轮数, size_t 大小) {
        std::cout << "\n--- 函数返回性能测试 ---" << std::endl;
        
        auto 创建大对象 = [大小]() -> std::vector<int> {
            return std::vector<int>(大小, 42);
        };
        
        auto 开始 = std::chrono::high_resolution_clock::now();
        std::vector<std::vector<int>> 结果容器;
        结果容器.reserve(轮数);
        
        for (int i = 0; i < 轮数; ++i) {
            结果容器.push_back(创建大对象());  // RVO + 移动
        }
        auto 结束 = std::chrono::high_resolution_clock::now();
        auto 函数返回时间 = std::chrono::duration_cast<std::chrono::milliseconds>(结束 - 开始);
        
        std::cout << "函数返回时间: " << 函数返回时间.count() << " 毫秒" << std::endl;
    }
    
    void 测试容器重分配性能(int 轮数, size_t 大小) {
        std::cout << "\n--- 容器重分配性能测试 ---" << std::endl;
        
        class 可移动对象 {
        private:
            std::unique_ptr<int[]> data_;
            size_t size_;
            std::string name_;
            
        public:
            可移动对象(const std::string& name, size_t size) 
                : name_(name), size_(size), data_(std::make_unique<int[]>(size)) {
                std::cout << "构造 " << name_ << " (大小: " << size_ << ")" << std::endl;
            }
            
            // 拷贝构造（模拟昂贵操作）
            可移动对象(const 可移动对象& other) 
                : name_(other.name_ + "_copy"), size_(other.size_), 
                  data_(std::make_unique<int[]>(other.size_)) {
                std::cout << "拷贝构造 " << name_ << " (昂贵操作)" << std::endl;
                std::copy(other.data_.get(), other.data_.get() + size_, data_.get());
            }
            
            // 移动构造（高效）
            可移动对象(可移动对象&& other) noexcept 
                : name_(std::move(other.name_)), size_(other.size_), 
                  data_(std::move(other.data_)) {
                std::cout << "移动构造 " << name_ << " (高效操作)" << std::endl;
                other.size_ = 0;
            }
            
            MoveableClass& operator=(const MoveableClass&) = delete;
            MoveableClass& operator=(MoveableClass&&) = delete;
            
            size_t Size() const { return size_; }
        };
        
        std::vector<MoveableClass> vec;
        vec.reserve(3);
        
        std::cout << "使用emplace_back (直接构造):" << std::endl;
        vec.emplace_back("obj1", 1000);
        
        std::cout << "\n使用push_back临时对象 (移动构造):" << std::endl;
        vec.push_back(MoveableClass("obj2", 1000));
        
        std::cout << "\n通过move传递左值 (移动构造):" << std::endl;
        MoveableClass obj3("obj3", 1000);
        vec.push_back(std::move(obj3));
        
        std::cout << "\nvector中的对象:" << std::endl;
        for (const auto& obj : vec) {
            std::cout << "  " << obj.GetName() << std::endl;
        }
    }
};
```

### 5.8 Part 5 总结

> **🎯 Part 5 核心要点**：

#### 移动语义技术总结

| 技术 | 作用 | 性能提升 | 使用场景 |
|------|------|----------|----------|
| **移动构造/赋值** | 转移资源所有权 | 显著提升 | 大对象传递、容器操作 |
| **右值引用** | 绑定临时对象 | 避免拷贝 | 函数参数、返回值 |
| **完美转发** | 保持参数特性 | 减少开销 | 模板函数、工厂模式 |
| **RVO/NRVO** | 编译器优化 | 零开销 | 函数返回值 |

#### 设计原则

1. **资源转移原则**：移动后源对象应处于有效但未定义状态
2. **异常安全原则**：移动操作应标记为noexcept
3. **性能优先原则**：优先使用移动语义而非拷贝
4. **编译器友好原则**：让编译器自动优化，避免过度使用std::move

#### 最佳实践

- ✅ 实现Rule of Five（或Rule of Zero）
- ✅ 移动构造/赋值标记为noexcept
- ✅ 使用std::move转移不再需要的资源
- ✅ 使用std::forward实现完美转发
- ✅ 让编译器进行RVO优化
- ❌ 避免对返回值使用std::move
- ❌ 避免对const对象使用std::move

**接下来**：Part 6将探讨内存池与自定义分配器技术，学习高级内存管理策略。

---

## Part 6: 内存池与自定义分配——高级内存管理技术

> **学习目标**：掌握内存池设计模式，理解自定义分配器的实现原理，学会针对特定场景优化内存分配性能，构建高效的内存管理系统。

### 6.1 内存池基础：批量分配的艺术

#### 6.1.1 内存池的核心思想

```mermaid
graph TD
    A["传统内存分配"] --> B["每次调用malloc/new<br/>系统调用开销大"]
    B --> C["内存碎片化<br/>分配效率低"]

    D["内存池技术"] --> E["预分配大块内存<br/>减少系统调用"]
    E --> F["统一管理分配<br/>减少碎片化"]
    F --> G["快速分配释放<br/>O(1)时间复杂度"]

    classDef traditional fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef pool fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A,B,C traditional
    class D,E,F,G pool
```

#### 6.1.2 简单内存池实现

```cpp
#include <iostream>
#include <vector>
#include <memory>
#include <cstdlib>

class 简单内存池 {
private:
    char* 内存块;
    size_t 总大小;
    size_t 当前位置;
    bool 拥有内存;

public:
    简单内存池(size_t 大小) : 总大小(大小), 当前位置(0), 拥有内存(true) {
        内存块 = static_cast<char*>(std::malloc(大小));
        if (!内存块) {
            throw std::bad_alloc();
        }
        std::cout << "创建内存池，大小：" << 大小 << " 字节" << std::endl;
    }

    ~简单内存池() {
        if (拥有内存 && 内存块) {
            std::free(内存块);
            std::cout << "销毁内存池，释放 " << 总大小 << " 字节" << std::endl;
        }
    }

    // 禁止拷贝
    简单内存池(const 简单内存池&) = delete;
    简单内存池& operator=(const 简单内存池&) = delete;

    // 允许移动
    简单内存池(简单内存池&& other) noexcept
        : 内存块(other.内存块), 总大小(other.总大小),
          当前位置(other.当前位置), 拥有内存(other.拥有内存) {
        other.内存块 = nullptr;
        other.拥有内存 = false;
    }

    template<typename T>
    T* 分配(size_t 数量 = 1) {
        size_t 需要大小 = sizeof(T) * 数量;
        size_t 对齐大小 = 对齐到(需要大小, alignof(T));

        if (当前位置 + 对齐大小 > 总大小) {
            std::cout << "内存池空间不足，需要：" << 对齐大小
                      << "，剩余：" << (总大小 - 当前位置) << std::endl;
            return nullptr;
        }

        // 确保对齐
        size_t 对齐位置 = 对齐到(当前位置, alignof(T));
        T* 结果 = reinterpret_cast<T*>(内存块 + 对齐位置);
        当前位置 = 对齐位置 + 需要大小;

        std::cout << "从内存池分配 " << 需要大小 << " 字节，类型："
                  << typeid(T).name() << std::endl;
        return 结果;
    }

    void 重置() {
        当前位置 = 0;
        std::cout << "内存池已重置" << std::endl;
    }

    size_t 已使用() const { return 当前位置; }
    size_t 剩余() const { return 总大小 - 当前位置; }
    double 使用率() const { return (double)当前位置 / 总大小 * 100; }

private:
    size_t 对齐到(size_t 值, size_t 对齐) const {
        return (值 + 对齐 - 1) & ~(对齐 - 1);
    }
};

void 简单内存池演示() {
    std::cout << "=== 简单内存池演示 ===" << std::endl;

    简单内存池 池(1024);  // 1KB内存池

    // 分配不同类型的对象
    int* 整数 = 池.分配<int>(10);
    double* 浮点数 = 池.分配<double>(5);
    char* 字符串 = 池.分配<char>(100);

    if (整数 && 浮点数 && 字符串) {
        // 使用分配的内存
        for (int i = 0; i < 10; ++i) {
            整数[i] = i * i;
        }

        for (int i = 0; i < 5; ++i) {
            浮点数[i] = i * 3.14;
        }

        std::strcpy(字符串, "Hello, Memory Pool!");

        std::cout << "内存池使用情况：" << std::endl;
        std::cout << "已使用：" << 池.已使用() << " 字节" << std::endl;
        std::cout << "剩余：" << 池.剩余() << " 字节" << std::endl;
        std::cout << "使用率：" << 池.使用率() << "%" << std::endl;

        // 验证数据
        std::cout << "整数数组：";
        for (int i = 0; i < 10; ++i) {
            std::cout << 整数[i] << " ";
        }
        std::cout << std::endl;

        std::cout << "字符串：" << 字符串 << std::endl;
    }

    // 重置内存池
    池.重置();
    std::cout << "重置后使用率：" << 池.使用率() << "%" << std::endl;
}
```

### 6.2 对象池：特定类型的高效管理

#### 6.2.1 对象池设计模式

```cpp
template<typename T>
class 对象池 {
private:
    std::vector<std::unique_ptr<T>> 可用对象池;
    std::vector<std::unique_ptr<T>> 使用中对象池;
    size_t 最大容量;
    size_t 创建计数;
    size_t 重用计数;

public:
    对象池(size_t 最大容量 = 100) : 最大容量(最大容量), 创建计数(0), 重用计数(0) {
        可用对象池.reserve(最大容量);
        使用中对象池.reserve(最大容量);
        std::cout << "创建对象池，最大容量：" << 最大容量 << std::endl;
    }

    ~对象池() {
        std::cout << "销毁对象池，统计信息：" << std::endl;
        std::cout << "  创建对象：" << 创建计数 << " 个" << std::endl;
        std::cout << "  重用对象：" << 重用计数 << " 个" << std::endl;
        std::cout << "  重用率：" << (重用计数 * 100.0 / (创建计数 + 重用计数)) << "%" << std::endl;
    }

    template<typename... Args>
    T* 获取对象(Args&&... args) {
        std::unique_ptr<T> 对象;

        if (!可用对象池.empty()) {
            // 重用现有对象
            对象 = std::move(可用对象池.back());
            可用对象池.pop_back();
            重用计数++;
            std::cout << "重用对象，当前可用：" << 可用对象池.size() << std::endl;
        } else {
            // 创建新对象
            对象 = std::make_unique<T>(std::forward<Args>(args)...);
            创建计数++;
            std::cout << "创建新对象，总创建：" << 创建计数 << std::endl;
        }

        T* 原始指针 = 对象.get();
        使用中对象池.push_back(std::move(对象));
        return 原始指针;
    }

    void 归还对象(T* 对象指针) {
        auto it = std::find_if(使用中对象池.begin(), 使用中对象池.end(),
            [对象指针](const std::unique_ptr<T>& ptr) {
                return ptr.get() == 对象指针;
            });

        if (it != 使用中对象池.end()) {
            if (可用对象池.size() < 最大容量) {
                // 归还到可用池
                可用对象池.push_back(std::move(*it));
                std::cout << "对象已归还，当前可用：" << 可用对象池.size() << std::endl;
            } else {
                std::cout << "对象池已满，直接销毁对象" << std::endl;
            }
            使用中对象池.erase(it);
        } else {
            std::cout << "警告：试图归还未知对象" << std::endl;
        }
    }

    size_t 可用数量() const { return 可用对象池.size(); }
    size_t 使用中数量() const { return 使用中对象池.size(); }
    size_t 总对象数() const { return 可用数量() + 使用中数量(); }

    void 打印统计信息() const {
        std::cout << "对象池统计：" << std::endl;
        std::cout << "  可用对象：" << 可用数量() << std::endl;
        std::cout << "  使用中对象：" << 使用中数量() << std::endl;
        std::cout << "  总对象数：" << 总对象数() << std::endl;
        std::cout << "  创建次数：" << 创建计数 << std::endl;
        std::cout << "  重用次数：" << 重用计数 << std::endl;
    }
};

// 测试用的资源类
class 昂贵资源 {
private:
    std::vector<int> 数据;
    std::string 名称;
    static int 实例计数;

public:
    昂贵资源(const std::string& 名称 = "默认") : 名称(名称), 数据(10000, 42) {
        实例计数++;
        std::cout << "创建昂贵资源：" << 名称 << " (实例#" << 实例计数 << ")" << std::endl;
    }

    ~昂贵资源() {
        std::cout << "销毁昂贵资源：" << 名称 << std::endl;
    }

    void 重置(const std::string& 新名称) {
        名称 = 新名称;
        std::fill(数据.begin(), 数据.end(), 0);
        std::cout << "重置资源为：" << 名称 << std::endl;
    }

    void 工作() {
        std::cout << "资源 " << 名称 << " 正在工作..." << std::endl;
        // 模拟一些工作
        for (size_t i = 0; i < 数据.size(); ++i) {
            数据[i] = static_cast<int>(i % 100);
        }
    }

    const std::string& 获取名称() const { return 名称; }
};

int 昂贵资源::实例计数 = 0;

void 对象池演示() {
    std::cout << "\n=== 对象池演示 ===" << std::endl;

    对象池<昂贵资源> 池(3);  // 最多缓存3个对象

    // 第一轮：创建和使用对象
    std::cout << "\n--- 第一轮：创建对象 ---" << std::endl;
    昂贵资源* obj1 = 池.获取对象("任务1");
    昂贵资源* obj2 = 池.获取对象("任务2");
    昂贵资源* obj3 = 池.获取对象("任务3");

    obj1->工作();
    obj2->工作();
    obj3->工作();

    池.打印统计信息();

    // 归还对象
    std::cout << "\n--- 归还对象 ---" << std::endl;
    池.归还对象(obj1);
    池.归还对象(obj2);
    池.归还对象(obj3);

    池.打印统计信息();

    // 第二轮：重用对象
    std::cout << "\n--- 第二轮：重用对象 ---" << std::endl;
    昂贵资源* obj4 = 池.获取对象("任务4");  // 应该重用现有对象
    昂贵资源* obj5 = 池.获取对象("任务5");  // 应该重用现有对象

    obj4->重置("重用任务4");
    obj5->重置("重用任务5");

    obj4->工作();
    obj5->工作();

    池.打印统计信息();

    // 清理
    池.归还对象(obj4);
    池.归还对象(obj5);

    std::cout << "\n--- 最终统计 ---" << std::endl;
    池.打印统计信息();
}
```

### 6.3 内存对齐与SIMD优化

#### 6.3.1 对齐内存分配器

```cpp
#include <immintrin.h>  // SIMD指令
#include <cstring>

class 对齐内存分配器 {
private:
    size_t 对齐大小;
    std::vector<void*> 已分配内存;

public:
    对齐内存分配器(size_t 对齐 = 32) : 对齐大小(对齐) {
        std::cout << "创建对齐内存分配器，对齐大小：" << 对齐 << " 字节" << std::endl;
    }

    ~对齐内存分配器() {
        for (void* ptr : 已分配内存) {
            if (ptr) {
                #ifdef _WIN32
                    _aligned_free(ptr);
                #else
                    std::free(ptr);
                #endif
            }
        }
        std::cout << "对齐内存分配器已销毁，释放了 " << 已分配内存.size() << " 个内存块" << std::endl;
    }

    template<typename T>
    T* 分配对齐内存(size_t 数量) {
        size_t 总大小 = sizeof(T) * 数量;
        void* ptr = nullptr;

        #ifdef _WIN32
            ptr = _aligned_malloc(总大小, 对齐大小);
        #else
            if (posix_memalign(&ptr, 对齐大小, 总大小) != 0) {
                ptr = nullptr;
            }
        #endif

        if (!ptr) {
            throw std::bad_alloc();
        }

        已分配内存.push_back(ptr);
        std::cout << "分配对齐内存：" << 总大小 << " 字节，地址：" << ptr
                  << "，对齐检查：" << (reinterpret_cast<uintptr_t>(ptr) % 对齐大小 == 0 ? "✓" : "✗") << std::endl;

        return static_cast<T*>(ptr);
    }

    void 释放内存(void* ptr) {
        auto it = std::find(已分配内存.begin(), 已分配内存.end(), ptr);
        if (it != 已分配内存.end()) {
            #ifdef _WIN32
                _aligned_free(ptr);
            #else
                std::free(ptr);
            #endif
            已分配内存.erase(it);
            std::cout << "释放对齐内存：" << ptr << std::endl;
        }
    }
};

void SIMD优化演示() {
    std::cout << "=== SIMD优化演示 ===" << std::endl;

    const size_t 数组大小 = 1024;
    对齐内存分配器 分配器(32);  // AVX需要32字节对齐

    // 分配对齐的内存
    float* 数组A = 分配器.分配对齐内存<float>(数组大小);
    float* 数组B = 分配器.分配对齐内存<float>(数组大小);
    float* 结果1 = 分配器.分配对齐内存<float>(数组大小);
    float* 结果2 = 分配器.分配对齐内存<float>(数组大小);

    // 初始化数据
    for (size_t i = 0; i < 数组大小; ++i) {
        数组A[i] = static_cast<float>(i);
        数组B[i] = static_cast<float>(i * 2);
    }

    // 标量版本
    auto 开始 = std::chrono::high_resolution_clock::now();
    for (size_t i = 0; i < 数组大小; ++i) {
        结果1[i] = 数组A[i] + 数组B[i];
    }
    auto 结束 = std::chrono::high_resolution_clock::now();
    auto 标量时间 = std::chrono::duration_cast<std::chrono::nanoseconds>(结束 - 开始);

    // SIMD版本 (AVX)
    开始 = std::chrono::high_resolution_clock::now();
    for (size_t i = 0; i < 数组大小; i += 8) {
        __m256 a = _mm256_load_ps(&数组A[i]);
        __m256 b = _mm256_load_ps(&数组B[i]);
        __m256 result = _mm256_add_ps(a, b);
        _mm256_store_ps(&结果2[i], result);
    }
    结束 = std::chrono::high_resolution_clock::now();
    auto SIMD时间 = std::chrono::duration_cast<std::chrono::nanoseconds>(结束 - 开始);

    // 验证结果
    bool 结果正确 = true;
    for (size_t i = 0; i < 数组大小; ++i) {
        if (std::abs(结果1[i] - 结果2[i]) > 1e-6) {
            结果正确 = false;
            break;
        }
    }

    std::cout << "数组大小：" << 数组大小 << std::endl;
    std::cout << "标量计算时间：" << 标量时间.count() << " 纳秒" << std::endl;
    std::cout << "SIMD计算时间：" << SIMD时间.count() << " 纳秒" << std::endl;
    std::cout << "性能提升：" << (double)标量时间.count() / SIMD时间.count() << " 倍" << std::endl;
    std::cout << "结果正确性：" << (结果正确 ? "✓" : "✗") << std::endl;
}
```

### 6.4 自定义分配器：STL容器的内存优化

#### 6.4.1 标准分配器接口

```cpp
template<typename T>
class 自定义分配器 {
public:
    using value_type = T;
    using pointer = T*;
    using const_pointer = const T*;
    using reference = T&;
    using const_reference = const T&;
    using size_type = std::size_t;
    using difference_type = std::ptrdiff_t;

    // 重绑定到其他类型
    template<typename U>
    struct rebind {
        using other = 自定义分配器<U>;
    };

private:
    static size_t 分配计数;
    static size_t 释放计数;
    static size_t 当前使用内存;

public:
    自定义分配器() noexcept = default;

    template<typename U>
    自定义分配器(const 自定义分配器<U>&) noexcept {}

    pointer allocate(size_type n) {
        if (n > std::numeric_limits<size_type>::max() / sizeof(T)) {
            throw std::bad_alloc();
        }

        size_t 字节数 = n * sizeof(T);
        pointer ptr = static_cast<pointer>(std::malloc(字节数));

        if (!ptr) {
            throw std::bad_alloc();
        }

        分配计数++;
        当前使用内存 += 字节数;

        std::cout << "自定义分配器分配：" << 字节数 << " 字节，地址：" << ptr
                  << "，总分配：" << 分配计数 << " 次" << std::endl;

        return ptr;
    }

    void deallocate(pointer ptr, size_type n) noexcept {
        size_t 字节数 = n * sizeof(T);
        std::free(ptr);

        释放计数++;
        当前使用内存 -= 字节数;

        std::cout << "自定义分配器释放：" << 字节数 << " 字节，地址：" << ptr
                  << "，总释放：" << 释放计数 << " 次" << std::endl;
    }

    template<typename U, typename... Args>
    void construct(U* ptr, Args&&... args) {
        new(ptr) U(std::forward<Args>(args)...);
    }

    template<typename U>
    void destroy(U* ptr) {
        ptr->~U();
    }

    static void 打印统计信息() {
        std::cout << "=== 自定义分配器统计 ===" << std::endl;
        std::cout << "总分配次数：" << 分配计数 << std::endl;
        std::cout << "总释放次数：" << 释放计数 << std::endl;
        std::cout << "当前使用内存：" << 当前使用内存 << " 字节" << std::endl;
        std::cout << "内存泄漏检查：" << (分配计数 == 释放计数 ? "✓ 无泄漏" : "✗ 可能有泄漏") << std::endl;
    }

    static void 重置统计() {
        分配计数 = 0;
        释放计数 = 0;
        当前使用内存 = 0;
    }
};

// 静态成员定义
template<typename T>
size_t 自定义分配器<T>::分配计数 = 0;

template<typename T>
size_t 自定义分配器<T>::释放计数 = 0;

template<typename T>
size_t 自定义分配器<T>::当前使用内存 = 0;

// 比较操作符
template<typename T, typename U>
bool operator==(const 自定义分配器<T>&, const 自定义分配器<U>&) noexcept {
    return true;
}

template<typename T, typename U>
bool operator!=(const 自定义分配器<T>&, const 自定义分配器<U>&) noexcept {
    return false;
}

void 自定义分配器演示() {
    std::cout << "=== 自定义分配器演示 ===" << std::endl;

    自定义分配器<int>::重置统计();

    {
        // 使用自定义分配器的vector
        std::vector<int, 自定义分配器<int>> 自定义vector;

        std::cout << "\n--- 添加元素到vector ---" << std::endl;
        for (int i = 0; i < 10; ++i) {
            自定义vector.push_back(i);
        }

        std::cout << "\nvector内容：";
        for (const auto& value : 自定义vector) {
            std::cout << value << " ";
        }
        std::cout << std::endl;

        自定义分配器<int>::打印统计信息();

        std::cout << "\n--- 扩容vector ---" << std::endl;
        自定义vector.reserve(100);

        自定义分配器<int>::打印统计信息();

        std::cout << "\n--- vector离开作用域 ---" << std::endl;
    }

    自定义分配器<int>::打印统计信息();
}
```

#### 6.4.2 内存池分配器

```cpp
template<typename T, size_t 池大小 = 1024>
class 内存池分配器 {
private:
    struct 内存池 {
        alignas(T) char 数据[池大小 * sizeof(T)];
        bool 使用标记[池大小];
        size_t 下次搜索位置;

        内存池() : 下次搜索位置(0) {
            std::fill(std::begin(使用标记), std::end(使用标记), false);
        }
    };

    static std::vector<std::unique_ptr<内存池>> 内存池列表;
    static size_t 当前池索引;

public:
    using value_type = T;
    using pointer = T*;
    using const_pointer = const T*;
    using size_type = std::size_t;
    using difference_type = std::ptrdiff_t;

    template<typename U>
    struct rebind {
        using other = 内存池分配器<U, 池大小>;
    };

    内存池分配器() noexcept = default;

    template<typename U>
    内存池分配器(const 内存池分配器<U, 池大小>&) noexcept {}

    pointer allocate(size_type n) {
        if (n != 1) {
            // 对于多个对象的分配，回退到标准分配器
            return static_cast<pointer>(std::malloc(n * sizeof(T)));
        }

        // 在现有池中查找空闲位置
        for (auto& 池 : 内存池列表) {
            pointer ptr = 从池中分配(池.get());
            if (ptr) {
                return ptr;
            }
        }

        // 创建新池
        auto 新池 = std::make_unique<内存池>();
        pointer ptr = 从池中分配(新池.get());
        内存池列表.push_back(std::move(新池));

        std::cout << "创建新内存池，总池数：" << 内存池列表.size() << std::endl;
        return ptr;
    }

    void deallocate(pointer ptr, size_type n) noexcept {
        if (n != 1) {
            std::free(ptr);
            return;
        }

        // 在池中标记为未使用
        for (auto& 池 : 内存池列表) {
            if (在池范围内(ptr, 池.get())) {
                size_t 索引 = static_cast<T*>(ptr) - reinterpret_cast<T*>(池->数据);
                池->使用标记[索引] = false;
                池->下次搜索位置 = std::min(池->下次搜索位置, 索引);
                return;
            }
        }
    }

    template<typename U, typename... Args>
    void construct(U* ptr, Args&&... args) {
        new(ptr) U(std::forward<Args>(args)...);
    }

    template<typename U>
    void destroy(U* ptr) {
        ptr->~U();
    }

    static void 打印池统计() {
        std::cout << "=== 内存池分配器统计 ===" << std::endl;
        std::cout << "总池数：" << 内存池列表.size() << std::endl;

        for (size_t i = 0; i < 内存池列表.size(); ++i) {
            size_t 已使用 = 0;
            for (size_t j = 0; j < 池大小; ++j) {
                if (内存池列表[i]->使用标记[j]) {
                    已使用++;
                }
            }
            std::cout << "池 " << i << "：已使用 " << 已使用 << "/" << 池大小
                      << " (" << (已使用 * 100.0 / 池大小) << "%)" << std::endl;
        }
    }

    static void 清理所有池() {
        内存池列表.clear();
        当前池索引 = 0;
        std::cout << "已清理所有内存池" << std::endl;
    }

private:
    pointer 从池中分配(内存池* 池) {
        for (size_t i = 池->下次搜索位置; i < 池大小; ++i) {
            if (!池->使用标记[i]) {
                池->使用标记[i] = true;
                池->下次搜索位置 = i + 1;
                return reinterpret_cast<T*>(&池->数据[i * sizeof(T)]);
            }
        }

        // 从头开始搜索
        for (size_t i = 0; i < 池->下次搜索位置; ++i) {
            if (!池->使用标记[i]) {
                池->使用标记[i] = true;
                池->下次搜索位置 = i + 1;
                return reinterpret_cast<T*>(&池->数据[i * sizeof(T)]);
            }
        }

        return nullptr;  // 池已满
    }

    bool 在池范围内(pointer ptr, 内存池* 池) {
        char* 池开始 = 池->数据;
        char* 池结束 = 池开始 + sizeof(池->数据);
        char* 指针位置 = reinterpret_cast<char*>(ptr);
        return 指针位置 >= 池开始 && 指针位置 < 池结束;
    }
};

// 静态成员定义
template<typename T, size_t 池大小>
std::vector<std::unique_ptr<typename 内存池分配器<T, 池大小>::内存池>>
内存池分配器<T, 池大小>::内存池列表;

template<typename T, size_t 池大小>
size_t 内存池分配器<T, 池大小>::当前池索引 = 0;

void 内存池分配器演示() {
    std::cout << "\n=== 内存池分配器演示 ===" << std::endl;

    {
        // 使用内存池分配器的list
        std::list<int, 内存池分配器<int, 64>> 池化列表;

        std::cout << "添加100个元素到list：" << std::endl;
        for (int i = 0; i < 100; ++i) {
            池化列表.push_back(i);
        }

        内存池分配器<int, 64>::打印池统计();

        std::cout << "\n删除50个元素：" << std::endl;
        for (int i = 0; i < 50; ++i) {
            池化列表.pop_back();
        }

        内存池分配器<int, 64>::打印池统计();

        std::cout << "\n再添加30个元素（应该重用释放的空间）：" << std::endl;
        for (int i = 100; i < 130; ++i) {
            池化列表.push_back(i);
        }

        内存池分配器<int, 64>::打印池统计();
    }

    std::cout << "\nlist销毁后：" << std::endl;
    内存池分配器<int, 64>::打印池统计();
    内存池分配器<int, 64>::清理所有池();
}
```

### 6.5 高级内存管理技术

#### 6.5.1 栈分配器

```cpp
template<size_t 栈大小>
class 栈分配器 {
private:
    alignas(std::max_align_t) char 栈内存[栈大小];
    char* 栈顶;

    struct 标记 {
        char* 位置;
        标记(char* pos) : 位置(pos) {}
    };

public:
    栈分配器() : 栈顶(栈内存) {
        std::cout << "创建栈分配器，大小：" << 栈大小 << " 字节" << std::endl;
    }

    template<typename T>
    T* 分配(size_t 数量 = 1) {
        size_t 需要大小 = sizeof(T) * 数量;
        size_t 对齐大小 = 对齐到(需要大小, alignof(T));

        if (栈顶 + 对齐大小 > 栈内存 + 栈大小) {
            throw std::bad_alloc();
        }

        // 对齐栈顶
        char* 对齐位置 = reinterpret_cast<char*>(
            对齐到(reinterpret_cast<uintptr_t>(栈顶), alignof(T))
        );

        T* 结果 = reinterpret_cast<T*>(对齐位置);
        栈顶 = 对齐位置 + 需要大小;

        std::cout << "栈分配：" << 需要大小 << " 字节，剩余："
                  << (栈内存 + 栈大小 - 栈顶) << " 字节" << std::endl;

        return 结果;
    }

    标记 获取标记() {
        return 标记(栈顶);
    }

    void 回滚到标记(const 标记& mark) {
        if (mark.位置 >= 栈内存 && mark.位置 <= 栈顶) {
            栈顶 = mark.位置;
            std::cout << "回滚栈，剩余：" << (栈内存 + 栈大小 - 栈顶) << " 字节" << std::endl;
        }
    }

    void 重置() {
        栈顶 = 栈内存;
        std::cout << "栈已重置" << std::endl;
    }

    size_t 已使用() const {
        return 栈顶 - 栈内存;
    }

    size_t 剩余() const {
        return 栈大小 - 已使用();
    }

private:
    size_t 对齐到(size_t 值, size_t 对齐) const {
        return (值 + 对齐 - 1) & ~(对齐 - 1);
    }
};

void 栈分配器演示() {
    std::cout << "\n=== 栈分配器演示 ===" << std::endl;

    栈分配器<1024> 栈(1024);

    // 分配一些对象
    int* 整数数组 = 栈.分配<int>(10);
    double* 浮点数 = 栈.分配<double>(5);

    // 获取标记点
    auto 标记点 = 栈.获取标记();

    // 继续分配
    char* 字符数组 = 栈.分配<char>(100);

    std::cout << "标记点后分配，已使用：" << 栈.已使用() << " 字节" << std::endl;

    // 回滚到标记点
    栈.回滚到标记(标记点);
    std::cout << "回滚后，已使用：" << 栈.已使用() << " 字节" << std::endl;

    // 重新分配
    float* 浮点数组 = 栈.分配<float>(20);

    // 使用分配的内存
    for (int i = 0; i < 10; ++i) {
        整数数组[i] = i;
    }

    for (int i = 0; i < 5; ++i) {
        浮点数[i] = i * 3.14;
    }

    for (int i = 0; i < 20; ++i) {
        浮点数组[i] = i * 2.5f;
    }

    std::cout << "最终使用：" << 栈.已使用() << " 字节" << std::endl;
}
```

### 6.6 Part 6 总结

> **🎯 Part 6 核心要点**：

#### 高级内存管理技术总结

| 技术 | 适用场景 | 优势 | 注意事项 |
|------|----------|------|----------|
| **内存池** | 频繁分配释放同大小对象 | 减少碎片，提高性能 | 需要预估使用量 |
| **对象池** | 昂贵对象的重复使用 | 避免构造析构开销 | 对象状态需要重置 |
| **栈分配器** | 临时对象，作用域明确 | 极快分配，自动清理 | 生命周期受限 |
| **自定义分配器** | 特定容器优化 | 精确控制内存行为 | 实现复杂度高 |
| **对齐分配** | SIMD优化，硬件要求 | 提升计算性能 | 内存开销增加 |

#### 设计原则

1. **局部性原则**：相关数据放在一起，提高缓存效率
2. **预分配原则**：提前分配大块内存，减少系统调用
3. **重用原则**：尽可能重用已分配的内存
4. **对齐原则**：满足硬件对齐要求，提升性能

#### 最佳实践

- ✅ 根据使用模式选择合适的内存管理策略
- ✅ 使用内存池减少频繁分配的开销
- ✅ 对昂贵对象使用对象池
- ✅ 考虑内存对齐优化SIMD性能
- ✅ 自定义分配器优化特定场景
- ❌ 避免过度优化，保持代码简洁
- ❌ 注意内存池的生命周期管理

**接下来**：Part 7将介绍内存调试与工具，学习如何诊断和解决内存问题。

---

## Part 7: 调试与工具——内存问题诊断和解决

> **学习目标**：掌握内存调试技术，学会使用各种内存分析工具，能够快速定位和解决内存泄漏、越界访问等问题，建立完善的内存问题预防机制。

### 7.1 常见内存问题类型

#### 7.1.1 内存问题分类图

```mermaid
graph TD
    A["🔍 内存问题"] --> B["💧 内存泄漏<br/>Memory Leaks"]
    A --> C["🚫 非法访问<br/>Invalid Access"]
    A --> D["🔄 重复释放<br/>Double Free"]
    A --> E["🎯 野指针<br/>Dangling Pointer"]
    A --> F["📊 内存碎片<br/>Fragmentation"]

    B --> B1["忘记释放<br/>Forgot to delete"]
    B --> B2["循环引用<br/>Circular Reference"]
    B --> B3["异常安全<br/>Exception Safety"]

    C --> C1["缓冲区溢出<br/>Buffer Overflow"]
    C --> C2["数组越界<br/>Array Bounds"]
    C --> C3["空指针解引用<br/>Null Dereference"]

    D --> D1["delete后再delete<br/>Double Delete"]
    D --> D2["free后再free<br/>Double Free"]

    E --> E1["使用已释放内存<br/>Use After Free"]
    E --> E2["返回局部变量地址<br/>Return Local Address"]

    classDef problem fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef category fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class A problem
    class B,C,D,E,F category
```

#### 7.1.2 内存问题示例代码

```cpp
#include <iostream>
#include <vector>
#include <memory>

class 内存问题演示 {
public:
    // 问题1：内存泄漏
    void 内存泄漏示例() {
        std::cout << "=== 内存泄漏示例 ===" << std::endl;

        // ❌ 错误：忘记释放内存
        int* 泄漏指针 = new int[1000];
        // 忘记 delete[] 泄漏指针;

        // ❌ 错误：异常导致的内存泄漏
        try {
            int* 临时指针 = new int[1000];
            // 如果这里抛出异常，临时指针不会被释放
            可能抛出异常的函数();
            delete[] 临时指针;
        } catch (...) {
            // 内存泄漏了！
            std::cout << "异常捕获，但内存已泄漏" << std::endl;
        }

        // ✅ 正确：使用智能指针
        auto 安全指针 = std::make_unique<int[]>(1000);
        // 自动释放，异常安全
    }

    // 问题2：缓冲区溢出
    void 缓冲区溢出示例() {
        std::cout << "\n=== 缓冲区溢出示例 ===" << std::endl;

        char 缓冲区[10];

        // ❌ 错误：写入超出缓冲区边界
        for (int i = 0; i <= 10; ++i) {  // 注意：i <= 10 会越界
            缓冲区[i] = 'A' + i;
        }

        // ❌ 错误：字符串拷贝溢出
        const char* 长字符串 = "这是一个很长的字符串，会导致缓冲区溢出";
        strcpy(缓冲区, 长字符串);  // 危险！

        // ✅ 正确：使用安全的字符串操作
        std::string 安全字符串 = "这是一个安全的字符串";
        std::cout << "安全字符串：" << 安全字符串 << std::endl;
    }

    // 问题3：重复释放
    void 重复释放示例() {
        std::cout << "\n=== 重复释放示例 ===" << std::endl;

        int* ptr = new int(42);

        // ❌ 错误：重复释放
        delete ptr;
        // delete ptr;  // 这会导致未定义行为

        // ✅ 正确：释放后置空
        ptr = nullptr;
        delete ptr;  // 删除nullptr是安全的

        // ✅ 更好：使用智能指针
        auto 智能ptr = std::make_unique<int>(42);
        // 自动管理，不会重复释放
    }

    // 问题4：野指针
    void 野指针示例() {
        std::cout << "\n=== 野指针示例 ===" << std::endl;

        int* 野指针 = nullptr;

        {
            int 局部变量 = 42;
            野指针 = &局部变量;  // 指向局部变量
        }  // 局部变量销毁

        // ❌ 错误：使用野指针
        // std::cout << *野指针 << std::endl;  // 未定义行为

        // ✅ 正确：检查指针有效性
        if (野指针 != nullptr) {
            // 但这里仍然不安全，因为指向的内存已无效
            std::cout << "指针不为空，但指向的内存可能无效" << std::endl;
        }
    }

    // 问题5：数组越界
    void 数组越界示例() {
        std::cout << "\n=== 数组越界示例 ===" << std::endl;

        int 数组[5] = {1, 2, 3, 4, 5};

        // ❌ 错误：读取越界
        for (int i = 0; i <= 5; ++i) {  // i=5时越界
            std::cout << "数组[" << i << "] = " << 数组[i] << std::endl;
        }

        // ✅ 正确：使用容器和范围检查
        std::vector<int> 安全数组 = {1, 2, 3, 4, 5};
        try {
            std::cout << "安全访问：" << 安全数组.at(10) << std::endl;
        } catch (const std::out_of_range& e) {
            std::cout << "捕获越界异常：" << e.what() << std::endl;
        }
    }

private:
    void 可能抛出异常的函数() {
        // 模拟可能抛出异常的函数
        if (rand() % 2) {
            throw std::runtime_error("随机异常");
        }
    }
};
```

### 7.2 内存调试技术

#### 7.2.1 自定义内存跟踪器

```cpp
#include <unordered_map>
#include <mutex>
#include <fstream>

class 内存跟踪器 {
private:
    struct 分配信息 {
        size_t 大小;
        const char* 文件名;
        int 行号;
        std::chrono::time_point<std::chrono::steady_clock> 时间戳;

        分配信息(size_t 大小, const char* 文件, int 行)
            : 大小(大小), 文件名(文件), 行号(行),
              时间戳(std::chrono::steady_clock::now()) {}
    };

    std::unordered_map<void*, 分配信息> 分配记录;
    std::mutex 互斥锁;
    size_t 总分配大小;
    size_t 总分配次数;
    size_t 总释放次数;
    bool 启用跟踪;

public:
    static 内存跟踪器& 获取实例() {
        static 内存跟踪器 实例;
        return 实例;
    }

    void* 分配内存(size_t 大小, const char* 文件, int 行) {
        void* ptr = std::malloc(大小);
        if (!ptr) {
            return nullptr;
        }

        if (启用跟踪) {
            std::lock_guard<std::mutex> 锁(互斥锁);
            分配记录.emplace(ptr, 分配信息(大小, 文件, 行));
            总分配大小 += 大小;
            总分配次数++;

            std::cout << "分配内存：" << ptr << "，大小：" << 大小
                      << "，位置：" << 文件 << ":" << 行 << std::endl;
        }

        return ptr;
    }

    void 释放内存(void* ptr, const char* 文件, int 行) {
        if (!ptr) return;

        if (启用跟踪) {
            std::lock_guard<std::mutex> 锁(互斥锁);
            auto it = 分配记录.find(ptr);

            if (it != 分配记录.end()) {
                总分配大小 -= it->second.大小;
                总释放次数++;

                std::cout << "释放内存：" << ptr << "，大小：" << it->second.大小
                          << "，分配位置：" << it->second.文件名 << ":" << it->second.行号
                          << "，释放位置：" << 文件 << ":" << 行 << std::endl;

                分配记录.erase(it);
            } else {
                std::cout << "警告：试图释放未跟踪的内存：" << ptr
                          << "，位置：" << 文件 << ":" << 行 << std::endl;
            }
        }

        std::free(ptr);
    }

    void 打印内存报告() {
        std::lock_guard<std::mutex> 锁(互斥锁);

        std::cout << "\n=== 内存跟踪报告 ===" << std::endl;
        std::cout << "总分配次数：" << 总分配次数 << std::endl;
        std::cout << "总释放次数：" << 总释放次数 << std::endl;
        std::cout << "当前分配大小：" << 总分配大小 << " 字节" << std::endl;
        std::cout << "未释放的内存块：" << 分配记录.size() << " 个" << std::endl;

        if (!分配记录.empty()) {
            std::cout << "\n内存泄漏详情：" << std::endl;
            for (const auto& pair : 分配记录) {
                const auto& 信息 = pair.second;
                auto 持续时间 = std::chrono::steady_clock::now() - 信息.时间戳;
                auto 秒数 = std::chrono::duration_cast<std::chrono::seconds>(持续时间).count();

                std::cout << "  地址：" << pair.first
                          << "，大小：" << 信息.大小
                          << "，位置：" << 信息.文件名 << ":" << 信息.行号
                          << "，持续：" << 秒数 << " 秒" << std::endl;
            }
        }
    }

    void 导出报告到文件(const std::string& 文件名) {
        std::lock_guard<std::mutex> 锁(互斥锁);
        std::ofstream 文件(文件名);

        if (!文件.is_open()) {
            std::cout << "无法打开文件：" << 文件名 << std::endl;
            return;
        }

        文件 << "内存跟踪报告\n";
        文件 << "================\n";
        文件 << "总分配次数：" << 总分配次数 << "\n";
        文件 << "总释放次数：" << 总释放次数 << "\n";
        文件 << "当前分配大小：" << 总分配大小 << " 字节\n";
        文件 << "未释放的内存块：" << 分配记录.size() << " 个\n\n";

        if (!分配记录.empty()) {
            文件 << "内存泄漏详情：\n";
            for (const auto& pair : 分配记录) {
                const auto& 信息 = pair.second;
                文件 << "地址：" << pair.first
                     << "，大小：" << 信息.大小
                     << "，位置：" << 信息.文件名 << ":" << 信息.行号 << "\n";
            }
        }

        std::cout << "内存报告已导出到：" << 文件名 << std::endl;
    }

    void 启用() { 启用跟踪 = true; }
    void 禁用() { 启用跟踪 = false; }

private:
    内存跟踪器() : 总分配大小(0), 总分配次数(0), 总释放次数(0), 启用跟踪(true) {}
    ~内存跟踪器() {
        if (启用跟踪) {
            打印内存报告();
        }
    }
};

// 宏定义，方便使用
#define TRACKED_NEW(size) 内存跟踪器::获取实例().分配内存(size, __FILE__, __LINE__)
#define TRACKED_DELETE(ptr) 内存跟踪器::获取实例().释放内存(ptr, __FILE__, __LINE__)

void 内存跟踪演示() {
    std::cout << "=== 内存跟踪演示 ===" << std::endl;

    // 启用内存跟踪
    内存跟踪器::获取实例().启用();

    // 分配一些内存
    void* ptr1 = TRACKED_NEW(100);
    void* ptr2 = TRACKED_NEW(200);
    void* ptr3 = TRACKED_NEW(300);

    // 释放部分内存
    TRACKED_DELETE(ptr1);
    TRACKED_DELETE(ptr2);

    // ptr3 故意不释放，模拟内存泄漏

    // 打印中间报告
    内存跟踪器::获取实例().打印内存报告();

    // 导出报告到文件
    内存跟踪器::获取实例().导出报告到文件("memory_report.txt");

    // 最后释放剩余内存
    TRACKED_DELETE(ptr3);
}
```

#### 7.2.2 智能指针调试包装器

```cpp
template<typename T>
class 调试智能指针 {
private:
    T* ptr_;
    std::string 创建位置_;
    mutable size_t 访问次数_;
    mutable std::chrono::time_point<std::chrono::steady_clock> 最后访问时间_;
    static size_t 实例计数_;

public:
    调试智能指针(T* ptr = nullptr, const std::string& 位置 = "未知")
        : ptr_(ptr), 创建位置_(位置), 访问次数_(0),
          最后访问时间_(std::chrono::steady_clock::now()) {
        实例计数_++;
        std::cout << "创建调试智能指针 #" << 实例计数_
                  << "，地址：" << ptr_ << "，位置：" << 位置 << std::endl;
    }

    ~调试智能指针() {
        if (ptr_) {
            std::cout << "销毁调试智能指针，地址：" << ptr_
                      << "，访问次数：" << 访问次数_
                      << "，创建位置：" << 创建位置_ << std::endl;
            delete ptr_;
        }
        实例计数_--;
    }

    // 禁止拷贝
    调试智能指针(const 调试智能指针&) = delete;
    调试智能指针& operator=(const 调试智能指针&) = delete;

    // 移动构造
    调试智能指针(调试智能指针&& other) noexcept
        : ptr_(other.ptr_), 创建位置_(std::move(other.创建位置_)),
          访问次数_(other.访问次数_), 最后访问时间_(other.最后访问时间_) {
        other.ptr_ = nullptr;
        std::cout << "移动构造调试智能指针，地址：" << ptr_ << std::endl;
    }

    // 移动赋值
    调试智能指针& operator=(调试智能指针&& other) noexcept {
        if (this != &other) {
            if (ptr_) {
                delete ptr_;
            }

            ptr_ = other.ptr_;
            创建位置_ = std::move(other.创建位置_);
            访问次数_ = other.访问次数_;
            最后访问时间_ = other.最后访问时间_;

            other.ptr_ = nullptr;
            std::cout << "移动赋值调试智能指针，地址：" << ptr_ << std::endl;
        }
        return *this;
    }

    T& operator*() const {
        记录访问("解引用");
        if (!ptr_) {
            throw std::runtime_error("试图解引用空指针，创建位置：" + 创建位置_);
        }
        return *ptr_;
    }

    T* operator->() const {
        记录访问("成员访问");
        if (!ptr_) {
            throw std::runtime_error("试图访问空指针成员，创建位置：" + 创建位置_);
        }
        return ptr_;
    }

    T* get() const {
        记录访问("获取原始指针");
        return ptr_;
    }

    bool operator!() const {
        return ptr_ == nullptr;
    }

    explicit operator bool() const {
        return ptr_ != nullptr;
    }

    void reset(T* new_ptr = nullptr) {
        if (ptr_) {
            std::cout << "重置调试智能指针，旧地址：" << ptr_
                      << "，新地址：" << new_ptr << std::endl;
            delete ptr_;
        }
        ptr_ = new_ptr;
        访问次数_ = 0;
        最后访问时间_ = std::chrono::steady_clock::now();
    }

    T* release() {
        T* temp = ptr_;
        ptr_ = nullptr;
        std::cout << "释放调试智能指针控制权，地址：" << temp << std::endl;
        return temp;
    }

    void 打印调试信息() const {
        auto 现在 = std::chrono::steady_clock::now();
        auto 持续时间 = std::chrono::duration_cast<std::chrono::seconds>(现在 - 最后访问时间_).count();

        std::cout << "调试智能指针信息：" << std::endl;
        std::cout << "  地址：" << ptr_ << std::endl;
        std::cout << "  创建位置：" << 创建位置_ << std::endl;
        std::cout << "  访问次数：" << 访问次数_ << std::endl;
        std::cout << "  最后访问：" << 持续时间 << " 秒前" << std::endl;
        std::cout << "  当前实例数：" << 实例计数_ << std::endl;
    }

    static size_t 获取实例计数() {
        return 实例计数_;
    }

private:
    void 记录访问(const std::string& 操作) const {
        访问次数_++;
        最后访问时间_ = std::chrono::steady_clock::now();
        std::cout << "访问调试智能指针 [" << 操作 << "]，地址：" << ptr_
                  << "，总访问：" << 访问次数_ << " 次" << std::endl;
    }
};

template<typename T>
size_t 调试智能指针<T>::实例计数_ = 0;

// 便利函数
template<typename T, typename... Args>
调试智能指针<T> make_debug_unique(const std::string& 位置, Args&&... args) {
    return 调试智能指针<T>(new T(std::forward<Args>(args)...), 位置);
}

void 调试智能指针演示() {
    std::cout << "\n=== 调试智能指针演示 ===" << std::endl;

    {
        auto ptr1 = make_debug_unique<int>("main函数第1个指针");
        *ptr1 = 42;

        auto ptr2 = make_debug_unique<std::string>("main函数第2个指针");
        *ptr2 = "Hello, Debug!";

        std::cout << "ptr1值：" << *ptr1 << std::endl;
        std::cout << "ptr2值：" << *ptr2 << std::endl;

        ptr1.打印调试信息();
        ptr2.打印调试信息();

        std::cout << "当前智能指针实例数：" << 调试智能指针<int>::获取实例计数() << std::endl;

        // 移动语义测试
        auto ptr3 = std::move(ptr1);
        std::cout << "移动后 ptr1 是否为空：" << (!ptr1 ? "是" : "否") << std::endl;
        std::cout << "移动后 ptr3 值：" << *ptr3 << std::endl;

        // 测试空指针访问
        try {
            *ptr1 = 100;  // 应该抛出异常
        } catch (const std::exception& e) {
            std::cout << "捕获异常：" << e.what() << std::endl;
        }
    }

    std::cout << "作用域结束后实例数：" << 调试智能指针<int>::获取实例计数() << std::endl;
}
```

### 7.3 内存分析工具

#### 7.3.1 工具对比表

| 工具 | 平台 | 检测能力 | 性能影响 | 使用难度 |
|------|------|----------|----------|----------|
| **Valgrind** | Linux/macOS | 内存泄漏、越界、未初始化 | 高(10-50x) | 简单 |
| **AddressSanitizer** | 跨平台 | 越界、use-after-free | 中(2x) | 简单 |
| **Dr. Memory** | Windows | 内存错误、泄漏 | 中等 | 中等 |
| **Intel Inspector** | 跨平台 | 内存和线程错误 | 中等 | 中等 |
| **Visual Studio诊断** | Windows | 内存使用、泄漏 | 低 | 简单 |

#### 7.3.2 AddressSanitizer使用示例

```cpp
// 编译命令：g++ -fsanitize=address -g -o test test.cpp

#include <iostream>
#include <vector>

class AddressSanitizer演示 {
public:
    void 缓冲区溢出检测() {
        std::cout << "=== AddressSanitizer 缓冲区溢出检测 ===" << std::endl;

        int* 数组 = new int[10];

        // 正常访问
        for (int i = 0; i < 10; ++i) {
            数组[i] = i;
        }

        // ❌ 越界访问 - AddressSanitizer会检测到
        // 数组[10] = 999;  // 取消注释会触发错误

        delete[] 数组;
        std::cout << "缓冲区溢出检测完成" << std::endl;
    }

    void use_after_free检测() {
        std::cout << "\n=== AddressSanitizer Use-After-Free检测 ===" << std::endl;

        int* ptr = new int(42);
        std::cout << "分配的值：" << *ptr << std::endl;

        delete ptr;

        // ❌ 使用已释放的内存 - AddressSanitizer会检测到
        // std::cout << "释放后的值：" << *ptr << std::endl;  // 取消注释会触发错误

        std::cout << "Use-After-Free检测完成" << std::endl;
    }

    void 内存泄漏检测() {
        std::cout << "\n=== AddressSanitizer 内存泄漏检测 ===" << std::endl;

        // ❌ 故意泄漏内存
        int* 泄漏内存 = new int[1000];
        *泄漏内存 = 42;

        // 忘记 delete[] 泄漏内存;

        std::cout << "内存泄漏检测完成（程序结束时会报告泄漏）" << std::endl;
    }

    void 栈溢出检测() {
        std::cout << "\n=== AddressSanitizer 栈溢出检测 ===" << std::endl;

        char 栈数组[10];

        // 正常使用
        for (int i = 0; i < 10; ++i) {
            栈数组[i] = 'A' + i;
        }

        // ❌ 栈溢出 - AddressSanitizer会检测到
        // 栈数组[10] = 'Z';  // 取消注释会触发错误

        std::cout << "栈溢出检测完成" << std::endl;
    }
};

void AddressSanitizer使用演示() {
    AddressSanitizer演示 演示;

    演示.缓冲区溢出检测();
    演示.use_after_free检测();
    演示.内存泄漏检测();
    演示.栈溢出检测();
}
```

### 7.4 性能分析与优化

#### 7.4.1 内存性能分析器

```cpp
#include <chrono>
#include <unordered_map>
#include <algorithm>

class 内存性能分析器 {
private:
    struct 性能统计 {
        size_t 调用次数 = 0;
        size_t 总分配大小 = 0;
        std::chrono::nanoseconds 总耗时{0};
        std::chrono::nanoseconds 最大耗时{0};
        std::chrono::nanoseconds 最小耗时{std::chrono::nanoseconds::max()};

        void 更新统计(size_t 大小, std::chrono::nanoseconds 耗时) {
            调用次数++;
            总分配大小 += 大小;
            总耗时 += 耗时;
            最大耗时 = std::max(最大耗时, 耗时);
            最小耗时 = std::min(最小耗时, 耗时);
        }

        double 平均耗时() const {
            return 调用次数 > 0 ? 总耗时.count() / double(调用次数) : 0.0;
        }

        double 平均大小() const {
            return 调用次数 > 0 ? 总分配大小 / double(调用次数) : 0.0;
        }
    };

    std::unordered_map<std::string, 性能统计> 分配统计;
    std::unordered_map<std::string, 性能统计> 释放统计;
    std::mutex 统计锁;
    bool 启用分析;

public:
    static 内存性能分析器& 获取实例() {
        static 内存性能分析器 实例;
        return 实例;
    }

    void* 计时分配(size_t 大小, const std::string& 标签 = "默认") {
        auto 开始 = std::chrono::high_resolution_clock::now();
        void* ptr = std::malloc(大小);
        auto 结束 = std::chrono::high_resolution_clock::now();

        if (启用分析) {
            auto 耗时 = std::chrono::duration_cast<std::chrono::nanoseconds>(结束 - 开始);

            std::lock_guard<std::mutex> 锁(统计锁);
            分配统计[标签].更新统计(大小, 耗时);
        }

        return ptr;
    }

    void 计时释放(void* ptr, const std::string& 标签 = "默认") {
        if (!ptr) return;

        auto 开始 = std::chrono::high_resolution_clock::now();
        std::free(ptr);
        auto 结束 = std::chrono::high_resolution_clock::now();

        if (启用分析) {
            auto 耗时 = std::chrono::duration_cast<std::chrono::nanoseconds>(结束 - 开始);

            std::lock_guard<std::mutex> 锁(统计锁);
            释放统计[标签].更新统计(0, 耗时);
        }
    }

    void 打印性能报告() {
        std::lock_guard<std::mutex> 锁(统计锁);

        std::cout << "\n=== 内存性能分析报告 ===" << std::endl;

        std::cout << "\n分配性能统计：" << std::endl;
        std::cout << std::setw(15) << "标签"
                  << std::setw(10) << "次数"
                  << std::setw(15) << "总大小(B)"
                  << std::setw(15) << "平均大小(B)"
                  << std::setw(15) << "平均耗时(ns)"
                  << std::setw(15) << "最大耗时(ns)"
                  << std::setw(15) << "最小耗时(ns)" << std::endl;

        for (const auto& pair : 分配统计) {
            const auto& 统计 = pair.second;
            std::cout << std::setw(15) << pair.first
                      << std::setw(10) << 统计.调用次数
                      << std::setw(15) << 统计.总分配大小
                      << std::setw(15) << std::fixed << std::setprecision(2) << 统计.平均大小()
                      << std::setw(15) << std::fixed << std::setprecision(2) << 统计.平均耗时()
                      << std::setw(15) << 统计.最大耗时.count()
                      << std::setw(15) << 统计.最小耗时.count() << std::endl;
        }

        std::cout << "\n释放性能统计：" << std::endl;
        std::cout << std::setw(15) << "标签"
                  << std::setw(10) << "次数"
                  << std::setw(15) << "平均耗时(ns)"
                  << std::setw(15) << "最大耗时(ns)"
                  << std::setw(15) << "最小耗时(ns)" << std::endl;

        for (const auto& pair : 释放统计) {
            const auto& 统计 = pair.second;
            std::cout << std::setw(15) << pair.first
                      << std::setw(10) << 统计.调用次数
                      << std::setw(15) << std::fixed << std::setprecision(2) << 统计.平均耗时()
                      << std::setw(15) << 统计.最大耗时.count()
                      << std::setw(15) << 统计.最小耗时.count() << std::endl;
        }
    }

    void 重置统计() {
        std::lock_guard<std::mutex> 锁(统计锁);
        分配统计.clear();
        释放统计.clear();
    }

    void 启用() { 启用分析 = true; }
    void 禁用() { 启用分析 = false; }

private:
    内存性能分析器() : 启用分析(true) {}
};

void 性能分析演示() {
    std::cout << "=== 内存性能分析演示 ===" << std::endl;

    auto& 分析器 = 内存性能分析器::获取实例();
    分析器.启用();

    // 测试小内存分配
    std::vector<void*> 小内存块;
    for (int i = 0; i < 1000; ++i) {
        void* ptr = 分析器.计时分配(64, "小内存");
        小内存块.push_back(ptr);
    }

    // 测试大内存分配
    std::vector<void*> 大内存块;
    for (int i = 0; i < 100; ++i) {
        void* ptr = 分析器.计时分配(1024 * 1024, "大内存");
        大内存块.push_back(ptr);
    }

    // 测试中等内存分配
    std::vector<void*> 中等内存块;
    for (int i = 0; i < 500; ++i) {
        void* ptr = 分析器.计时分配(4096, "中等内存");
        中等内存块.push_back(ptr);
    }

    // 释放内存
    for (void* ptr : 小内存块) {
        分析器.计时释放(ptr, "小内存");
    }

    for (void* ptr : 大内存块) {
        分析器.计时释放(ptr, "大内存");
    }

    for (void* ptr : 中等内存块) {
        分析器.计时释放(ptr, "中等内存");
    }

    // 打印性能报告
    分析器.打印性能报告();
}
```

### 7.5 内存问题预防策略

#### 7.5.1 RAII和智能指针最佳实践

```cpp
class 内存安全最佳实践 {
public:
    // ✅ 使用RAII管理资源
    class 文件管理器 {
    private:
        FILE* 文件句柄;

    public:
        文件管理器(const char* 文件名, const char* 模式) {
            文件句柄 = fopen(文件名, 模式);
            if (!文件句柄) {
                throw std::runtime_error("无法打开文件");
            }
            std::cout << "文件已打开：" << 文件名 << std::endl;
        }

        ~文件管理器() {
            if (文件句柄) {
                fclose(文件句柄);
                std::cout << "文件已关闭" << std::endl;
            }
        }

        // 禁止拷贝
        文件管理器(const 文件管理器&) = delete;
        文件管理器& operator=(const 文件管理器&) = delete;

        FILE* 获取句柄() { return 文件句柄; }
    };

    // ✅ 使用智能指针管理动态内存
    void 智能指针最佳实践() {
        std::cout << "=== 智能指针最佳实践 ===" << std::endl;

        // 优先使用make_unique和make_shared
        auto 唯一指针 = std::make_unique<std::vector<int>>(1000, 42);
        auto 共享指针 = std::make_shared<std::string>("Hello, Smart Pointers!");

        // 使用weak_ptr避免循环引用
        std::weak_ptr<std::string> 弱引用 = 共享指针;

        // 检查weak_ptr是否有效
        if (auto 锁定指针 = 弱引用.lock()) {
            std::cout << "弱引用有效：" << *锁定指针 << std::endl;
        }

        // 自动清理，无需手动delete
    }

    // ✅ 异常安全的内存管理
    void 异常安全示例() {
        std::cout << "\n=== 异常安全示例 ===" << std::endl;

        try {
            // 使用智能指针确保异常安全
            auto 数据 = std::make_unique<int[]>(1000);

            // 可能抛出异常的操作
            可能失败的操作();

            // 即使异常发生，智能指针也会自动清理

        } catch (const std::exception& e) {
            std::cout << "捕获异常：" << e.what() << std::endl;
            // 内存已自动清理
        }
    }

    // ✅ 容器优于原始数组
    void 容器vs原始数组() {
        std::cout << "\n=== 容器 vs 原始数组 ===" << std::endl;

        // ❌ 原始数组（容易出错）
        // int* 原始数组 = new int[100];
        // 需要手动管理大小、边界检查、内存释放

        // ✅ 使用容器（安全、方便）
        std::vector<int> 安全数组(100);

        // 自动边界检查
        try {
            安全数组.at(150) = 42;  // 会抛出异常
        } catch (const std::out_of_range& e) {
            std::cout << "边界检查捕获错误：" << e.what() << std::endl;
        }

        // 自动内存管理，无需手动释放
    }

    // ✅ 使用标准算法避免手动循环
    void 标准算法最佳实践() {
        std::cout << "\n=== 标准算法最佳实践 ===" << std::endl;

        std::vector<int> 数据 = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};

        // ✅ 使用标准算法，减少出错机会
        auto 结果 = std::find_if(数据.begin(), 数据.end(),
            [](int x) { return x > 5; });

        if (结果 != 数据.end()) {
            std::cout << "找到第一个大于5的数：" << *结果 << std::endl;
        }

        // ✅ 使用范围for循环
        std::cout << "所有数据：";
        for (const auto& 值 : 数据) {
            std::cout << 值 << " ";
        }
        std::cout << std::endl;
    }

private:
    void 可能失败的操作() {
        // 模拟可能失败的操作
        if (rand() % 3 == 0) {
            throw std::runtime_error("模拟操作失败");
        }
    }
};

void 内存安全演示() {
    内存安全最佳实践 实践;

    实践.智能指针最佳实践();
    实践.异常安全示例();
    实践.容器vs原始数组();
    实践.标准算法最佳实践();
}
```

### 7.6 Part 7 总结

> **🎯 Part 7 核心要点**：

#### 内存调试工具选择指南

| 问题类型 | 推荐工具 | 检测能力 | 使用场景 |
|----------|----------|----------|----------|
| **内存泄漏** | Valgrind, AddressSanitizer | 高 | 开发、测试阶段 |
| **缓冲区溢出** | AddressSanitizer, Dr. Memory | 高 | 所有阶段 |
| **Use-After-Free** | AddressSanitizer | 高 | 开发、测试阶段 |
| **性能分析** | 自定义分析器, Profiler | 中 | 优化阶段 |
| **线程安全** | ThreadSanitizer | 高 | 多线程开发 |

#### 预防策略

1. **RAII原则**：资源获取即初始化，自动管理生命周期
2. **智能指针优先**：使用unique_ptr、shared_ptr替代原始指针
3. **容器优于数组**：使用vector、array替代原始数组
4. **异常安全**：确保异常情况下资源正确释放
5. **工具辅助**：集成内存检测工具到开发流程

#### 调试流程

1. **编译时检查**：启用编译器警告和静态分析
2. **运行时检测**：使用AddressSanitizer等工具
3. **性能分析**：识别内存热点和瓶颈
4. **代码审查**：人工检查内存管理逻辑
5. **自动化测试**：集成内存检测到CI/CD

#### 最佳实践总结

- ✅ 使用现代C++特性（智能指针、容器、RAII）
- ✅ 集成内存检测工具到开发流程
- ✅ 编写异常安全的代码
- ✅ 定期进行内存性能分析
- ✅ 建立内存问题预防机制
- ❌ 避免手动内存管理
- ❌ 不要忽视编译器警告

**接下来**：附录将提供实战指导和面试问题解答。

---

## 附录：最佳实践与面试指南

### A.1 内存管理最佳实践清单

#### A.1.1 日常开发检查清单

```markdown
## 🔍 内存管理检查清单

### 设计阶段
- [ ] 明确对象所有权和生命周期
- [ ] 选择合适的智能指针类型
- [ ] 考虑是否需要自定义内存管理
- [ ] 评估内存使用模式和性能需求

### 编码阶段
- [ ] 优先使用栈内存而非堆内存
- [ ] 使用智能指针管理动态内存
- [ ] 避免原始指针的所有权语义
- [ ] 使用容器替代原始数组
- [ ] 实现异常安全的资源管理

### 测试阶段
- [ ] 启用AddressSanitizer编译
- [ ] 运行内存泄漏检测工具
- [ ] 测试异常情况下的内存管理
- [ ] 验证多线程环境下的内存安全
- [ ] 进行压力测试和长时间运行测试

### 优化阶段
- [ ] 分析内存使用热点
- [ ] 考虑内存池优化
- [ ] 评估缓存友好性
- [ ] 优化内存对齐
- [ ] 减少内存碎片
```

#### A.1.2 代码审查要点

```cpp
class 代码审查指南 {
public:
    // ✅ 好的实践示例
    void 良好的内存管理() {
        // 1. 使用智能指针
        auto 资源 = std::make_unique<ExpensiveResource>();

        // 2. 使用容器管理集合
        std::vector<int> 数据;
        数据.reserve(预期大小);  // 预分配避免重分配

        // 3. RAII管理资源
        {
            std::lock_guard<std::mutex> 锁(互斥量);
            // 自动解锁
        }

        // 4. 异常安全
        try {
            auto 临时资源 = std::make_unique<Resource>();
            危险操作();
            // 即使异常也会自动清理
        } catch (...) {
            // 处理异常
        }
    }

    // ❌ 需要改进的代码模式
    void 需要改进的模式() {
        // 问题1：原始指针管理
        Resource* ptr = new Resource();  // 危险！
        // ... 使用ptr
        delete ptr;  // 容易忘记或异常时跳过

        // 问题2：数组管理
        int* arr = new int[size];  // 危险！
        // ... 使用arr
        delete[] arr;  // 容易用错delete

        // 问题3：异常不安全
        Resource* res = new Resource();
        危险操作();  // 如果抛异常，res泄漏
        delete res;
    }

    // 审查要点
    void 审查检查点() {
        /*
        代码审查时重点检查：

        1. 内存分配与释放配对
           - 每个new都有对应的delete
           - 每个new[]都有对应的delete[]
           - malloc/free配对使用

        2. 智能指针使用
           - 优先使用make_unique/make_shared
           - 正确选择unique_ptr vs shared_ptr
           - 避免循环引用，使用weak_ptr

        3. 异常安全
           - 资源管理使用RAII
           - 构造函数异常安全
           - 强异常安全保证

        4. 容器使用
           - 预分配容器容量
           - 使用emplace而非push+临时对象
           - 正确的迭代器失效处理

        5. 性能考虑
           - 避免不必要的拷贝
           - 使用移动语义
           - 考虑内存对齐
        */
    }

private:
    void 危险操作() {
        // 模拟可能抛异常的操作
    }

    size_t 预期大小 = 1000;
    std::mutex 互斥量;
};
```

### A.2 常见面试问题与解答

#### A.2.1 基础概念问题

```cpp
class 面试问题解答 {
public:
    /*
    Q1: 解释栈内存和堆内存的区别
    A1:
    - 栈内存：自动管理，速度快，大小有限，局部变量存储
    - 堆内存：手动管理，速度较慢，大小灵活，动态分配
    */
    void 栈vs堆演示() {
        // 栈内存
        int 栈变量 = 42;  // 自动管理，函数结束时自动销毁

        // 堆内存
        int* 堆变量 = new int(42);  // 需要手动管理
        delete 堆变量;
    }

    /*
    Q2: 什么是内存泄漏？如何避免？
    A2:
    - 内存泄漏：分配的内存没有被正确释放
    - 避免方法：使用智能指针、RAII、容器
    */
    void 内存泄漏避免() {
        // ❌ 容易泄漏
        // int* ptr = new int(42);
        // 忘记delete ptr;

        // ✅ 自动管理
        auto ptr = std::make_unique<int>(42);
        // 自动释放
    }

    /*
    Q3: 智能指针有哪些类型？各自的用途？
    A3:
    - unique_ptr: 独占所有权，不可拷贝，可移动
    - shared_ptr: 共享所有权，引用计数，可拷贝
    - weak_ptr: 观察者，不影响生命周期，避免循环引用
    */
    void 智能指针类型演示() {
        // unique_ptr - 独占所有权
        auto unique = std::make_unique<int>(42);
        // auto copy = unique;  // 编译错误，不可拷贝
        auto moved = std::move(unique);  // 可以移动

        // shared_ptr - 共享所有权
        auto shared1 = std::make_shared<int>(42);
        auto shared2 = shared1;  // 可以拷贝，引用计数+1

        // weak_ptr - 观察者
        std::weak_ptr<int> weak = shared1;
        if (auto locked = weak.lock()) {
            // 安全访问
        }
    }

    /*
    Q4: 什么是移动语义？有什么优势？
    A4:
    - 移动语义：转移资源所有权而非拷贝
    - 优势：避免深拷贝，提高性能，支持只移动类型
    */
    void 移动语义演示() {
        std::vector<int> source(1000000, 42);

        // 拷贝语义 - 昂贵
        std::vector<int> copy = source;

        // 移动语义 - 高效
        std::vector<int> moved = std::move(source);
        // source现在为空，moved拥有原数据
    }

    /*
    Q5: 如何实现自定义的内存管理？
    A5: 可以通过重载operator new/delete或自定义分配器
    */
    class 自定义内存管理 {
    public:
        // 重载operator new
        void* operator new(size_t size) {
            std::cout << "自定义分配：" << size << " 字节" << std::endl;
            return std::malloc(size);
        }

        void operator delete(void* ptr) noexcept {
            std::cout << "自定义释放" << std::endl;
            std::free(ptr);
        }
    };
};
```

#### A.2.2 高级问题解答

```cpp
class 高级面试问题 {
public:
    /*
    Q6: 解释RAII原则及其重要性
    A6: Resource Acquisition Is Initialization
    - 资源获取即初始化
    - 利用对象生命周期管理资源
    - 异常安全，自动清理
    */
    class RAII示例 {
        FILE* file_;
    public:
        RAII示例(const char* filename) {
            file_ = fopen(filename, "r");
            if (!file_) throw std::runtime_error("打开文件失败");
        }

        ~RAII示例() {
            if (file_) fclose(file_);  // 自动清理
        }

        // 禁止拷贝，允许移动
        RAII示例(const RAII示例&) = delete;
        RAII示例& operator=(const RAII示例&) = delete;
        RAII示例(RAII示例&&) = default;
        RAII示例& operator=(RAII示例&&) = default;
    };

    /*
    Q7: 什么是内存对齐？为什么重要？
    A7:
    - 内存对齐：数据按特定边界存储
    - 重要性：提高访问效率，满足硬件要求
    */
    void 内存对齐演示() {
        struct 未对齐 {
            char a;     // 1字节
            int b;      // 4字节
            char c;     // 1字节
        };  // 实际大小可能是12字节（因为对齐）

        struct 手动对齐 {
            char a;
            char padding[3];  // 手动填充
            int b;
            char c;
            char padding2[3]; // 手动填充
        };

        std::cout << "未对齐结构大小：" << sizeof(未对齐) << std::endl;
        std::cout << "手动对齐结构大小：" << sizeof(手动对齐) << std::endl;
    }

    /*
    Q8: 如何检测和调试内存问题？
    A8: 使用工具和技术
    */
    void 内存调试方法() {
        /*
        工具选择：
        1. AddressSanitizer - 编译时启用
        2. Valgrind - Linux下的内存检测
        3. Dr. Memory - Windows下的内存检测
        4. 静态分析工具 - 编译时检查

        技术方法：
        1. 自定义内存跟踪器
        2. 智能指针调试包装
        3. 单元测试覆盖内存管理
        4. 代码审查关注内存安全
        */
    }

    /*
    Q9: 在多线程环境下如何安全管理内存？
    A9: 线程安全的内存管理策略
    */
    void 多线程内存安全() {
        // 1. 使用线程安全的智能指针
        std::shared_ptr<int> 线程安全指针 = std::make_shared<int>(42);

        // 2. 避免共享可变状态
        thread_local int 线程局部变量 = 0;

        // 3. 使用原子操作
        std::atomic<int*> 原子指针{nullptr};

        // 4. 正确的同步机制
        std::mutex 内存锁;
        std::lock_guard<std::mutex> 锁(内存锁);

        /*
        注意事项：
        - shared_ptr的引用计数是线程安全的
        - 但指向的对象访问需要额外同步
        - 避免数据竞争和内存序问题
        */
    }

    /*
    Q10: 如何优化内存使用性能？
    A10: 多层次优化策略
    */
    void 内存性能优化() {
        /*
        优化策略：

        1. 减少分配次数
           - 使用内存池
           - 预分配容器容量
           - 批量分配

        2. 提高缓存效率
           - 数据局部性
           - 内存对齐
           - 减少内存碎片

        3. 使用移动语义
           - 避免不必要拷贝
           - 实现移动构造/赋值
           - 使用完美转发

        4. 选择合适的数据结构
           - vector vs list vs deque
           - 根据访问模式选择
           - 考虑内存开销
        */

        // 示例：容器预分配
        std::vector<int> 优化容器;
        优化容器.reserve(1000);  // 避免重分配

        // 示例：使用emplace
        std::vector<std::string> 字符串容器;
        字符串容器.emplace_back("直接构造");  // 避免临时对象
    }
};
```

### A.3 实战项目指导

#### A.3.1 内存管理框架设计

```cpp
// 一个完整的内存管理框架示例
namespace MemoryFramework {

// 内存统计信息
struct MemoryStats {
    size_t total_allocated = 0;
    size_t total_freed = 0;
    size_t current_usage = 0;
    size_t peak_usage = 0;
    size_t allocation_count = 0;
    size_t deallocation_count = 0;
};

// 内存管理器接口
class IMemoryManager {
public:
    virtual ~IMemoryManager() = default;
    virtual void* allocate(size_t size, size_t alignment = alignof(std::max_align_t)) = 0;
    virtual void deallocate(void* ptr) = 0;
    virtual MemoryStats get_stats() const = 0;
    virtual void reset() = 0;
};

// 默认内存管理器
class DefaultMemoryManager : public IMemoryManager {
private:
    mutable std::mutex stats_mutex_;
    MemoryStats stats_;
    std::unordered_map<void*, size_t> allocations_;

public:
    void* allocate(size_t size, size_t alignment) override {
        void* ptr = aligned_alloc(alignment, size);
        if (!ptr) {
            throw std::bad_alloc();
        }

        std::lock_guard<std::mutex> lock(stats_mutex_);
        allocations_[ptr] = size;
        stats_.total_allocated += size;
        stats_.current_usage += size;
        stats_.peak_usage = std::max(stats_.peak_usage, stats_.current_usage);
        stats_.allocation_count++;

        return ptr;
    }

    void deallocate(void* ptr) override {
        if (!ptr) return;

        std::lock_guard<std::mutex> lock(stats_mutex_);
        auto it = allocations_.find(ptr);
        if (it != allocations_.end()) {
            size_t size = it->second;
            stats_.total_freed += size;
            stats_.current_usage -= size;
            stats_.deallocation_count++;
            allocations_.erase(it);
        }

        free(ptr);
    }

    MemoryStats get_stats() const override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return stats_;
    }

    void reset() override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_ = MemoryStats{};
        allocations_.clear();
    }
};

// 全局内存管理器
class GlobalMemoryManager {
private:
    static std::unique_ptr<IMemoryManager> instance_;
    static std::mutex instance_mutex_;

public:
    static void set_manager(std::unique_ptr<IMemoryManager> manager) {
        std::lock_guard<std::mutex> lock(instance_mutex_);
        instance_ = std::move(manager);
    }

    static IMemoryManager& get_manager() {
        std::lock_guard<std::mutex> lock(instance_mutex_);
        if (!instance_) {
            instance_ = std::make_unique<DefaultMemoryManager>();
        }
        return *instance_;
    }
};

// 静态成员定义
std::unique_ptr<IMemoryManager> GlobalMemoryManager::instance_;
std::mutex GlobalMemoryManager::instance_mutex_;

// 智能指针包装器
template<typename T>
class managed_ptr {
private:
    T* ptr_;

public:
    explicit managed_ptr(T* ptr = nullptr) : ptr_(ptr) {}

    ~managed_ptr() {
        if (ptr_) {
            ptr_->~T();
            GlobalMemoryManager::get_manager().deallocate(ptr_);
        }
    }

    // 移动语义
    managed_ptr(managed_ptr&& other) noexcept : ptr_(other.ptr_) {
        other.ptr_ = nullptr;
    }

    managed_ptr& operator=(managed_ptr&& other) noexcept {
        if (this != &other) {
            if (ptr_) {
                ptr_->~T();
                GlobalMemoryManager::get_manager().deallocate(ptr_);
            }
            ptr_ = other.ptr_;
            other.ptr_ = nullptr;
        }
        return *this;
    }

    // 禁止拷贝
    managed_ptr(const managed_ptr&) = delete;
    managed_ptr& operator=(const managed_ptr&) = delete;

    T* get() const { return ptr_; }
    T& operator*() const { return *ptr_; }
    T* operator->() const { return ptr_; }
    explicit operator bool() const { return ptr_ != nullptr; }
};

// 便利函数
template<typename T, typename... Args>
managed_ptr<T> make_managed(Args&&... args) {
    auto& manager = GlobalMemoryManager::get_manager();
    void* memory = manager.allocate(sizeof(T), alignof(T));
    T* ptr = new(memory) T(std::forward<Args>(args)...);
    return managed_ptr<T>(ptr);
}

} // namespace MemoryFramework

// 使用示例
void 内存框架使用示例() {
    using namespace MemoryFramework;

    std::cout << "=== 内存管理框架使用示例 ===" << std::endl;

    // 创建管理对象
    auto obj1 = make_managed<std::string>("Hello, Framework!");
    auto obj2 = make_managed<std::vector<int>>(1000, 42);

    // 使用对象
    std::cout << "字符串：" << *obj1 << std::endl;
    std::cout << "向量大小：" << obj2->size() << std::endl;

    // 查看内存统计
    auto stats = GlobalMemoryManager::get_manager().get_stats();
    std::cout << "当前内存使用：" << stats.current_usage << " 字节" << std::endl;
    std::cout << "分配次数：" << stats.allocation_count << std::endl;

    // 对象自动清理
}
```

### A.4 总结与展望

#### A.4.1 学习路径建议

```markdown
## 🎯 C++内存管理学习路径

### 初级阶段（1-2个月）
1. **基础概念**
   - 理解栈内存vs堆内存
   - 掌握new/delete基本用法
   - 学习指针和引用

2. **智能指针入门**
   - unique_ptr基本使用
   - shared_ptr基本使用
   - RAII原则理解

3. **容器使用**
   - vector、string等基本容器
   - 避免原始数组

### 中级阶段（2-3个月）
1. **深入智能指针**
   - weak_ptr解决循环引用
   - 自定义删除器
   - 智能指针性能考虑

2. **移动语义**
   - 右值引用理解
   - 移动构造/赋值
   - 完美转发基础

3. **异常安全**
   - RAII深入应用
   - 异常安全保证级别
   - 资源管理最佳实践

### 高级阶段（3-6个月）
1. **高级内存技术**
   - 内存池设计
   - 自定义分配器
   - 内存对齐优化

2. **性能优化**
   - 缓存友好编程
   - 内存访问模式优化
   - SIMD内存操作

3. **调试与工具**
   - 内存调试工具使用
   - 自定义内存跟踪
   - 性能分析技术

### 专家阶段（持续学习）
1. **框架设计**
   - 内存管理框架
   - 跨平台内存管理
   - 大规模系统内存优化

2. **前沿技术**
   - 现代C++新特性
   - 并发内存管理
   - 特定领域优化
```

#### A.4.2 持续改进建议

```cpp
class 持续改进指南 {
public:
    void 代码质量提升() {
        /*
        持续改进建议：

        1. 建立编码规范
           - 统一的内存管理风格
           - 代码审查检查清单
           - 自动化检查工具

        2. 工具集成
           - CI/CD集成内存检测
           - 静态分析工具
           - 性能基准测试

        3. 团队培训
           - 定期技术分享
           - 最佳实践更新
           - 问题案例分析

        4. 文档维护
           - 内存管理指南
           - 常见问题解答
           - 设计决策记录
        */
    }

    void 技术跟踪() {
        /*
        技术发展跟踪：

        1. C++标准更新
           - 新的智能指针特性
           - 内存管理改进
           - 性能优化技术

        2. 工具发展
           - 新的调试工具
           - 性能分析工具
           - 静态分析改进

        3. 最佳实践演进
           - 社区经验分享
           - 开源项目学习
           - 学术研究成果
        */
    }
};
```

---

## 🎉 结语

恭喜您完成了这份详尽的C++内存管理学习指南！

通过本文档的学习，您已经掌握了：

- **基础理论**：内存模型、分配机制、生命周期管理
- **实用技术**：智能指针、移动语义、容器优化
- **高级技巧**：内存池、自定义分配器、性能调优
- **调试技能**：问题诊断、工具使用、预防策略

### 📚 推荐进一步学习资源

1. **经典书籍**
   - 《Effective C++》- Scott Meyers
   - 《More Effective C++》- Scott Meyers
   - 《Effective Modern C++》- Scott Meyers
   - 《C++ Primer》- Stanley Lippman

2. **在线资源**
   - cppreference.com - C++标准库参考
   - CppCon视频 - 年度C++大会演讲
   - Compiler Explorer - 在线编译器和汇编查看

3. **实践项目**
   - 实现自己的智能指针
   - 设计内存池系统
   - 优化现有项目的内存使用

### 🚀 下一步行动

1. **实践应用**：在实际项目中应用所学知识
2. **深入专研**：选择感兴趣的领域深入学习
3. **分享交流**：与同事分享经验，参与技术讨论
4. **持续更新**：跟踪C++标准和工具的最新发展

记住，内存管理是一个需要持续实践和改进的技能。保持学习的热情，在实践中不断完善您的技术水平！

**愿您在C++内存管理的道路上越走越远，写出更安全、更高效的代码！** 🎯