# C++ 异常处理与错误管理：从入门到专业实践的权威指南

本指南旨在为您构建一个关于 C++ 异常处理与错误管理的坚实、专业且现代的知识体系。我们将遵循一条从基础概念到高级实践的清晰路径，确保每一个知识点都得到深入的探讨，并融入权威书籍的核心思想与业界的最佳实践。

---

## Part 0: 快速入门——30分钟掌握异常处理核心

> **写给初学者**：本章将通过生动的实例，带您无痛入门C++异常处理的核心概念。

### 0.1 异常处理的本质：优雅的错误传播

**核心概念**：
异常处理是一种错误处理机制，允许程序在遇到错误时"跳出"正常的执行流程，将错误信息传播到能够处理它的地方。

```cpp
#include <iostream>
#include <stdexcept>
#include <string>

// 传统错误处理方式（不推荐）
int divide_old_style(int a, int b, bool& success) {
    if (b == 0) {
        success = false;
        return 0;  // 错误码，但0也可能是有效结果
    }
    success = true;
    return a / b;
}

// 现代异常处理方式（推荐）
int divide_modern(int a, int b) {
    if (b == 0) {
        throw std::invalid_argument("Division by zero is not allowed");
    }
    return a / b;
}

void demonstrate_basic_exception_handling() {
    std::cout << "=== Basic Exception Handling ===" << std::endl;
    
    // 传统方式的问题
    bool success;
    int result1 = divide_old_style(10, 0, success);
    if (!success) {
        std::cout << "Old style: Division failed" << std::endl;
    }
    
    // 现代异常处理
    try {
        int result2 = divide_modern(10, 2);
        std::cout << "10 / 2 = " << result2 << std::endl;
        
        int result3 = divide_modern(10, 0);  // 这里会抛出异常
        std::cout << "This line won't be executed" << std::endl;
    } catch (const std::invalid_argument& e) {
        std::cout << "Caught exception: " << e.what() << std::endl;
    }
    
    std::cout << "Program continues normally" << std::endl;
}
```

### 0.2 异常的三个关键字：try、throw、catch

```cpp
#include <iostream>
#include <stdexcept>
#include <vector>

class BankAccount {
private:
    double balance;
    std::string account_number;
    
public:
    BankAccount(const std::string& number, double initial_balance) 
        : account_number(number), balance(initial_balance) {
        if (initial_balance < 0) {
            throw std::invalid_argument("Initial balance cannot be negative");
        }
    }
    
    void withdraw(double amount) {
        if (amount < 0) {
            throw std::invalid_argument("Withdrawal amount cannot be negative");
        }
        if (amount > balance) {
            throw std::runtime_error("Insufficient funds");
        }
        balance -= amount;
    }
    
    void deposit(double amount) {
        if (amount < 0) {
            throw std::invalid_argument("Deposit amount cannot be negative");
        }
        balance += amount;
    }
    
    double get_balance() const { return balance; }
    const std::string& get_account_number() const { return account_number; }
};

void demonstrate_exception_keywords() {
    std::cout << "=== Exception Keywords Demo ===" << std::endl;
    
    try {
        // 1. 正常操作
        BankAccount account("12345", 1000.0);
        std::cout << "Account created with balance: $" << account.get_balance() << std::endl;
        
        // 2. 成功的操作
        account.deposit(500.0);
        std::cout << "After deposit: $" << account.get_balance() << std::endl;
        
        account.withdraw(200.0);
        std::cout << "After withdrawal: $" << account.get_balance() << std::endl;
        
        // 3. 这里会抛出异常
        account.withdraw(2000.0);  // 余额不足
        
    } catch (const std::invalid_argument& e) {
        std::cout << "Invalid argument: " << e.what() << std::endl;
    } catch (const std::runtime_error& e) {
        std::cout << "Runtime error: " << e.what() << std::endl;
    } catch (const std::exception& e) {
        std::cout << "General exception: " << e.what() << std::endl;
    } catch (...) {
        std::cout << "Unknown exception caught" << std::endl;
    }
    
    std::cout << "Banking operations completed" << std::endl;
}
```

### 0.3 异常安全：RAII的威力

```cpp
#include <iostream>
#include <memory>
#include <fstream>
#include <stdexcept>

// 不安全的资源管理
void unsafe_resource_management() {
    int* ptr = new int(42);
    std::ofstream file("temp.txt");
    
    // 如果这里抛出异常，内存泄漏！
    if (some_condition()) {
        throw std::runtime_error("Something went wrong");
    }
    
    delete ptr;  // 可能永远不会执行
    file.close(); // 可能永远不会执行
}

// 安全的资源管理（RAII）
void safe_resource_management() {
    std::unique_ptr<int> ptr = std::make_unique<int>(42);
    std::ofstream file("temp.txt");
    
    // 即使这里抛出异常，资源也会被自动清理
    if (some_condition()) {
        throw std::runtime_error("Something went wrong");
    }
    
    // unique_ptr和ofstream的析构函数会自动清理资源
}

// RAII资源管理器示例
class FileManager {
private:
    std::ofstream file;
    std::string filename;
    
public:
    explicit FileManager(const std::string& name) : filename(name) {
        file.open(filename);
        if (!file.is_open()) {
            throw std::runtime_error("Failed to open file: " + filename);
        }
        std::cout << "File opened: " << filename << std::endl;
    }
    
    ~FileManager() {
        if (file.is_open()) {
            file.close();
            std::cout << "File closed: " << filename << std::endl;
        }
    }
    
    void write(const std::string& data) {
        if (!file.is_open()) {
            throw std::runtime_error("File is not open");
        }
        file << data << std::endl;
    }
    
    // 禁止拷贝，允许移动
    FileManager(const FileManager&) = delete;
    FileManager& operator=(const FileManager&) = delete;
    FileManager(FileManager&&) = default;
    FileManager& operator=(FileManager&&) = default;
};

void demonstrate_exception_safety() {
    std::cout << "=== Exception Safety Demo ===" << std::endl;
    
    try {
        FileManager fm("test.txt");
        fm.write("Hello, World!");
        fm.write("This is a test.");
        
        // 即使这里抛出异常，FileManager的析构函数也会关闭文件
        throw std::runtime_error("Simulated error");
        
    } catch (const std::exception& e) {
        std::cout << "Caught exception: " << e.what() << std::endl;
    }
    
    std::cout << "Resources cleaned up automatically" << std::endl;
}

bool some_condition() {
    return true;  // 模拟条件
}
```

### 0.4 异常规范：noexcept的重要性

```cpp
#include <iostream>
#include <vector>
#include <type_traits>

class SafeClass {
public:
    // 不会抛出异常的操作
    int get_value() const noexcept { return value; }
    void set_value(int v) noexcept { value = v; }
    
    // 可能抛出异常的操作
    void risky_operation() {
        if (value < 0) {
            throw std::invalid_argument("Value cannot be negative");
        }
    }
    
    // 移动构造函数应该是noexcept的
    SafeClass(SafeClass&& other) noexcept : value(other.value) {
        other.value = 0;
    }
    
    // 移动赋值运算符也应该是noexcept的
    SafeClass& operator=(SafeClass&& other) noexcept {
        if (this != &other) {
            value = other.value;
            other.value = 0;
        }
        return *this;
    }
    
private:
    int value = 0;
};

void demonstrate_noexcept() {
    std::cout << "=== noexcept Demo ===" << std::endl;
    
    // 检查函数是否为noexcept
    SafeClass obj;
    
    std::cout << "get_value() is noexcept: " 
              << std::boolalpha << noexcept(obj.get_value()) << std::endl;
    std::cout << "risky_operation() is noexcept: " 
              << std::boolalpha << noexcept(obj.risky_operation()) << std::endl;
    
    // noexcept对STL容器优化的影响
    std::cout << "Move constructor is noexcept: " 
              << std::boolalpha << std::is_nothrow_move_constructible_v<SafeClass> << std::endl;
    
    // 这影响了vector等容器的性能优化
    std::vector<SafeClass> vec;
    vec.reserve(10);
    
    for (int i = 0; i < 5; ++i) {
        vec.emplace_back();
    }
    
    std::cout << "Vector operations completed efficiently" << std::endl;
}
```

> **快速入门总结**：异常处理提供了一种优雅的错误传播机制，通过try-catch-throw三个关键字实现。RAII确保了异常安全，noexcept规范提高了性能。掌握这些基础概念是编写健壮C++程序的关键。

> ---
> ⚠️ **【给初学者的黄金法则】**
> 1. **异常用于异常情况**：不要用异常处理正常的控制流程
> 2. **RAII确保安全**：使用智能指针和RAII类管理资源
> 3. **按引用捕获**：`catch(const std::exception& e)`避免对象切片
> 4. **noexcept很重要**：移动操作应该标记为noexcept
> 5. **异常规范要明确**：文档化哪些函数可能抛出异常
> ---

---

## 第一部分：异常机制的深度解析 (Exception Mechanism Deep Dive)

### 1.1 异常的传播机制：栈展开的艺术

**概念讲解**：
当异常被抛出时，程序会进行栈展开(stack unwinding)，自动调用局部对象的析构函数，直到找到合适的异常处理器。

**【深度解析】栈展开过程**
```cpp
#include <iostream>
#include <stdexcept>

class TrackingObject {
private:
    std::string name;
    
public:
    explicit TrackingObject(const std::string& n) : name(n) {
        std::cout << "Constructing " << name << std::endl;
    }
    
    ~TrackingObject() {
        std::cout << "Destructing " << name << std::endl;
    }
    
    void do_something() {
        std::cout << name << " is doing something" << std::endl;
    }
};

void level3() {
    TrackingObject obj3("Level3Object");
    obj3.do_something();
    
    std::cout << "About to throw exception from level3" << std::endl;
    throw std::runtime_error("Error from level 3");
    
    std::cout << "This line will never execute" << std::endl;
}

void level2() {
    TrackingObject obj2("Level2Object");
    obj2.do_something();
    
    std::cout << "Calling level3 from level2" << std::endl;
    level3();  // 异常会从这里传播上来
    
    std::cout << "This line will never execute" << std::endl;
}

void level1() {
    TrackingObject obj1("Level1Object");
    obj1.do_something();
    
    std::cout << "Calling level2 from level1" << std::endl;
    level2();  // 异常会从这里传播上来
    
    std::cout << "This line will never execute" << std::endl;
}

void demonstrate_stack_unwinding() {
    std::cout << "=== Stack Unwinding Demo ===" << std::endl;
    
    try {
        TrackingObject main_obj("MainObject");
        main_obj.do_something();
        
        std::cout << "Calling level1 from main" << std::endl;
        level1();
        
        std::cout << "This line will never execute" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "Caught exception in main: " << e.what() << std::endl;
    }
    
    std::cout << "Program continues after exception handling" << std::endl;
}
```

### 1.2 异常类型层次：标准异常的设计

**概念讲解**：
C++标准库提供了一个完整的异常类型层次结构，所有标准异常都继承自std::exception。理解这个层次结构有助于设计合适的异常处理策略。

**【深度解析】标准异常层次结构**
```cpp
#include <iostream>
#include <stdexcept>
#include <typeinfo>
#include <memory>
#include <vector>

class ExceptionHierarchyDemo {
public:
    static void demonstrate_standard_exceptions() {
        std::cout << "=== Standard Exception Hierarchy ===" << std::endl;

        // 1. logic_error系列 - 程序逻辑错误
        try {
            throw std::invalid_argument("This is an invalid argument");
        } catch (const std::logic_error& e) {
            std::cout << "Caught logic_error: " << e.what() << std::endl;
            std::cout << "Exception type: " << typeid(e).name() << std::endl;
        }

        // 2. runtime_error系列 - 运行时错误
        try {
            throw std::runtime_error("This is a runtime error");
        } catch (const std::runtime_error& e) {
            std::cout << "Caught runtime_error: " << e.what() << std::endl;
            std::cout << "Exception type: " << typeid(e).name() << std::endl;
        }

        // 3. 系统相关异常
        try {
            std::vector<int> vec(1);
            vec.at(10);  // 抛出std::out_of_range
        } catch (const std::out_of_range& e) {
            std::cout << "Caught out_of_range: " << e.what() << std::endl;
        } catch (const std::logic_error& e) {
            std::cout << "This won't be reached due to catch order" << std::endl;
        }

        // 4. 内存相关异常
        try {
            // 模拟内存分配失败（实际很难触发）
            throw std::bad_alloc();
        } catch (const std::bad_alloc& e) {
            std::cout << "Caught bad_alloc: " << e.what() << std::endl;
        }
    }

    static void demonstrate_exception_polymorphism() {
        std::cout << "\n=== Exception Polymorphism ===" << std::endl;

        auto throw_different_exceptions = [](int type) {
            switch (type) {
                case 1: throw std::invalid_argument("Invalid argument");
                case 2: throw std::out_of_range("Out of range");
                case 3: throw std::runtime_error("Runtime error");
                case 4: throw std::bad_alloc();
                default: throw std::exception();
            }
        };

        for (int i = 1; i <= 5; ++i) {
            try {
                throw_different_exceptions(i);
            } catch (const std::logic_error& e) {
                std::cout << "Logic error " << i << ": " << e.what() << std::endl;
            } catch (const std::runtime_error& e) {
                std::cout << "Runtime error " << i << ": " << e.what() << std::endl;
            } catch (const std::bad_alloc& e) {
                std::cout << "Memory error " << i << ": " << e.what() << std::endl;
            } catch (const std::exception& e) {
                std::cout << "General exception " << i << ": " << e.what() << std::endl;
            }
        }
    }
};
```

### 1.3 自定义异常：设计专业的错误类型

**概念讲解**：
虽然标准异常类型覆盖了大多数情况，但在专业开发中，设计自定义异常类型可以提供更精确的错误信息和更好的错误处理策略。

**【代码演示】自定义异常的最佳实践**
```cpp
#include <iostream>
#include <stdexcept>
#include <string>
#include <sstream>

// 基础自定义异常类
class DatabaseException : public std::runtime_error {
private:
    int error_code_;
    std::string operation_;

public:
    DatabaseException(int code, const std::string& operation, const std::string& message)
        : std::runtime_error(format_message(code, operation, message))
        , error_code_(code)
        , operation_(operation) {}

    int error_code() const noexcept { return error_code_; }
    const std::string& operation() const noexcept { return operation_; }

private:
    static std::string format_message(int code, const std::string& operation, const std::string& message) {
        std::ostringstream oss;
        oss << "Database error [" << code << "] in operation '" << operation << "': " << message;
        return oss.str();
    }
};

// 具体的异常类型
class ConnectionException : public DatabaseException {
public:
    ConnectionException(const std::string& host, int port)
        : DatabaseException(1001, "connect", "Failed to connect to " + host + ":" + std::to_string(port)) {}
};

class QueryException : public DatabaseException {
public:
    QueryException(const std::string& query, const std::string& error)
        : DatabaseException(2001, "query", "Query failed: " + query + " - " + error) {}
};

class TransactionException : public DatabaseException {
public:
    TransactionException(const std::string& details)
        : DatabaseException(3001, "transaction", "Transaction failed: " + details) {}
};

// 模拟数据库操作类
class Database {
public:
    void connect(const std::string& host, int port) {
        if (host.empty()) {
            throw ConnectionException(host, port);
        }
        std::cout << "Connected to " << host << ":" << port << std::endl;
    }

    void execute_query(const std::string& query) {
        if (query.find("DROP") != std::string::npos) {
            throw QueryException(query, "DROP operations are not allowed");
        }
        std::cout << "Executed query: " << query << std::endl;
    }

    void begin_transaction() {
        if (transaction_active_) {
            throw TransactionException("Transaction already active");
        }
        transaction_active_ = true;
        std::cout << "Transaction started" << std::endl;
    }

    void commit_transaction() {
        if (!transaction_active_) {
            throw TransactionException("No active transaction to commit");
        }
        transaction_active_ = false;
        std::cout << "Transaction committed" << std::endl;
    }

private:
    bool transaction_active_ = false;
};

void demonstrate_custom_exceptions() {
    std::cout << "=== Custom Exceptions Demo ===" << std::endl;

    Database db;

    // 测试不同类型的异常
    std::vector<std::function<void()>> operations = {
        [&]() { db.connect("", 5432); },  // ConnectionException
        [&]() { db.execute_query("DROP TABLE users"); },  // QueryException
        [&]() { db.commit_transaction(); },  // TransactionException
        [&]() {
            db.connect("localhost", 5432);
            db.execute_query("SELECT * FROM users");
            db.begin_transaction();
            db.commit_transaction();
        }  // 正常操作
    };

    for (size_t i = 0; i < operations.size(); ++i) {
        try {
            std::cout << "\n--- Operation " << (i + 1) << " ---" << std::endl;
            operations[i]();
            std::cout << "Operation completed successfully" << std::endl;

        } catch (const ConnectionException& e) {
            std::cout << "Connection error: " << e.what() << std::endl;
            std::cout << "Error code: " << e.error_code() << std::endl;

        } catch (const QueryException& e) {
            std::cout << "Query error: " << e.what() << std::endl;
            std::cout << "Operation: " << e.operation() << std::endl;

        } catch (const TransactionException& e) {
            std::cout << "Transaction error: " << e.what() << std::endl;

        } catch (const DatabaseException& e) {
            std::cout << "Database error: " << e.what() << std::endl;

        } catch (const std::exception& e) {
            std::cout << "Unexpected error: " << e.what() << std::endl;
        }
    }
}
```

### 1.4 异常安全保证：三个级别的承诺

**概念讲解**：
异常安全是衡量代码质量的重要标准。C++定义了三个异常安全级别：基本保证、强保证和不抛出保证。

**【深度解析】异常安全级别的实现**
```cpp
#include <iostream>
#include <vector>
#include <memory>
#include <algorithm>

class ExceptionSafetyDemo {
private:
    std::vector<int> data_;
    std::string name_;

public:
    // 基本异常安全保证：异常发生时对象处于有效状态
    void basic_safety_append(int value) {
        // 可能抛出异常，但对象状态仍然有效
        data_.push_back(value);  // 如果失败，vector状态不变
        name_ += std::to_string(value);  // 如果失败，可能部分修改
    }

    // 强异常安全保证：异常发生时状态回滚到操作前
    void strong_safety_append(int value) {
        // 使用临时对象和swap技术
        std::vector<int> temp_data = data_;
        std::string temp_name = name_;

        try {
            temp_data.push_back(value);
            temp_name += std::to_string(value);

            // 只有在所有操作都成功后才修改实际状态
            data_.swap(temp_data);
            name_.swap(temp_name);

        } catch (...) {
            // 异常时，原始状态保持不变
            throw;
        }
    }

    // 不抛出异常保证：操作绝不抛出异常
    void nothrow_clear() noexcept {
        data_.clear();  // vector::clear() 是 noexcept 的
        name_.clear();  // string::clear() 是 noexcept 的
    }

    size_t size() const noexcept { return data_.size(); }
    const std::string& name() const noexcept { return name_; }

    void print() const {
        std::cout << "Name: " << name_ << ", Data: [";
        for (size_t i = 0; i < data_.size(); ++i) {
            std::cout << data_[i];
            if (i < data_.size() - 1) std::cout << ", ";
        }
        std::cout << "]" << std::endl;
    }
};

// RAII资源管理的异常安全示例
class ResourceManager {
private:
    std::unique_ptr<int[]> buffer_;
    size_t size_;

public:
    explicit ResourceManager(size_t size) : size_(size) {
        buffer_ = std::make_unique<int[]>(size);  // 可能抛出 bad_alloc

        // 初始化数据
        for (size_t i = 0; i < size_; ++i) {
            buffer_[i] = static_cast<int>(i);
        }
    }

    // 强异常安全的复制赋值
    ResourceManager& operator=(const ResourceManager& other) {
        if (this == &other) return *this;

        // 创建新资源（可能失败）
        auto new_buffer = std::make_unique<int[]>(other.size_);

        // 复制数据（可能失败）
        std::copy(other.buffer_.get(), other.buffer_.get() + other.size_, new_buffer.get());

        // 只有在所有操作成功后才修改状态
        buffer_ = std::move(new_buffer);
        size_ = other.size_;

        return *this;
    }

    // 移动操作应该是 noexcept 的
    ResourceManager(ResourceManager&& other) noexcept
        : buffer_(std::move(other.buffer_)), size_(other.size_) {
        other.size_ = 0;
    }

    ResourceManager& operator=(ResourceManager&& other) noexcept {
        if (this != &other) {
            buffer_ = std::move(other.buffer_);
            size_ = other.size_;
            other.size_ = 0;
        }
        return *this;
    }

    int& operator[](size_t index) {
        if (index >= size_) {
            throw std::out_of_range("Index out of range");
        }
        return buffer_[index];
    }

    size_t size() const noexcept { return size_; }
};

void demonstrate_exception_safety_levels() {
    std::cout << "=== Exception Safety Levels Demo ===" << std::endl;

    ExceptionSafetyDemo demo;

    // 测试强异常安全
    try {
        demo.strong_safety_append(1);
        demo.strong_safety_append(2);
        demo.print();

        // 模拟异常情况（实际中可能是内存不足等）
        // demo.strong_safety_append(very_large_number);

    } catch (const std::exception& e) {
        std::cout << "Exception caught, object state preserved" << std::endl;
        demo.print();
    }

    // 测试 noexcept 操作
    demo.nothrow_clear();
    std::cout << "After nothrow_clear: ";
    demo.print();

    // 测试RAII资源管理
    try {
        ResourceManager rm1(10);
        ResourceManager rm2(5);

        std::cout << "rm1 size: " << rm1.size() << std::endl;
        std::cout << "rm2 size: " << rm2.size() << std::endl;

        rm2 = rm1;  // 强异常安全的赋值
        std::cout << "After assignment, rm2 size: " << rm2.size() << std::endl;

    } catch (const std::exception& e) {
        std::cout << "Resource management exception: " << e.what() << std::endl;
    }
}
```

---

## 第二部分：现代错误处理策略 (Modern Error Handling Strategies)

### 2.1 错误码 vs 异常：选择合适的策略

**概念讲解**：
现代C++提供了多种错误处理方式，包括传统的错误码、异常、以及C++17引入的std::optional和std::expected。选择合适的策略对程序的性能和可维护性至关重要。

**【深度解析】不同错误处理策略的对比**
```cpp
#include <iostream>
#include <optional>
#include <variant>
#include <string>
#include <system_error>

// 1. 传统错误码方式
enum class FileError {
    Success = 0,
    FileNotFound,
    PermissionDenied,
    DiskFull,
    UnknownError
};

class FileOperations {
public:
    // 错误码方式
    static FileError read_file_errorcode(const std::string& filename, std::string& content) {
        if (filename.empty()) {
            return FileError::FileNotFound;
        }
        if (filename == "protected.txt") {
            return FileError::PermissionDenied;
        }
        if (filename == "large.txt") {
            return FileError::DiskFull;
        }

        content = "File content of " + filename;
        return FileError::Success;
    }

    // 异常方式
    static std::string read_file_exception(const std::string& filename) {
        if (filename.empty()) {
            throw std::invalid_argument("Filename cannot be empty");
        }
        if (filename == "protected.txt") {
            throw std::runtime_error("Permission denied");
        }
        if (filename == "large.txt") {
            throw std::runtime_error("Disk full");
        }

        return "File content of " + filename;
    }

    // std::optional方式（C++17）
    static std::optional<std::string> read_file_optional(const std::string& filename) {
        if (filename.empty() || filename == "protected.txt" || filename == "large.txt") {
            return std::nullopt;
        }

        return "File content of " + filename;
    }

    // std::variant作为错误处理（类似Rust的Result）
    using FileResult = std::variant<std::string, FileError>;

    static FileResult read_file_variant(const std::string& filename) {
        if (filename.empty()) {
            return FileError::FileNotFound;
        }
        if (filename == "protected.txt") {
            return FileError::PermissionDenied;
        }
        if (filename == "large.txt") {
            return FileError::DiskFull;
        }

        return std::string("File content of " + filename);
    }
};

void demonstrate_error_handling_strategies() {
    std::cout << "=== Error Handling Strategies Comparison ===" << std::endl;

    std::vector<std::string> test_files = {"normal.txt", "", "protected.txt", "large.txt"};

    for (const auto& filename : test_files) {
        std::cout << "\n--- Testing file: '" << filename << "' ---" << std::endl;

        // 1. 错误码方式
        std::string content;
        FileError error = FileOperations::read_file_errorcode(filename, content);
        if (error == FileError::Success) {
            std::cout << "ErrorCode: Success - " << content << std::endl;
        } else {
            std::cout << "ErrorCode: Failed with code " << static_cast<int>(error) << std::endl;
        }

        // 2. 异常方式
        try {
            std::string result = FileOperations::read_file_exception(filename);
            std::cout << "Exception: Success - " << result << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Exception: Failed - " << e.what() << std::endl;
        }

        // 3. std::optional方式
        auto optional_result = FileOperations::read_file_optional(filename);
        if (optional_result) {
            std::cout << "Optional: Success - " << *optional_result << std::endl;
        } else {
            std::cout << "Optional: Failed - no value" << std::endl;
        }

        // 4. std::variant方式
        auto variant_result = FileOperations::read_file_variant(filename);
        if (std::holds_alternative<std::string>(variant_result)) {
            std::cout << "Variant: Success - " << std::get<std::string>(variant_result) << std::endl;
        } else {
            std::cout << "Variant: Failed with code "
                      << static_cast<int>(std::get<FileError>(variant_result)) << std::endl;
        }
    }
}
```

### 2.2 异常处理的性能考量

**概念讲解**：
异常处理虽然提供了优雅的错误传播机制，但也带来了性能开销。理解异常的性能特征有助于在合适的场景下使用异常。

**【代码演示】异常处理性能测试**
```cpp
#include <iostream>
#include <chrono>
#include <vector>
#include <random>

class PerformanceTest {
public:
    // 使用异常的版本
    static int divide_with_exception(int a, int b) {
        if (b == 0) {
            throw std::invalid_argument("Division by zero");
        }
        return a / b;
    }

    // 使用错误码的版本
    static bool divide_with_errorcode(int a, int b, int& result) {
        if (b == 0) {
            return false;
        }
        result = a / b;
        return true;
    }

    // 使用optional的版本
    static std::optional<int> divide_with_optional(int a, int b) {
        if (b == 0) {
            return std::nullopt;
        }
        return a / b;
    }

    static void performance_comparison() {
        std::cout << "=== Performance Comparison ===" << std::endl;

        const int iterations = 1000000;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(1, 100);

        // 生成测试数据（大部分情况下不会出错）
        std::vector<std::pair<int, int>> test_data;
        for (int i = 0; i < iterations; ++i) {
            int a = dis(gen);
            int b = (i % 10000 == 0) ? 0 : dis(gen);  // 0.01%的错误率
            test_data.emplace_back(a, b);
        }

        // 测试异常版本
        auto start = std::chrono::high_resolution_clock::now();
        int exception_results = 0;
        int exception_errors = 0;

        for (const auto& [a, b] : test_data) {
            try {
                exception_results += divide_with_exception(a, b);
            } catch (const std::exception&) {
                ++exception_errors;
            }
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto exception_time = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

        // 测试错误码版本
        start = std::chrono::high_resolution_clock::now();
        int errorcode_results = 0;
        int errorcode_errors = 0;

        for (const auto& [a, b] : test_data) {
            int result;
            if (divide_with_errorcode(a, b, result)) {
                errorcode_results += result;
            } else {
                ++errorcode_errors;
            }
        }

        end = std::chrono::high_resolution_clock::now();
        auto errorcode_time = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

        // 测试optional版本
        start = std::chrono::high_resolution_clock::now();
        int optional_results = 0;
        int optional_errors = 0;

        for (const auto& [a, b] : test_data) {
            auto result = divide_with_optional(a, b);
            if (result) {
                optional_results += *result;
            } else {
                ++optional_errors;
            }
        }

        end = std::chrono::high_resolution_clock::now();
        auto optional_time = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

        // 输出结果
        std::cout << "Iterations: " << iterations << std::endl;
        std::cout << "Error rate: " << (static_cast<double>(exception_errors) / iterations * 100) << "%" << std::endl;
        std::cout << std::endl;

        std::cout << "Exception approach: " << exception_time.count() << "ms" << std::endl;
        std::cout << "Error code approach: " << errorcode_time.count() << "ms" << std::endl;
        std::cout << "Optional approach: " << optional_time.count() << "ms" << std::endl;
        std::cout << std::endl;

        std::cout << "Results verification:" << std::endl;
        std::cout << "Exception results: " << exception_results << ", errors: " << exception_errors << std::endl;
        std::cout << "Error code results: " << errorcode_results << ", errors: " << errorcode_errors << std::endl;
        std::cout << "Optional results: " << optional_results << ", errors: " << optional_errors << std::endl;
    }
};
```

---

## 附录：异常处理实践指南

### A.1 面试核心问题

1. **什么是异常安全？有哪些级别？**
   > 异常安全是指程序在异常发生时的行为保证。三个级别：基本保证（对象处于有效状态）、强保证（状态回滚）、不抛出保证（绝不抛出异常）。

2. **什么是栈展开？它如何工作？**
   > 栈展开是异常传播时自动调用局部对象析构函数的过程。从抛出点开始，逐层向上查找异常处理器，同时清理局部对象。

3. **何时使用异常，何时使用错误码？**
   > 异常适用于异常情况、跨多层函数传播错误、构造函数错误；错误码适用于预期的错误、性能敏感场景、C接口兼容。

4. **为什么移动构造函数应该是noexcept的？**
   > STL容器在重新分配内存时，如果移动构造函数是noexcept的，会优先使用移动而非复制，提高性能。否则为了异常安全会使用复制。

5. **如何设计异常安全的赋值运算符？**
   > 使用copy-and-swap惯用法或先创建临时对象再交换状态，确保在异常发生时原对象状态不变。

### A.2 权威书籍拓展阅读

*   **《Effective C++ (第3版)》**:
    *   **条款25-30**: 异常处理的最佳实践和陷阱避免

*   **《More Effective C++》**:
    *   **条款9-15**: 深入的异常处理技术和性能考量

*   **《C++ Core Guidelines》**:
    *   **E (Error Handling)**: 错误处理的权威指导原则

### A.3 最佳实践总结

**异常设计原则**：
- 异常用于异常情况，不用于正常控制流
- 按const引用捕获异常，避免对象切片
- 设计清晰的异常层次结构
- 提供有意义的错误信息

**性能优化策略**：
- 在性能关键路径避免异常
- 使用noexcept标记不抛出异常的函数
- 考虑使用std::optional或错误码
- 合理设计异常处理粒度

### A.4 实践挑战

**[初级] 实现异常安全的动态数组**
```cpp
// 要求：
// 1. 提供基本保证和强保证的操作
// 2. 正确处理内存分配异常
// 3. 实现RAII资源管理
// 4. 提供noexcept操作
```

**[中级] 设计文件处理系统**
```cpp
// 要求：
// 1. 自定义异常层次结构
// 2. 异常安全的文件操作
// 3. 资源自动清理
// 4. 错误恢复机制
```

**[高级] 实现事务性内存管理器**
```cpp
// 要求：
// 1. 强异常安全保证
// 2. 支持嵌套事务
// 3. 自动回滚机制
// 4. 性能优化考虑
```

---

> **总结**：异常处理是C++中强大而复杂的特性。正确使用异常可以让代码更加健壮和易于维护，但也需要深入理解其机制和性能特征。RAII是异常安全的基础，noexcept规范是性能优化的关键。在现代C++中，应该根据具体场景选择合适的错误处理策略，平衡代码的正确性、性能和可维护性。
```
