# C++ IO流系统：从底层机制到高性能实践的权威技术指南

> **技术深度声明**：本指南深入探讨C++ IO流的底层实现机制、性能优化策略和现代化最佳实践，旨在构建从系统级理解到工程级应用的完整知识体系。

---

## Part 0: 核心概念速览——理解IO流的本质

### 0.1 什么是IO流？用水管来理解数据流动

想象一下你家的自来水系统：

```mermaid
graph LR
    subgraph "外部数据源"
        A1["⌨️ 键盘输入"]
        A2["📁 文件"]
        A3["🌐 网络"]
        A4["💾 数据库"]
    end

    subgraph "程序内部"
        B["🏠 你的程序<br/>数据处理中心"]
    end

    subgraph "输出目标"
        C1["🖥️ 屏幕显示"]
        C2["📄 文件保存"]
        C3["📡 网络发送"]
        C4["🖨️ 打印机"]
    end

    A1 -->|"📥 输入流 (>>)"| B
    A2 -->|"📥 输入流 (>>)"| B
    A3 -->|"📥 输入流 (>>)"| B
    A4 -->|"📥 输入流 (>>)"| B

    B -->|"📤 输出流 (<<)"| C1
    B -->|"📤 输出流 (<<)"| C2
    B -->|"📤 输出流 (<<)"| C3
    B -->|"📤 输出流 (<<)"| C4

    classDef source fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef program fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    classDef target fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A1,A2,A3,A4 source
    class B program
    class C1,C2,C3,C4 target
```

**IO流的核心思想**：数据像水一样在管道中流动，程序通过"输入流"获取数据，通过"输出流"输出数据。

#### 为什么需要流的概念？

在没有流概念之前，程序员需要：
- 为键盘输入写一套代码
- 为文件读取写另一套代码
- 为网络通信再写一套代码

**流的统一抽象解决了这个问题**：

| 数据源/目标 | 传统方式 | 流方式 |
|------------|---------|--------|
| 键盘输入 | `scanf()` | `std::cin >>` |
| 文件读取 | `fread()` | `file >>` |
| 字符串解析 | `sscanf()` | `stringstream >>` |
| 网络接收 | `recv()` | `network_stream >>` |

**一个操作符，处理所有数据源！**

```cpp
// 同样的代码，不同的数据源
template<typename StreamType>
void ReadData(StreamType& stream) {
    int number;
    std::string text;
    stream >> number >> text;  // 统一的语法！
}

// 可以用于任何流
ReadData(std::cin);           // 从键盘读取
ReadData(file_stream);        // 从文件读取
ReadData(string_stream);      // 从字符串读取
```

### 0.2 IO流的三大核心优势

#### 🛡️ **1. 类型安全 - 编译期错误检查**

**传统C方式的问题**：
```c
printf("%d %s", "hello", 42);  // 运行时崩溃！类型不匹配
```

**C++流的解决方案**：
```cpp
std::cout << "hello" << 42;    // 编译期自动推导类型，永远不会出错
```

#### 🔧 **2. 可扩展性 - 支持自定义类型**

```cpp
class Point {
    int x, y;
public:
    Point(int x, int y) : x(x), y(y) {}

    // 定义输出方式
    friend std::ostream& operator<<(std::ostream& os, const Point& p) {
        return os << "(" << p.x << ", " << p.y << ")";
    }
};

Point p(3, 4);
std::cout << p;  // 输出: (3, 4)
```

#### ⚡ **3. 性能优化 - 智能缓冲**

流系统内置缓冲机制，就像：
- **小水杯**：每次接一点水（无缓冲）- 效率低
- **大水桶**：攒够了一桶再处理（缓冲）- 效率高

```
无缓冲: 程序 → 系统调用 → 硬盘 (每个字符都要系统调用)
有缓冲: 程序 → 缓冲区 → 系统调用 → 硬盘 (积累到一定量再调用)
```

### 0.3 IO流的家族树：从抽象到具体

```mermaid
graph TD
    A["🏗️ ios_base<br/>基础设施<br/><small>错误状态、格式控制</small>"] --> B["⚙️ basic_ios&lt;char&gt;<br/>基本功能<br/><small>流状态管理</small>"]

    B --> C["📥 istream<br/>输入流<br/><small>从外部读取数据</small>"]
    B --> D["📤 ostream<br/>输出流<br/><small>向外部写入数据</small>"]
    B --> E["🔄 iostream<br/>双向流<br/><small>既能读又能写</small>"]

    C --> F["📁 ifstream<br/>文件输入流<br/><small>从文件读取</small>"]
    C --> G["💭 istringstream<br/>字符串输入流<br/><small>从字符串读取</small>"]

    D --> H["📄 ofstream<br/>文件输出流<br/><small>写入到文件</small>"]
    D --> I["📝 ostringstream<br/>字符串输出流<br/><small>写入到字符串</small>"]

    E --> J["📂 fstream<br/>文件双向流<br/><small>文件读写</small>"]
    E --> K["🔤 stringstream<br/>字符串双向流<br/><small>字符串读写</small>"]

    classDef baseClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef inputClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef outputClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef bidirClass fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class A,B baseClass
    class C,F,G inputClass
    class D,H,I outputClass
    class E,J,K bidirClass
```

**简单理解**：
- `istream` = 所有输入流的爸爸
- `ostream` = 所有输出流的妈妈
- `ifstream` = 专门读文件的孩子
- `ofstream` = 专门写文件的孩子
- `stringstream` = 在内存里玩的孩子

### 0.4 最简单的IO流使用

#### 输出流 - 就像说话
```cpp
std::cout << "Hello";           // 说一个词
std::cout << " World" << "!";   // 连续说几个词
std::cout << std::endl;         // 说完换行
```

#### 输入流 - 就像听话
```cpp
int age;
std::string name;
std::cin >> age >> name;        // 听两个词：数字和文字
```

#### 文件流 - 就像读写日记
```cpp
// 写日记
std::ofstream diary("my_diary.txt");
diary << "今天学了IO流，很有趣！";

// 读日记
std::ifstream read_diary("my_diary.txt");
std::string content;
std::getline(read_diary, content);
std::cout << content;
```

**看，就这么简单！** 核心就是 `<<`（输出）和 `>>`（输入）两个操作符。

### 0.5 流状态：程序的"健康检查"

就像人会生病一样，流也会出现各种状态。理解流状态是IO编程的关键。

#### 流的四种状态

| 状态位 | 含义 | 生活比喻 | 何时发生 |
|--------|------|----------|----------|
| `goodbit` | 一切正常 | 身体健康 | 操作成功 |
| `eofbit` | 到达文件末尾 | 书读完了 | 没有更多数据 |
| `failbit` | 操作失败 | 感冒了 | 格式错误、类型不匹配 |
| `badbit` | 严重错误 | 重病 | 硬件故障、内存不足 |

#### 检查流状态的方法

```cpp
std::ifstream file("test.txt");

// 方法1：直接转换为bool
if (file) {
    std::cout << "文件打开成功" << std::endl;
}

// 方法2：检查具体状态
if (file.good()) {
    std::cout << "流状态完全正常" << std::endl;
}
if (file.eof()) {
    std::cout << "已读到文件末尾" << std::endl;
}
if (file.fail()) {
    std::cout << "操作失败了" << std::endl;
}
if (file.bad()) {
    std::cout << "出现严重错误" << std::endl;
}
```

#### 流状态转换图

```mermaid
stateDiagram-v2
    [*] --> Good : 流创建成功

    Good --> Good : 正常读写操作
    Good --> Fail : 格式错误/类型不匹配
    Good --> EOF : 读到文件末尾
    Good --> Bad : 硬件故障/严重错误

    Fail --> Good : clear() + 重新操作
    EOF --> Good : clear() + seekg()重定位
    Bad --> Bad : 严重错误，通常无法恢复

    state Good {
        [*] --> ReadyToUse
        ReadyToUse : ✅ 可以正常读写
        ReadyToUse : goodbit = true
    }

    state Fail {
        [*] --> OperationFailed
        OperationFailed : ❌ 操作失败
        OperationFailed : failbit = true
        OperationFailed : 需要 clear() 恢复
    }

    state EOF {
        [*] --> EndOfFile
        EndOfFile : 📄 文件读完了
        EndOfFile : eofbit = true
        EndOfFile : 可以重新定位
    }

    state Bad {
        [*] --> SeriousError
        SeriousError : 💥 严重错误
        SeriousError : badbit = true
        SeriousError : 通常无法恢复
    }
```

#### 流状态恢复的标准流程

```cpp
int number;
std::cin >> number;  // 如果输入"abc"，会失败

if (std::cin.fail()) {
    std::cout << "输入格式错误！" << std::endl;
    std::cin.clear();                    // 🔧 清除错误状态
    std::cin.ignore(1000, '\n');        // 🧹 清空输入缓冲区
    std::cout << "请重新输入数字：";
    std::cin >> number;                  // 🔄 重新尝试
}
```

**关键理解**：流状态就像交通信号灯，告诉你当前能不能继续操作。

### 0.6 缓冲机制：为什么IO这么快？

#### 缓冲机制的可视化对比

```mermaid
graph TD
    subgraph "无缓冲方式 (效率低)"
        A1["程序写入 'H'"] --> B1["立即系统调用"]
        B1 --> C1["磁盘写入 'H'"]
        A2["程序写入 'e'"] --> B2["立即系统调用"]
        B2 --> C2["磁盘写入 'e'"]
        A3["程序写入 'l'"] --> B3["立即系统调用"]
        B3 --> C3["磁盘写入 'l'"]
        A4["..."] --> B4["..."]
        B4 --> C4["..."]
    end

    subgraph "有缓冲方式 (效率高)"
        D1["程序写入 'Hello World!'"] --> E1["📦 缓冲区<br/>积累数据"]
        E1 --> F1["缓冲区满或手动刷新"]
        F1 --> G1["一次系统调用"]
        G1 --> H1["磁盘写入完整数据"]
    end

    classDef inefficient fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef efficient fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef buffer fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class A1,A2,A3,A4,B1,B2,B3,B4,C1,C2,C3,C4 inefficient
    class D1,F1,G1,H1 efficient
    class E1 buffer
```

**关键理解**：缓冲就像用大箱子搬家，而不是一个杯子一个杯子地搬！

#### 三种缓冲策略

| 缓冲类型 | 特点 | 适用场景 | 例子 |
|----------|------|----------|------|
| **全缓冲** | 缓冲区满了才输出 | 文件操作 | 写文件时积累数据 |
| **行缓冲** | 遇到换行符就输出 | 终端交互 | `std::cout` 遇到 `\n` |
| **无缓冲** | 立即输出 | 错误信息 | `std::cerr` 立即显示错误 |

#### 手动控制缓冲

```cpp
std::cout << "Hello";           // 可能还在缓冲区里
std::cout << "World" << std::flush;  // 强制输出，不换行
std::cout << "!" << std::endl;       // 输出并换行
```

**记住**：
- `std::flush` = 冲马桶（强制输出）
- `std::endl` = 换行 + 冲马桶
- `'\n'` = 只换行，不冲马桶

### 0.3 流状态机制：错误处理的现代化方案

IO流内置了精密的**状态管理系统** (State Management System)，通过位标志 (Bit Flags) 跟踪流的健康状态：

```cpp
#include <iostream>
#include <fstream>
#include <bitset>

class StreamStateAnalyzer {
public:
    static void AnalyzeState(const std::ios& stream, const std::string& context) {
        std::cout << "\n=== " << context << " 流状态分析 ===" << std::endl;
        
        // 获取状态位
        auto state = stream.rdstate();
        
        std::cout << "状态位模式: " << std::bitset<4>(state) << std::endl;
        std::cout << "goodbit (正常): " << stream.good() << std::endl;
        std::cout << "eofbit (文件结束): " << stream.eof() << std::endl;
        std::cout << "failbit (操作失败): " << stream.fail() << std::endl;
        std::cout << "badbit (严重错误): " << stream.bad() << std::endl;
        std::cout << "布尔转换结果: " << static_cast<bool>(stream) << std::endl;
    }
};

void StreamStateDemo() {
    std::ifstream file("nonexistent.txt");
    StreamStateAnalyzer::AnalyzeState(file, "打开不存在文件后");
    
    // 尝试读取
    std::string content;
    file >> content;
    StreamStateAnalyzer::AnalyzeState(file, "读取失败后");
    
    // 状态恢复
    file.clear();  // 清除错误标志
    StreamStateAnalyzer::AnalyzeState(file, "clear()后");
}
```

### 0.4 缓冲机制：性能优化的核心

IO流的高性能来源于其精密的**缓冲系统** (Buffering System)，通过 `basic_streambuf` 模板类实现：

```cpp
#include <iostream>
#include <fstream>
#include <chrono>

class BufferingPerformanceTest {
public:
    static void CompareBufferedVsUnbuffered() {
        const int iterations = 100000;
        const std::string filename = "perf_test.txt";
        
        // 测试1：缓冲输出
        auto start = std::chrono::high_resolution_clock::now();
        {
            std::ofstream file(filename);
            for (int i = 0; i < iterations; ++i) {
                file << "Line " << i << "\n";
            }
        } // 文件关闭时自动刷新缓冲区
        auto end = std::chrono::high_resolution_clock::now();
        auto buffered_time = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        // 测试2：无缓冲输出（每次写入都刷新）
        start = std::chrono::high_resolution_clock::now();
        {
            std::ofstream file(filename);
            for (int i = 0; i < iterations; ++i) {
                file << "Line " << i << "\n" << std::flush;  // 强制刷新
            }
        }
        end = std::chrono::high_resolution_clock::now();
        auto unbuffered_time = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        std::cout << "缓冲输出时间: " << buffered_time.count() << " ms" << std::endl;
        std::cout << "无缓冲输出时间: " << unbuffered_time.count() << " ms" << std::endl;
        std::cout << "性能提升: " << (double)unbuffered_time.count() / buffered_time.count() << "x" << std::endl;
    }
};
```

---

## Part 1: 输入输出流的深度剖析

### 1.1 标准流对象：全局IO接口的实现机制

C++提供了四个预定义的全局流对象，它们在程序启动时自动初始化：

```cpp
#include <iostream>
#include <iomanip>

class StandardStreamAnalyzer {
public:
    static void AnalyzeStandardStreams() {
        std::cout << "=== 标准流对象技术分析 ===" << std::endl;
        
        // cin: 标准输入流 (通常连接到键盘)
        std::cout << "std::cin 缓冲区地址: " << std::cin.rdbuf() << std::endl;
        std::cout << "std::cin 是否与 cout 绑定: " << (std::cin.tie() == &std::cout) << std::endl;
        
        // cout: 标准输出流 (通常连接到屏幕)
        std::cout << "std::cout 缓冲区地址: " << std::cout.rdbuf() << std::endl;
        std::cout << "std::cout 缓冲区类型: " << typeid(*std::cout.rdbuf()).name() << std::endl;
        
        // cerr: 标准错误流 (无缓冲，立即输出)
        std::cout << "std::cerr 是否与 cout 使用相同缓冲区: " 
                  << (std::cerr.rdbuf() == std::cout.rdbuf()) << std::endl;
        
        // clog: 标准日志流 (有缓冲的错误流)
        std::cout << "std::clog 缓冲区地址: " << std::clog.rdbuf() << std::endl;
    }
    
    static void DemonstrateStreamSynchronization() {
        std::cout << "=== 流同步机制演示 ===" << std::endl;
        
        // 默认情况下，C++流与C标准IO同步
        std::cout << "C++流与C IO同步状态: " << std::ios::sync_with_stdio() << std::endl;
        
        // 关闭同步以提高性能（但不能再混用printf和cout）
        std::ios::sync_with_stdio(false);
        std::cout << "关闭同步后状态: " << std::ios::sync_with_stdio() << std::endl;
        
        // cin与cout的绑定机制
        std::cout << "cin当前绑定的流: " << std::cin.tie() << std::endl;
        
        // 解除绑定（可能影响交互式程序的用户体验）
        auto old_tie = std::cin.tie(nullptr);
        std::cout << "解除绑定后: " << std::cin.tie() << std::endl;
        
        // 恢复绑定
        std::cin.tie(old_tie);
    }
};
```

### 1.2 格式化输入输出：类型安全的数据转换

IO流的格式化系统通过**操纵符** (Manipulators) 和**格式标志** (Format Flags) 实现精确的数据表示控制：

```cpp
#include <iostream>
#include <iomanip>
#include <limits>
#include <bitset>

class FormattingMasterClass {
public:
    static void NumericFormattingDemo() {
        std::cout << "=== 数值格式化技术演示 ===" << std::endl;
        
        double pi = 3.141592653589793;
        int value = 255;
        
        // 浮点数精度控制
        std::cout << "默认精度: " << pi << std::endl;
        std::cout << std::setprecision(10) << "10位精度: " << pi << std::endl;
        std::cout << std::fixed << std::setprecision(6) << "固定6位小数: " << pi << std::endl;
        std::cout << std::scientific << "科学计数法: " << pi << std::endl;
        
        // 整数进制转换
        std::cout << std::resetiosflags(std::ios::floatfield) << std::endl;
        std::cout << "十进制: " << value << std::endl;
        std::cout << "十六进制: " << std::hex << value << std::endl;
        std::cout << "八进制: " << std::oct << value << std::endl;
        std::cout << "二进制: " << std::bitset<8>(value) << std::endl;
        
        // 恢复十进制
        std::cout << std::dec;
    }
    
    static void AdvancedFormattingTechniques() {
        std::cout << "\n=== 高级格式化技术 ===" << std::endl;
        
        // 字段宽度和对齐
        std::cout << std::setw(20) << std::left << "左对齐" << "|" << std::endl;
        std::cout << std::setw(20) << std::right << "右对齐" << "|" << std::endl;
        std::cout << std::setw(20) << std::internal << -123.45 << "|" << std::endl;
        
        // 填充字符
        std::cout << std::setfill('*') << std::setw(15) << 42 << std::endl;
        std::cout << std::setfill(' '); // 恢复默认填充
        
        // 布尔值格式化
        std::cout << std::boolalpha << "布尔值: " << true << ", " << false << std::endl;
        std::cout << std::noboolalpha << "数值形式: " << true << ", " << false << std::endl;
        
        // 显示正号和进制前缀
        std::cout << std::showpos << std::showbase;
        std::cout << "十进制: " << 42 << std::endl;
        std::cout << std::hex << "十六进制: " << 42 << std::endl;
        std::cout << std::oct << "八进制: " << 42 << std::endl;
        
        // 恢复默认格式
        std::cout << std::noshowpos << std::noshowbase << std::dec;
    }
};
```

### 1.3 输入流的高级技术：错误处理与数据验证

输入流的健壮性设计需要深入理解其**状态管理**和**错误恢复**机制：

```cpp
#include <iostream>
#include <sstream>
#include <string>
#include <limits>

class RobustInputHandler {
public:
    // 类型安全的数值输入
    template<typename T>
    static bool SafeInput(T& value, const std::string& prompt = "") {
        if (!prompt.empty()) {
            std::cout << prompt;
        }
        
        std::string input;
        std::getline(std::cin, input);
        
        std::istringstream iss(input);
        T temp;
        
        // 尝试提取值
        if (!(iss >> temp)) {
            return false;  // 提取失败
        }
        
        // 检查是否有剩余字符（除了空白字符）
        std::string remaining;
        if (iss >> remaining) {
            return false;  // 有非空白字符剩余
        }
        
        value = temp;
        return true;
    }
    
    // 带范围验证的输入
    template<typename T>
    static T GetValidatedInput(const std::string& prompt, T min_val, T max_val) {
        T value;
        while (true) {
            if (SafeInput(value, prompt)) {
                if (value >= min_val && value <= max_val) {
                    return value;
                } else {
                    std::cout << "值必须在 [" << min_val << ", " << max_val << "] 范围内。" << std::endl;
                }
            } else {
                std::cout << "输入格式错误，请重新输入。" << std::endl;
            }
        }
    }
    
    // 流状态恢复技术
    static void DemonstrateStreamRecovery() {
        std::cout << "=== 流状态恢复技术演示 ===" << std::endl;
        
        std::istringstream iss("123 abc 456");
        int value;
        
        // 第一次读取成功
        if (iss >> value) {
            std::cout << "成功读取: " << value << std::endl;
        }
        
        // 第二次读取失败
        if (iss >> value) {
            std::cout << "成功读取: " << value << std::endl;
        } else {
            std::cout << "读取失败，流状态: " << iss.rdstate() << std::endl;
            
            // 清除错误状态
            iss.clear();
            
            // 跳过错误的输入
            std::string garbage;
            iss >> garbage;
            std::cout << "跳过的垃圾数据: " << garbage << std::endl;
            
            // 继续读取
            if (iss >> value) {
                std::cout << "恢复后成功读取: " << value << std::endl;
            }
        }
    }
};
```

---

## Part 2: 文件操作基础：从入门到精通

### 2.1 文件操作的核心概念

#### 文件操作就像操作文件柜

想象你有一个文件柜，里面存放着各种文档：

📁 **文件** = 文件柜里的文档
📖 **读取** = 从文档中查看信息
✏️ **写入** = 在文档上记录信息
🔍 **定位** = 翻到文档的特定页面

#### 三种文件操作方式

| 流类型 | 作用 | 生活比喻 | 使用场景 |
|--------|------|----------|----------|
| `ifstream` | 只读文件 | 只能看书，不能写字 | 读取配置文件、日志分析 |
| `ofstream` | 只写文件 | 只能写字，不能看 | 生成报告、保存数据 |
| `fstream` | 读写文件 | 既能看又能写 | 数据库操作、文件编辑 |

#### 文件打开模式：告诉系统你要干什么

就像进入房间前要说明来意一样，打开文件前要说明用途：

| 模式 | 含义 | 生活比喻 | 效果 |
|------|------|----------|------|
| `ios::in` | 读取模式 | "我来看书" | 只能读，不能写 |
| `ios::out` | 写入模式 | "我来写字" | 清空文件重新写 |
| `ios::app` | 追加模式 | "我来续写" | 在文件末尾继续写 |
| `ios::ate` | 定位到末尾 | "翻到最后一页" | 打开时跳到文件末尾 |
| `ios::trunc` | 截断模式 | "撕掉重写" | 清空文件内容 |
| `ios::binary` | 二进制模式 | "按原样处理" | 不转换换行符等 |

#### 文件打开模式的可视化

```mermaid
graph TD
    subgraph "文件操作模式"
        A["📁 文件"]
    end

    subgraph "只读模式 (ios::in)"
        B1["👀 只能看<br/>不能改"] --> B2["文件必须存在<br/>否则打开失败"]
    end

    subgraph "只写模式 (ios::out)"
        C1["✏️ 只能写<br/>不能读"] --> C2["🗑️ 清空原内容<br/>重新开始写"]
    end

    subgraph "追加模式 (ios::app)"
        D1["➕ 只能写<br/>不能读"] --> D2["📝 在末尾继续写<br/>保留原内容"]
    end

    subgraph "读写模式 (ios::in | ios::out)"
        E1["🔄 既能读又能写"] --> E2["📖 保留原内容<br/>可以修改"]
    end

    subgraph "截断读写 (ios::in | ios::out | ios::trunc)"
        F1["🔄 既能读又能写"] --> F2["🗑️ 清空原内容<br/>重新开始"]
    end

    A --> B1
    A --> C1
    A --> D1
    A --> E1
    A --> F1

    classDef readOnly fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef writeOnly fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef append fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef readWrite fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef truncate fill:#ffebee,stroke:#d32f2f,stroke-width:2px

    class B1,B2 readOnly
    class C1,C2 writeOnly
    class D1,D2 append
    class E1,E2 readWrite
    class F1,F2 truncate
```

#### 模式组合速查表

| 模式组合 | 效果 | 使用场景 |
|----------|------|----------|
| `ios::in` | 👀 只读，文件必须存在 | 读取配置文件 |
| `ios::out` | ✏️ 只写，清空文件 | 生成新报告 |
| `ios::out \| ios::app` | ➕ 只写，追加到末尾 | 写日志文件 |
| `ios::in \| ios::out` | 🔄 读写，保留内容 | 修改数据文件 |
| `ios::in \| ios::out \| ios::trunc` | 🔄 读写，清空文件 | 重建数据文件 |
| `ios::binary` | 💾 二进制模式 | 可与上述任意组合 |

### 2.2 文本文件操作：像读写文章一样简单

#### 写入文本文件

```cpp
#include <fstream>

// 最简单的文件写入
std::ofstream file("diary.txt");
file << "今天天气很好" << std::endl;
file << "学习了C++ IO流" << std::endl;
// 文件自动关闭
```

#### 读取文本文件的三种方式

```cpp
std::ifstream file("diary.txt");

// 方式1：逐行读取（最常用）
std::string line;
while (std::getline(file, line)) {
    std::cout << line << std::endl;
}

// 方式2：按单词读取
std::string word;
while (file >> word) {
    std::cout << word << " ";
}

// 方式3：一次读取整个文件
std::ostringstream buffer;
buffer << file.rdbuf();
std::string content = buffer.str();
```

#### 文件操作的最佳实践

**✅ 推荐做法**：
```cpp
{
    std::ofstream file("data.txt");
    if (file.is_open()) {
        file << "数据内容";
        // 作用域结束时自动关闭文件
    }
}
```

**❌ 不推荐做法**：
```cpp
std::ofstream file("data.txt");
// 忘记检查文件是否打开成功
file << "数据内容";
// 忘记关闭文件
```

```cpp
#include <fstream>
#include <iostream>
#include <string>

class FileOperationBasics {
public:
    // 演示三种文件流类型
    static void DemonstrateFileStreamTypes() {
        std::cout << "=== 文件流类型演示 ===" << std::endl;

        const std::string filename = "demo.txt";

        // 1. ofstream - 输出文件流（只写）
        {
            std::ofstream outFile(filename);
            if (outFile.is_open()) {
                outFile << "这是第一行文本" << std::endl;
                outFile << "这是第二行文本" << std::endl;
                outFile << "数字: " << 42 << std::endl;
                outFile.close();
                std::cout << "✅ 使用 ofstream 写入文件成功" << std::endl;
            } else {
                std::cout << "❌ 无法创建文件" << std::endl;
            }
        }

        // 2. ifstream - 输入文件流（只读）
        {
            std::ifstream inFile(filename);
            if (inFile.is_open()) {
                std::string line;
                int lineNumber = 1;
                std::cout << "使用 ifstream 读取文件内容:" << std::endl;
                while (std::getline(inFile, line)) {
                    std::cout << "第" << lineNumber++ << "行: " << line << std::endl;
                }
                inFile.close();
                std::cout << "✅ 使用 ifstream 读取文件成功" << std::endl;
            } else {
                std::cout << "❌ 无法打开文件进行读取" << std::endl;
            }
        }

        // 3. fstream - 双向文件流（读写）
        {
            std::fstream file(filename, std::ios::in | std::ios::out | std::ios::app);
            if (file.is_open()) {
                // 追加写入
                file << "这是追加的一行" << std::endl;

                // 移动到文件开头进行读取
                file.seekg(0, std::ios::beg);

                std::string line;
                std::cout << "使用 fstream 读取完整文件:" << std::endl;
                while (std::getline(file, line)) {
                    std::cout << line << std::endl;
                }

                file.close();
                std::cout << "✅ 使用 fstream 读写文件成功" << std::endl;
            } else {
                std::cout << "❌ 无法打开文件进行读写" << std::endl;
            }
        }
    }
};
```

#### 2.1.2 文件打开模式详解

文件打开模式决定了文件的访问方式和行为：

```cpp
class FileOpenModes {
public:
    static void DemonstrateOpenModes() {
        std::cout << "\n=== 文件打开模式详解 ===" << std::endl;

        const std::string filename = "mode_test.txt";

        // 创建测试文件
        CreateTestFile(filename);

        // 1. std::ios::in - 只读模式
        DemonstrateReadMode(filename);

        // 2. std::ios::out - 只写模式（会截断文件）
        DemonstrateWriteMode(filename);

        // 3. std::ios::app - 追加模式
        DemonstrateAppendMode(filename);

        // 4. std::ios::ate - 打开时定位到文件末尾
        DemonstrateAtEndMode(filename);

        // 5. std::ios::trunc - 截断模式
        DemonstrateTruncateMode(filename);

        // 6. std::ios::binary - 二进制模式
        DemonstrateBinaryMode();
    }

private:
    static void CreateTestFile(const std::string& filename) {
        std::ofstream file(filename);
        file << "原始内容第1行" << std::endl;
        file << "原始内容第2行" << std::endl;
        file << "原始内容第3行" << std::endl;
    }

    static void DemonstrateReadMode(const std::string& filename) {
        std::cout << "\n--- std::ios::in (只读模式) ---" << std::endl;
        std::ifstream file(filename, std::ios::in);
        if (file) {
            std::string content;
            while (std::getline(file, content)) {
                std::cout << "读取: " << content << std::endl;
            }
        }
    }

    static void DemonstrateWriteMode(const std::string& filename) {
        std::cout << "\n--- std::ios::out (只写模式，会截断) ---" << std::endl;
        {
            std::ofstream file(filename, std::ios::out);
            file << "新内容替换了原始内容" << std::endl;
        }

        // 验证内容被替换
        std::ifstream readFile(filename);
        std::string line;
        while (std::getline(readFile, line)) {
            std::cout << "文件现在包含: " << line << std::endl;
        }
    }

    static void DemonstrateAppendMode(const std::string& filename) {
        std::cout << "\n--- std::ios::app (追加模式) ---" << std::endl;
        {
            std::ofstream file(filename, std::ios::app);
            file << "这是追加的内容" << std::endl;
        }

        // 显示完整内容
        std::ifstream readFile(filename);
        std::string line;
        std::cout << "追加后的完整内容:" << std::endl;
        while (std::getline(readFile, line)) {
            std::cout << line << std::endl;
        }
    }

    static void DemonstrateAtEndMode(const std::string& filename) {
        std::cout << "\n--- std::ios::ate (打开时定位到末尾) ---" << std::endl;
        std::fstream file(filename, std::ios::in | std::ios::out | std::ios::ate);
        if (file) {
            std::cout << "文件打开时位置: " << file.tellg() << std::endl;

            // 移动到开头读取
            file.seekg(0, std::ios::beg);
            std::string line;
            std::getline(file, line);
            std::cout << "第一行内容: " << line << std::endl;
        }
    }

    static void DemonstrateTruncateMode(const std::string& filename) {
        std::cout << "\n--- std::ios::trunc (截断模式) ---" << std::endl;
        {
            std::ofstream file(filename, std::ios::out | std::ios::trunc);
            file << "截断后的新内容" << std::endl;
        }

        std::ifstream readFile(filename);
        std::string line;
        std::cout << "截断后的内容:" << std::endl;
        while (std::getline(readFile, line)) {
            std::cout << line << std::endl;
        }
    }

    static void DemonstrateBinaryMode() {
        std::cout << "\n--- std::ios::binary (二进制模式) ---" << std::endl;
        const std::string binFile = "binary_test.dat";

        // 写入二进制数据
        {
            std::ofstream file(binFile, std::ios::binary);
            int numbers[] = {1, 2, 3, 4, 5};
            file.write(reinterpret_cast<const char*>(numbers), sizeof(numbers));
        }

        // 读取二进制数据
        {
            std::ifstream file(binFile, std::ios::binary);
            int numbers[5];
            file.read(reinterpret_cast<char*>(numbers), sizeof(numbers));

            std::cout << "读取的二进制数据: ";
            for (int i = 0; i < 5; ++i) {
                std::cout << numbers[i] << " ";
            }
            std::cout << std::endl;
        }
    }
};
```

### 2.2 文本文件操作详解

#### 2.2.1 基本文本文件读写

```cpp
#include <fstream>
#include <vector>
#include <sstream>

class TextFileOperations {
public:
    // 逐行写入文本文件
    static void WriteTextFile(const std::string& filename, const std::vector<std::string>& lines) {
        std::cout << "\n=== 写入文本文件 ===" << std::endl;

        std::ofstream file(filename);
        if (!file.is_open()) {
            std::cerr << "❌ 无法创建文件: " << filename << std::endl;
            return;
        }

        for (size_t i = 0; i < lines.size(); ++i) {
            file << lines[i];
            if (i < lines.size() - 1) {  // 除了最后一行，都添加换行符
                file << std::endl;
            }
        }

        std::cout << "✅ 成功写入 " << lines.size() << " 行到文件: " << filename << std::endl;
    }

    // 逐行读取文本文件
    static std::vector<std::string> ReadTextFile(const std::string& filename) {
        std::cout << "\n=== 读取文本文件 ===" << std::endl;

        std::vector<std::string> lines;
        std::ifstream file(filename);

        if (!file.is_open()) {
            std::cerr << "❌ 无法打开文件: " << filename << std::endl;
            return lines;
        }

        std::string line;
        while (std::getline(file, line)) {
            lines.push_back(line);
        }

        std::cout << "✅ 成功读取 " << lines.size() << " 行从文件: " << filename << std::endl;
        return lines;
    }

    // 一次性读取整个文件
    static std::string ReadEntireFile(const std::string& filename) {
        std::cout << "\n=== 一次性读取整个文件 ===" << std::endl;

        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cerr << "❌ 无法打开文件: " << filename << std::endl;
            return "";
        }

        // 方法1: 使用 stringstream
        std::ostringstream buffer;
        buffer << file.rdbuf();
        std::string content = buffer.str();

        std::cout << "✅ 读取文件大小: " << content.size() << " 字节" << std::endl;
        return content;
    }

    // 按单词读取文件
    static std::vector<std::string> ReadWords(const std::string& filename) {
        std::cout << "\n=== 按单词读取文件 ===" << std::endl;

        std::vector<std::string> words;
        std::ifstream file(filename);

        if (!file.is_open()) {
            std::cerr << "❌ 无法打开文件: " << filename << std::endl;
            return words;
        }

        std::string word;
        while (file >> word) {  // 自动跳过空白字符
            words.push_back(word);
        }

        std::cout << "✅ 读取到 " << words.size() << " 个单词" << std::endl;
        return words;
    }

    // 格式化写入（类似printf）
    static void WriteFormattedData(const std::string& filename) {
        std::cout << "\n=== 格式化数据写入 ===" << std::endl;

        std::ofstream file(filename);
        if (!file.is_open()) {
            std::cerr << "❌ 无法创建文件: " << filename << std::endl;
            return;
        }

        // 写入表格数据
        file << std::left << std::setw(10) << "姓名"
             << std::setw(8) << "年龄"
             << std::setw(12) << "薪资" << std::endl;
        file << std::string(30, '-') << std::endl;

        file << std::left << std::setw(10) << "张三"
             << std::setw(8) << 25
             << std::fixed << std::setprecision(2) << std::setw(12) << 5000.50 << std::endl;

        file << std::left << std::setw(10) << "李四"
             << std::setw(8) << 30
             << std::fixed << std::setprecision(2) << std::setw(12) << 7500.75 << std::endl;

        std::cout << "✅ 格式化数据写入完成" << std::endl;
    }

    // 解析格式化数据
    static void ParseFormattedData(const std::string& filename) {
        std::cout << "\n=== 解析格式化数据 ===" << std::endl;

        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cerr << "❌ 无法打开文件: " << filename << std::endl;
            return;
        }

        std::string line;
        int lineNumber = 0;

        while (std::getline(file, line)) {
            ++lineNumber;

            // 跳过表头和分隔线
            if (lineNumber <= 2) {
                std::cout << "跳过: " << line << std::endl;
                continue;
            }

            // 解析数据行
            std::istringstream iss(line);
            std::string name;
            int age;
            double salary;

            if (iss >> name >> age >> salary) {
                std::cout << "解析结果 - 姓名: " << name
                          << ", 年龄: " << age
                          << ", 薪资: " << salary << std::endl;
            } else {
                std::cout << "❌ 解析失败: " << line << std::endl;
            }
        }
    }
};
```

---

## Part 3: 二进制文件操作专题

### 3.1 什么是二进制文件？为什么需要它？

#### 文本文件 vs 二进制文件的存储对比

```mermaid
graph TD
    subgraph "要保存的数据"
        A["数字: 12345<br/>浮点: 3.14159<br/>字符: 'A'"]
    end

    subgraph "文本文件存储方式"
        B1["📝 转换为字符串"]
        B2["'1' '2' '3' '4' '5'<br/>'3' '.' '1' '4' '1' '5' '9'<br/>'A'"]
        B3["💾 存储: 13 字节<br/>✅ 人类可读<br/>❌ 空间效率低<br/>❌ 精度可能丢失"]
    end

    subgraph "二进制文件存储方式"
        C1["🔢 直接存储内存表示"]
        C2["00110000 00111001<br/>01000000 01001001 00001111 11011011<br/>01000001"]
        C3["💾 存储: 9 字节<br/>❌ 人类不可读<br/>✅ 空间效率高<br/>✅ 精度完全保持"]
    end

    A --> B1
    B1 --> B2
    B2 --> B3

    A --> C1
    C1 --> C2
    C2 --> C3

    classDef data fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef text fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef binary fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A data
    class B1,B2,B3 text
    class C1,C2,C3 binary
```

#### 二进制文件的优势

| 特点 | 文本文件 | 二进制文件 |
|------|----------|------------|
| **可读性** | 人类可读 | 需要程序解析 |
| **存储效率** | 低（需要格式转换） | 高（直接存储） |
| **读写速度** | 慢（需要格式转换） | 快（直接复制内存） |
| **精度保持** | 可能丢失精度 | 完全保持精度 |
| **跨平台** | 较好 | 需要考虑字节序 |

#### 什么时候使用二进制文件？

- ✅ **大量数值数据**：科学计算、图像处理
- ✅ **性能要求高**：游戏存档、数据库
- ✅ **精度要求高**：金融数据、工程计算
- ✅ **自定义格式**：专有文件格式

### 3.2 二进制文件的基本操作

#### 写入基本数据类型

```cpp
#include <fstream>

// 写入不同类型的数据
std::ofstream file("data.bin", std::ios::binary);

int number = 42;
double pi = 3.14159;
char letter = 'A';

file.write(reinterpret_cast<const char*>(&number), sizeof(number));
file.write(reinterpret_cast<const char*>(&pi), sizeof(pi));
file.write(reinterpret_cast<const char*>(&letter), sizeof(letter));
```

#### 读取基本数据类型

```cpp
std::ifstream file("data.bin", std::ios::binary);

int number;
double pi;
char letter;

file.read(reinterpret_cast<char*>(&number), sizeof(number));
file.read(reinterpret_cast<char*>(&pi), sizeof(pi));
file.read(reinterpret_cast<char*>(&letter), sizeof(letter));

std::cout << "数字: " << number << std::endl;
std::cout << "π: " << pi << std::endl;
std::cout << "字母: " << letter << std::endl;
```

#### 理解 `reinterpret_cast`

这个看起来复杂的转换其实很简单：

```cpp
int number = 42;

// 告诉编译器："把这个int的内存地址当作char*来看待"
const char* bytes = reinterpret_cast<const char*>(&number);

// 就像告诉快递员："这个包裹里装的是书，按书的方式处理"
// 实际上可能装的是衣服，但我们需要按书的方式打包
```

### 3.3 结构体的序列化：保存复杂数据

#### 简单结构体（POD类型）

```cpp
struct Student {
    int id;
    char name[32];  // 固定长度字符串
    double grade;
};

// 写入结构体
Student s = {1001, "张三", 85.5};
std::ofstream file("student.bin", std::ios::binary);
file.write(reinterpret_cast<const char*>(&s), sizeof(s));

// 读取结构体
Student s2;
std::ifstream infile("student.bin", std::ios::binary);
infile.read(reinterpret_cast<char*>(&s2), sizeof(s2));
```

#### 复杂结构体的处理

对于包含 `std::string`、`std::vector` 等动态数据的结构体，需要自定义序列化：

```cpp
struct ComplexStudent {
    int id;
    std::string name;        // 动态长度
    std::vector<double> grades;  // 动态数组

    // 序列化：把对象转换为字节流
    void SaveTo(std::ostream& os) const {
        os.write(reinterpret_cast<const char*>(&id), sizeof(id));

        // 保存字符串：先保存长度，再保存内容
        size_t nameLen = name.length();
        os.write(reinterpret_cast<const char*>(&nameLen), sizeof(nameLen));
        os.write(name.c_str(), nameLen);

        // 保存数组：先保存元素个数，再保存所有元素
        size_t gradeCount = grades.size();
        os.write(reinterpret_cast<const char*>(&gradeCount), sizeof(gradeCount));
        os.write(reinterpret_cast<const char*>(grades.data()),
                 gradeCount * sizeof(double));
    }

    // 反序列化：从字节流恢复对象
    void LoadFrom(std::istream& is) {
        is.read(reinterpret_cast<char*>(&id), sizeof(id));

        // 读取字符串
        size_t nameLen;
        is.read(reinterpret_cast<char*>(&nameLen), sizeof(nameLen));
        name.resize(nameLen);
        is.read(&name[0], nameLen);

        // 读取数组
        size_t gradeCount;
        is.read(reinterpret_cast<char*>(&gradeCount), sizeof(gradeCount));
        grades.resize(gradeCount);
        is.read(reinterpret_cast<char*>(grades.data()),
                gradeCount * sizeof(double));
    }
};
```

#### 序列化过程的可视化

```mermaid
graph LR
    subgraph "序列化 (打包)"
        A1["🎒 复杂对象<br/>id: 1001<br/>name: '张三'<br/>grades: [85, 90, 88]"]
        A2["📦 按顺序打包"]
        A3["💾 二进制数据<br/>[4字节:id][2字节:长度][6字节:张三][3字节:数量][24字节:成绩]"]
    end

    subgraph "反序列化 (拆包)"
        B1["💾 二进制数据<br/>[4字节:id][2字节:长度][6字节:张三][3字节:数量][24字节:成绩]"]
        B2["📦 按顺序拆包"]
        B3["🎒 复杂对象<br/>id: 1001<br/>name: '张三'<br/>grades: [85, 90, 88]"]
    end

    A1 --> A2
    A2 --> A3
    A3 -.->|"保存到文件"| B1
    B1 --> B2
    B2 --> B3

    classDef object fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef process fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef binary fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A1,B3 object
    class A2,B2 process
    class A3,B1 binary
```

**关键理解**：序列化就像打包行李，要记住每样东西放在哪里，这样才能正确地拆包。

**序列化的核心原则**：
1. **顺序一致**：打包和拆包的顺序必须完全一致
2. **长度记录**：动态数据（如字符串、数组）要先记录长度
3. **类型安全**：确保读取时的数据类型与写入时一致

#### 3.1.1 基本数据类型的二进制读写

```cpp
#include <fstream>
#include <iostream>
#include <vector>
#include <cstring>

class BinaryFileBasics {
public:
    // 写入基本数据类型
    static void WritePrimitiveTypes(const std::string& filename) {
        std::cout << "=== 写入基本数据类型到二进制文件 ===" << std::endl;

        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "❌ 无法创建二进制文件: " << filename << std::endl;
            return;
        }

        // 写入不同类型的数据
        int intValue = 42;
        double doubleValue = 3.14159;
        char charValue = 'A';
        bool boolValue = true;

        file.write(reinterpret_cast<const char*>(&intValue), sizeof(intValue));
        file.write(reinterpret_cast<const char*>(&doubleValue), sizeof(doubleValue));
        file.write(reinterpret_cast<const char*>(&charValue), sizeof(charValue));
        file.write(reinterpret_cast<const char*>(&boolValue), sizeof(boolValue));

        std::cout << "✅ 写入数据:" << std::endl;
        std::cout << "  int: " << intValue << " (大小: " << sizeof(intValue) << " 字节)" << std::endl;
        std::cout << "  double: " << doubleValue << " (大小: " << sizeof(doubleValue) << " 字节)" << std::endl;
        std::cout << "  char: " << charValue << " (大小: " << sizeof(charValue) << " 字节)" << std::endl;
        std::cout << "  bool: " << boolValue << " (大小: " << sizeof(boolValue) << " 字节)" << std::endl;
    }

    // 读取基本数据类型
    static void ReadPrimitiveTypes(const std::string& filename) {
        std::cout << "\n=== 从二进制文件读取基本数据类型 ===" << std::endl;

        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "❌ 无法打开二进制文件: " << filename << std::endl;
            return;
        }

        int intValue;
        double doubleValue;
        char charValue;
        bool boolValue;

        file.read(reinterpret_cast<char*>(&intValue), sizeof(intValue));
        file.read(reinterpret_cast<char*>(&doubleValue), sizeof(doubleValue));
        file.read(reinterpret_cast<char*>(&charValue), sizeof(charValue));
        file.read(reinterpret_cast<char*>(&boolValue), sizeof(boolValue));

        // 检查读取是否成功
        if (file.gcount() == sizeof(boolValue)) {
            std::cout << "✅ 读取数据:" << std::endl;
            std::cout << "  int: " << intValue << std::endl;
            std::cout << "  double: " << doubleValue << std::endl;
            std::cout << "  char: " << charValue << std::endl;
            std::cout << "  bool: " << std::boolalpha << boolValue << std::endl;
        } else {
            std::cout << "❌ 数据读取不完整" << std::endl;
        }
    }

    // 写入数组数据
    static void WriteArrayData(const std::string& filename) {
        std::cout << "\n=== 写入数组数据到二进制文件 ===" << std::endl;

        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "❌ 无法创建二进制文件: " << filename << std::endl;
            return;
        }

        // 整数数组
        std::vector<int> intArray = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};

        // 先写入数组大小
        size_t arraySize = intArray.size();
        file.write(reinterpret_cast<const char*>(&arraySize), sizeof(arraySize));

        // 再写入数组数据
        file.write(reinterpret_cast<const char*>(intArray.data()),
                   arraySize * sizeof(int));

        // 浮点数组
        std::vector<double> doubleArray = {1.1, 2.2, 3.3, 4.4, 5.5};
        arraySize = doubleArray.size();
        file.write(reinterpret_cast<const char*>(&arraySize), sizeof(arraySize));
        file.write(reinterpret_cast<const char*>(doubleArray.data()),
                   arraySize * sizeof(double));

        std::cout << "✅ 写入整数数组: " << intArray.size() << " 个元素" << std::endl;
        std::cout << "✅ 写入浮点数组: " << doubleArray.size() << " 个元素" << std::endl;
    }

    // 读取数组数据
    static void ReadArrayData(const std::string& filename) {
        std::cout << "\n=== 从二进制文件读取数组数据 ===" << std::endl;

        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "❌ 无法打开二进制文件: " << filename << std::endl;
            return;
        }

        // 读取整数数组
        size_t intArraySize;
        file.read(reinterpret_cast<char*>(&intArraySize), sizeof(intArraySize));

        std::vector<int> intArray(intArraySize);
        file.read(reinterpret_cast<char*>(intArray.data()),
                  intArraySize * sizeof(int));

        std::cout << "✅ 读取整数数组 (" << intArraySize << " 个元素): ";
        for (size_t i = 0; i < std::min(intArraySize, size_t(10)); ++i) {
            std::cout << intArray[i] << " ";
        }
        std::cout << std::endl;

        // 读取浮点数组
        size_t doubleArraySize;
        file.read(reinterpret_cast<char*>(&doubleArraySize), sizeof(doubleArraySize));

        std::vector<double> doubleArray(doubleArraySize);
        file.read(reinterpret_cast<char*>(doubleArray.data()),
                  doubleArraySize * sizeof(double));

        std::cout << "✅ 读取浮点数组 (" << doubleArraySize << " 个元素): ";
        for (size_t i = 0; i < doubleArraySize; ++i) {
            std::cout << std::fixed << std::setprecision(1) << doubleArray[i] << " ";
        }
        std::cout << std::endl;
    }

    // 字符串的二进制读写
    static void WriteStrings(const std::string& filename) {
        std::cout << "\n=== 写入字符串到二进制文件 ===" << std::endl;

        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "❌ 无法创建二进制文件: " << filename << std::endl;
            return;
        }

        std::vector<std::string> strings = {
            "Hello, World!",
            "C++ 二进制文件操作",
            "这是一个测试字符串",
            "Binary file operations"
        };

        // 写入字符串数量
        size_t stringCount = strings.size();
        file.write(reinterpret_cast<const char*>(&stringCount), sizeof(stringCount));

        // 写入每个字符串
        for (const auto& str : strings) {
            // 先写入字符串长度
            size_t length = str.length();
            file.write(reinterpret_cast<const char*>(&length), sizeof(length));

            // 再写入字符串内容
            file.write(str.c_str(), length);
        }

        std::cout << "✅ 写入 " << stringCount << " 个字符串" << std::endl;
    }

    // 读取字符串
    static void ReadStrings(const std::string& filename) {
        std::cout << "\n=== 从二进制文件读取字符串 ===" << std::endl;

        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "❌ 无法打开二进制文件: " << filename << std::endl;
            return;
        }

        // 读取字符串数量
        size_t stringCount;
        file.read(reinterpret_cast<char*>(&stringCount), sizeof(stringCount));

        std::cout << "✅ 准备读取 " << stringCount << " 个字符串:" << std::endl;

        // 读取每个字符串
        for (size_t i = 0; i < stringCount; ++i) {
            // 读取字符串长度
            size_t length;
            file.read(reinterpret_cast<char*>(&length), sizeof(length));

            // 读取字符串内容
            std::vector<char> buffer(length + 1);  // +1 for null terminator
            file.read(buffer.data(), length);
            buffer[length] = '\0';  // 添加字符串结束符

            std::string str(buffer.data());
            std::cout << "  字符串 " << (i + 1) << ": \"" << str << "\" (长度: " << length << ")" << std::endl;
        }
    }
};
```

#### 3.1.2 结构体和类的二进制序列化

```cpp
#include <fstream>
#include <iostream>
#include <vector>
#include <cstring>

// 简单的POD结构体（Plain Old Data）
struct SimpleStudent {
    int id;
    char name[32];
    double grade;
    bool isActive;

    SimpleStudent() = default;
    SimpleStudent(int id, const std::string& name, double grade, bool active)
        : id(id), grade(grade), isActive(active) {
        std::strncpy(this->name, name.c_str(), sizeof(this->name) - 1);
        this->name[sizeof(this->name) - 1] = '\0';
    }

    void Print() const {
        std::cout << "ID: " << id << ", 姓名: " << name
                  << ", 成绩: " << grade << ", 活跃: " << std::boolalpha << isActive << std::endl;
    }
};

// 复杂结构体（包含动态数据）
class ComplexStudent {
public:
    int id;
    std::string name;
    std::vector<double> grades;
    bool isActive;

    ComplexStudent() = default;
    ComplexStudent(int id, const std::string& name, const std::vector<double>& grades, bool active)
        : id(id), name(name), grades(grades), isActive(active) {}

    // 序列化到二进制流
    void Serialize(std::ostream& os) const {
        // 写入基本类型
        os.write(reinterpret_cast<const char*>(&id), sizeof(id));
        os.write(reinterpret_cast<const char*>(&isActive), sizeof(isActive));

        // 写入字符串
        size_t nameLength = name.length();
        os.write(reinterpret_cast<const char*>(&nameLength), sizeof(nameLength));
        os.write(name.c_str(), nameLength);

        // 写入vector
        size_t gradesCount = grades.size();
        os.write(reinterpret_cast<const char*>(&gradesCount), sizeof(gradesCount));
        if (gradesCount > 0) {
            os.write(reinterpret_cast<const char*>(grades.data()),
                     gradesCount * sizeof(double));
        }
    }

    // 从二进制流反序列化
    void Deserialize(std::istream& is) {
        // 读取基本类型
        is.read(reinterpret_cast<char*>(&id), sizeof(id));
        is.read(reinterpret_cast<char*>(&isActive), sizeof(isActive));

        // 读取字符串
        size_t nameLength;
        is.read(reinterpret_cast<char*>(&nameLength), sizeof(nameLength));
        std::vector<char> nameBuffer(nameLength + 1);
        is.read(nameBuffer.data(), nameLength);
        nameBuffer[nameLength] = '\0';
        name = std::string(nameBuffer.data());

        // 读取vector
        size_t gradesCount;
        is.read(reinterpret_cast<char*>(&gradesCount), sizeof(gradesCount));
        grades.resize(gradesCount);
        if (gradesCount > 0) {
            is.read(reinterpret_cast<char*>(grades.data()),
                    gradesCount * sizeof(double));
        }
    }

    void Print() const {
        std::cout << "ID: " << id << ", 姓名: " << name << ", 活跃: " << std::boolalpha << isActive;
        std::cout << ", 成绩: [";
        for (size_t i = 0; i < grades.size(); ++i) {
            std::cout << grades[i];
            if (i < grades.size() - 1) std::cout << ", ";
        }
        std::cout << "]" << std::endl;
    }
};

class StructBinaryOperations {
public:
    // POD结构体的简单序列化
    static void WritePODStructs(const std::string& filename) {
        std::cout << "=== 写入POD结构体到二进制文件 ===" << std::endl;

        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "❌ 无法创建二进制文件: " << filename << std::endl;
            return;
        }

        std::vector<SimpleStudent> students = {
            SimpleStudent(1001, "张三", 85.5, true),
            SimpleStudent(1002, "李四", 92.0, true),
            SimpleStudent(1003, "王五", 78.5, false),
            SimpleStudent(1004, "赵六", 88.0, true)
        };

        // 写入学生数量
        size_t count = students.size();
        file.write(reinterpret_cast<const char*>(&count), sizeof(count));

        // 写入所有学生数据
        file.write(reinterpret_cast<const char*>(students.data()),
                   count * sizeof(SimpleStudent));

        std::cout << "✅ 写入 " << count << " 个学生记录" << std::endl;
    }

    // 读取POD结构体
    static void ReadPODStructs(const std::string& filename) {
        std::cout << "\n=== 从二进制文件读取POD结构体 ===" << std::endl;

        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "❌ 无法打开二进制文件: " << filename << std::endl;
            return;
        }

        // 读取学生数量
        size_t count;
        file.read(reinterpret_cast<char*>(&count), sizeof(count));

        // 读取所有学生数据
        std::vector<SimpleStudent> students(count);
        file.read(reinterpret_cast<char*>(students.data()),
                  count * sizeof(SimpleStudent));

        std::cout << "✅ 读取 " << count << " 个学生记录:" << std::endl;
        for (const auto& student : students) {
            student.Print();
        }
    }

    // 复杂结构体的序列化
    static void WriteComplexStructs(const std::string& filename) {
        std::cout << "\n=== 写入复杂结构体到二进制文件 ===" << std::endl;

        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "❌ 无法创建二进制文件: " << filename << std::endl;
            return;
        }

        std::vector<ComplexStudent> students = {
            ComplexStudent(2001, "张三丰", {85.5, 90.0, 88.5}, true),
            ComplexStudent(2002, "李小龙", {92.0, 95.5, 89.0, 91.5}, true),
            ComplexStudent(2003, "王重阳", {78.5, 82.0}, false),
            ComplexStudent(2004, "赵敏敏", {88.0, 87.5, 90.0}, true)
        };

        // 写入学生数量
        size_t count = students.size();
        file.write(reinterpret_cast<const char*>(&count), sizeof(count));

        // 序列化每个学生
        for (const auto& student : students) {
            student.Serialize(file);
        }

        std::cout << "✅ 序列化 " << count << " 个复杂学生记录" << std::endl;
    }

    // 读取复杂结构体
    static void ReadComplexStructs(const std::string& filename) {
        std::cout << "\n=== 从二进制文件读取复杂结构体 ===" << std::endl;

        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "❌ 无法打开二进制文件: " << filename << std::endl;
            return;
        }

        // 读取学生数量
        size_t count;
        file.read(reinterpret_cast<char*>(&count), sizeof(count));

        std::cout << "✅ 准备反序列化 " << count << " 个复杂学生记录:" << std::endl;

        // 反序列化每个学生
        for (size_t i = 0; i < count; ++i) {
            ComplexStudent student;
            student.Deserialize(file);
            student.Print();
        }
    }
};
```

### 3.2 文件定位操作：精确控制读写位置

文件定位是高级文件操作的核心，允许我们在文件中任意位置进行读写操作。

```cpp
#include <fstream>
#include <iostream>

class FilePositioning {
public:
    // 演示文件定位的基本操作
    static void DemonstrateBasicPositioning() {
        std::cout << "=== 文件定位基本操作演示 ===" << std::endl;

        const std::string filename = "positioning_test.txt";

        // 创建测试文件
        CreateTestFile(filename);

        // 演示读取定位
        DemonstrateReadPositioning(filename);

        // 演示写入定位
        DemonstrateWritePositioning(filename);

        // 演示随机访问
        DemonstrateRandomAccess(filename);
    }

private:
    static void CreateTestFile(const std::string& filename) {
        std::ofstream file(filename);
        file << "0123456789" << std::endl;  // 第1行：10个数字 + 换行符
        file << "ABCDEFGHIJ" << std::endl;  // 第2行：10个字母 + 换行符
        file << "abcdefghij" << std::endl;  // 第3行：10个小写字母 + 换行符
        std::cout << "✅ 创建测试文件: " << filename << std::endl;
    }

    static void DemonstrateReadPositioning(const std::string& filename) {
        std::cout << "\n--- 读取定位操作 ---" << std::endl;

        std::ifstream file(filename);
        if (!file.is_open()) return;

        // 获取当前位置
        std::cout << "初始位置: " << file.tellg() << std::endl;

        // 读取5个字符
        char buffer[6] = {0};
        file.read(buffer, 5);
        std::cout << "读取5个字符: \"" << buffer << "\"" << std::endl;
        std::cout << "当前位置: " << file.tellg() << std::endl;

        // 移动到文件开头
        file.seekg(0, std::ios::beg);
        std::cout << "移动到开头后位置: " << file.tellg() << std::endl;

        // 移动到文件末尾
        file.seekg(0, std::ios::end);
        std::cout << "移动到末尾后位置: " << file.tellg() << std::endl;

        // 从当前位置向前移动10个字符
        file.seekg(-10, std::ios::cur);
        std::cout << "向前移动10个字符后位置: " << file.tellg() << std::endl;

        // 读取剩余内容
        file.read(buffer, 5);
        buffer[5] = '\0';
        std::cout << "读取内容: \"" << buffer << "\"" << std::endl;
    }

    static void DemonstrateWritePositioning(const std::string& filename) {
        std::cout << "\n--- 写入定位操作 ---" << std::endl;

        std::fstream file(filename, std::ios::in | std::ios::out);
        if (!file.is_open()) return;

        // 移动到第二行开头（跳过第一行的11个字符：10个数字+换行符）
        file.seekp(11, std::ios::beg);
        std::cout << "写入位置: " << file.tellp() << std::endl;

        // 替换部分内容
        file.write("XYZ", 3);
        std::cout << "写入\"XYZ\"后位置: " << file.tellp() << std::endl;

        // 验证修改结果
        file.seekg(0, std::ios::beg);
        std::string line;
        int lineNum = 1;
        while (std::getline(file, line)) {
            std::cout << "第" << lineNum++ << "行: " << line << std::endl;
        }
    }

    static void DemonstrateRandomAccess(const std::string& filename) {
        std::cout << "\n--- 随机访问演示 ---" << std::endl;

        std::fstream file(filename, std::ios::in | std::ios::out);
        if (!file.is_open()) return;

        // 在多个位置进行读写操作
        std::vector<std::pair<int, char>> positions = {
            {0, '['}, {10, ']'}, {22, '('}, {32, ')'}
        };

        for (const auto& pos : positions) {
            file.seekp(pos.first, std::ios::beg);
            file.put(pos.second);
            std::cout << "在位置 " << pos.first << " 写入字符 '" << pos.second << "'" << std::endl;
        }

        // 显示最终结果
        file.seekg(0, std::ios::beg);
        std::string line;
        int lineNum = 1;
        std::cout << "随机访问修改后的文件内容:" << std::endl;
        while (std::getline(file, line)) {
            std::cout << "第" << lineNum++ << "行: " << line << std::endl;
        }
    }

public:
    // 二进制文件的随机访问
    static void BinaryRandomAccess() {
        std::cout << "\n=== 二进制文件随机访问 ===" << std::endl;

        const std::string filename = "binary_random.dat";

        // 创建包含100个整数的二进制文件
        {
            std::ofstream file(filename, std::ios::binary);
            for (int i = 0; i < 100; ++i) {
                file.write(reinterpret_cast<const char*>(&i), sizeof(i));
            }
            std::cout << "✅ 创建包含100个整数的二进制文件" << std::endl;
        }

        // 随机访问读取特定位置的数据
        {
            std::ifstream file(filename, std::ios::binary);

            // 读取第10个整数（索引9，位置 9 * sizeof(int)）
            file.seekg(9 * sizeof(int), std::ios::beg);
            int value;
            file.read(reinterpret_cast<char*>(&value), sizeof(value));
            std::cout << "第10个整数: " << value << std::endl;

            // 读取第50个整数
            file.seekg(49 * sizeof(int), std::ios::beg);
            file.read(reinterpret_cast<char*>(&value), sizeof(value));
            std::cout << "第50个整数: " << value << std::endl;

            // 读取最后一个整数
            file.seekg(-sizeof(int), std::ios::end);
            file.read(reinterpret_cast<char*>(&value), sizeof(value));
            std::cout << "最后一个整数: " << value << std::endl;
        }

        // 修改特定位置的数据
        {
            std::fstream file(filename, std::ios::in | std::ios::out | std::ios::binary);

            // 将第25个整数改为999
            int newValue = 999;
            file.seekp(24 * sizeof(int), std::ios::beg);
            file.write(reinterpret_cast<const char*>(&newValue), sizeof(newValue));

            // 验证修改
            file.seekg(24 * sizeof(int), std::ios::beg);
            int readValue;
            file.read(reinterpret_cast<char*>(&readValue), sizeof(readValue));
            std::cout << "修改后第25个整数: " << readValue << std::endl;
        }
    }
};
```

### 3.3 实际应用示例：学生管理系统

让我们通过一个完整的学生管理系统来演示文件操作的实际应用：

```cpp
#include <fstream>
#include <iostream>
#include <vector>
#include <algorithm>
#include <iomanip>

class StudentRecord {
public:
    int id;
    char name[32];
    int age;
    double gpa;
    bool isActive;

    StudentRecord() : id(0), age(0), gpa(0.0), isActive(false) {
        std::memset(name, 0, sizeof(name));
    }

    StudentRecord(int id, const std::string& name, int age, double gpa, bool active = true)
        : id(id), age(age), gpa(gpa), isActive(active) {
        std::strncpy(this->name, name.c_str(), sizeof(this->name) - 1);
        this->name[sizeof(this->name) - 1] = '\0';
    }

    void Print() const {
        std::cout << std::setw(6) << id
                  << std::setw(20) << name
                  << std::setw(6) << age
                  << std::setw(8) << std::fixed << std::setprecision(2) << gpa
                  << std::setw(10) << (isActive ? "活跃" : "非活跃") << std::endl;
    }

    static void PrintHeader() {
        std::cout << std::setw(6) << "ID"
                  << std::setw(20) << "姓名"
                  << std::setw(6) << "年龄"
                  << std::setw(8) << "GPA"
                  << std::setw(10) << "状态" << std::endl;
        std::cout << std::string(50, '-') << std::endl;
    }
};

class StudentManagementSystem {
private:
    std::string filename_;

public:
    explicit StudentManagementSystem(const std::string& filename) : filename_(filename) {}

    // 添加学生记录
    bool AddStudent(const StudentRecord& student) {
        std::ofstream file(filename_, std::ios::binary | std::ios::app);
        if (!file.is_open()) {
            std::cerr << "❌ 无法打开文件进行写入" << std::endl;
            return false;
        }

        file.write(reinterpret_cast<const char*>(&student), sizeof(StudentRecord));
        std::cout << "✅ 成功添加学生: " << student.name << std::endl;
        return true;
    }

    // 读取所有学生记录
    std::vector<StudentRecord> ReadAllStudents() {
        std::vector<StudentRecord> students;
        std::ifstream file(filename_, std::ios::binary);

        if (!file.is_open()) {
            std::cout << "📝 文件不存在，返回空列表" << std::endl;
            return students;
        }

        StudentRecord student;
        while (file.read(reinterpret_cast<char*>(&student), sizeof(StudentRecord))) {
            students.push_back(student);
        }

        std::cout << "✅ 读取了 " << students.size() << " 条学生记录" << std::endl;
        return students;
    }

    // 按ID查找学生
    bool FindStudentById(int id, StudentRecord& student) {
        std::ifstream file(filename_, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }

        StudentRecord temp;
        while (file.read(reinterpret_cast<char*>(&temp), sizeof(StudentRecord))) {
            if (temp.id == id) {
                student = temp;
                return true;
            }
        }

        return false;
    }

    // 更新学生记录
    bool UpdateStudent(int id, const StudentRecord& newData) {
        std::fstream file(filename_, std::ios::in | std::ios::out | std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "❌ 无法打开文件进行更新" << std::endl;
            return false;
        }

        StudentRecord temp;
        std::streampos position = 0;

        while (file.read(reinterpret_cast<char*>(&temp), sizeof(StudentRecord))) {
            if (temp.id == id) {
                // 移动到记录开始位置
                file.seekp(position, std::ios::beg);
                file.write(reinterpret_cast<const char*>(&newData), sizeof(StudentRecord));
                std::cout << "✅ 成功更新学生ID: " << id << std::endl;
                return true;
            }
            position = file.tellg();
        }

        std::cout << "❌ 未找到ID为 " << id << " 的学生" << std::endl;
        return false;
    }

    // 删除学生记录（标记为非活跃）
    bool DeactivateStudent(int id) {
        StudentRecord student;
        if (FindStudentById(id, student)) {
            student.isActive = false;
            return UpdateStudent(id, student);
        }
        return false;
    }

    // 显示所有学生
    void DisplayAllStudents() {
        auto students = ReadAllStudents();
        if (students.empty()) {
            std::cout << "📝 没有学生记录" << std::endl;
            return;
        }

        std::cout << "\n=== 所有学生记录 ===" << std::endl;
        StudentRecord::PrintHeader();
        for (const auto& student : students) {
            student.Print();
        }
    }

    // 显示活跃学生
    void DisplayActiveStudents() {
        auto students = ReadAllStudents();

        std::cout << "\n=== 活跃学生记录 ===" << std::endl;
        StudentRecord::PrintHeader();

        int count = 0;
        for (const auto& student : students) {
            if (student.isActive) {
                student.Print();
                count++;
            }
        }

        if (count == 0) {
            std::cout << "📝 没有活跃的学生记录" << std::endl;
        }
    }

    // 按GPA排序显示
    void DisplayStudentsByGPA() {
        auto students = ReadAllStudents();
        if (students.empty()) {
            std::cout << "📝 没有学生记录" << std::endl;
            return;
        }

        // 按GPA降序排序
        std::sort(students.begin(), students.end(),
                  [](const StudentRecord& a, const StudentRecord& b) {
                      return a.gpa > b.gpa;
                  });

        std::cout << "\n=== 按GPA排序的学生记录 ===" << std::endl;
        StudentRecord::PrintHeader();
        for (const auto& student : students) {
            if (student.isActive) {
                student.Print();
            }
        }
    }

    // 获取文件统计信息
    void ShowFileStatistics() {
        std::ifstream file(filename_, std::ios::binary | std::ios::ate);
        if (!file.is_open()) {
            std::cout << "📝 文件不存在" << std::endl;
            return;
        }

        auto fileSize = file.tellg();
        auto recordCount = fileSize / sizeof(StudentRecord);

        std::cout << "\n=== 文件统计信息 ===" << std::endl;
        std::cout << "文件大小: " << fileSize << " 字节" << std::endl;
        std::cout << "记录大小: " << sizeof(StudentRecord) << " 字节" << std::endl;
        std::cout << "记录数量: " << recordCount << std::endl;

        // 统计活跃和非活跃学生
        auto students = ReadAllStudents();
        int activeCount = 0, inactiveCount = 0;
        for (const auto& student : students) {
            if (student.isActive) {
                activeCount++;
            } else {
                inactiveCount++;
            }
        }

        std::cout << "活跃学生: " << activeCount << std::endl;
        std::cout << "非活跃学生: " << inactiveCount << std::endl;
    }
};

// 演示学生管理系统的使用
void DemonstrateStudentSystem() {
    std::cout << "=== 学生管理系统演示 ===" << std::endl;

    StudentManagementSystem sms("students.dat");

    // 添加一些学生记录
    sms.AddStudent(StudentRecord(1001, "张三", 20, 3.8));
    sms.AddStudent(StudentRecord(1002, "李四", 21, 3.6));
    sms.AddStudent(StudentRecord(1003, "王五", 19, 3.9));
    sms.AddStudent(StudentRecord(1004, "赵六", 22, 3.4));
    sms.AddStudent(StudentRecord(1005, "钱七", 20, 3.7));

    // 显示所有学生
    sms.DisplayAllStudents();

    // 更新学生信息
    sms.UpdateStudent(1003, StudentRecord(1003, "王五五", 19, 4.0));

    // 停用一个学生
    sms.DeactivateStudent(1004);

    // 显示活跃学生
    sms.DisplayActiveStudents();

    // 按GPA排序显示
    sms.DisplayStudentsByGPA();

    // 显示文件统计
    sms.ShowFileStatistics();
}
```

---

## Part 4: 文件流系统的工程级应用

### 4.1 文件流的底层机制：从系统调用到C++抽象

文件流是对操作系统文件API的高级封装，理解其底层机制对于编写高性能IO代码至关重要：

```cpp
#include <fstream>
#include <iostream>
#include <chrono>
#include <vector>
#include <memory>

class FileStreamInternals {
public:
    // 文件打开模式的技术分析
    static void AnalyzeOpenModes() {
        std::cout << "=== 文件打开模式技术分析 ===" << std::endl;

        // 基本模式组合
        struct ModeInfo {
            std::ios::openmode mode;
            std::string description;
            std::string technical_note;
        };

        std::vector<ModeInfo> modes = {
            {std::ios::in, "只读模式", "对应系统调用 open(O_RDONLY)"},
            {std::ios::out, "只写模式", "对应系统调用 open(O_WRONLY | O_CREAT | O_TRUNC)"},
            {std::ios::app, "追加模式", "对应系统调用 open(O_WRONLY | O_CREAT | O_APPEND)"},
            {std::ios::ate, "打开时定位到末尾", "打开后立即 seek 到文件末尾"},
            {std::ios::trunc, "截断模式", "清空现有文件内容"},
            {std::ios::binary, "二进制模式", "禁用文本模式的换行符转换"}
        };

        for (const auto& mode_info : modes) {
            std::cout << "模式: " << mode_info.description
                      << " | 技术细节: " << mode_info.technical_note << std::endl;
        }

        // 复合模式示例
        std::cout << "\n=== 复合模式应用 ===" << std::endl;
        DemonstrateCompoundModes();
    }

private:
    static void DemonstrateCompoundModes() {
        const std::string filename = "compound_mode_test.txt";

        // 模式1: 读写模式，文件必须存在
        {
            std::fstream file(filename, std::ios::in | std::ios::out);
            if (!file) {
                std::cout << "读写模式打开失败（文件不存在）" << std::endl;
            }
        }

        // 模式2: 读写模式，不存在则创建
        {
            std::fstream file(filename, std::ios::in | std::ios::out | std::ios::trunc);
            if (file) {
                file << "测试数据" << std::endl;
                file.seekg(0);  // 回到开头
                std::string content;
                std::getline(file, content);
                std::cout << "读写模式成功，读取内容: " << content << std::endl;
            }
        }

        // 模式3: 二进制读写模式
        {
            std::fstream file(filename, std::ios::in | std::ios::out | std::ios::binary);
            if (file) {
                // 写入二进制数据
                int binary_data[] = {0x12345678, 0xABCDEF00};
                file.write(reinterpret_cast<const char*>(binary_data), sizeof(binary_data));

                // 读取二进制数据
                file.seekg(0);
                int read_data[2];
                file.read(reinterpret_cast<char*>(read_data), sizeof(read_data));

                std::cout << "二进制数据读写成功: "
                          << std::hex << read_data[0] << ", " << read_data[1] << std::dec << std::endl;
            }
        }
    }
};
```

### 4.2 高性能文件IO：缓冲策略与内存映射

现代文件IO的性能优化涉及多个层面，从缓冲区管理到操作系统级别的内存映射：

```cpp
#include <fstream>
#include <vector>
#include <chrono>
#include <memory>
#include <algorithm>

class HighPerformanceFileIO {
public:
    // 自定义缓冲区大小的性能测试
    static void BufferSizePerformanceTest() {
        std::cout << "=== 缓冲区大小性能测试 ===" << std::endl;

        const std::string filename = "perf_test_large.dat";
        const size_t data_size = 10 * 1024 * 1024;  // 10MB
        std::vector<char> test_data(data_size, 'A');

        // 测试不同缓冲区大小
        std::vector<size_t> buffer_sizes = {1024, 4096, 16384, 65536, 262144};

        for (size_t buffer_size : buffer_sizes) {
            auto start = std::chrono::high_resolution_clock::now();

            {
                std::ofstream file(filename, std::ios::binary);

                // 设置自定义缓冲区
                std::vector<char> buffer(buffer_size);
                file.rdbuf()->pubsetbuf(buffer.data(), buffer_size);

                // 写入数据
                file.write(test_data.data(), test_data.size());
            }

            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

            std::cout << "缓冲区大小: " << buffer_size << " bytes, "
                      << "写入时间: " << duration.count() << " ms" << std::endl;
        }
    }

    // RAII文件管理器
    class FileManager {
    private:
        std::unique_ptr<std::fstream> file_;
        std::string filename_;

    public:
        explicit FileManager(const std::string& filename, std::ios::openmode mode)
            : filename_(filename), file_(std::make_unique<std::fstream>(filename, mode)) {

            if (!file_->is_open()) {
                throw std::runtime_error("无法打开文件: " + filename);
            }

            std::cout << "文件已打开: " << filename_ << std::endl;
        }

        ~FileManager() {
            if (file_ && file_->is_open()) {
                file_->close();
                std::cout << "文件已关闭: " << filename_ << std::endl;
            }
        }

        // 禁用拷贝，启用移动
        FileManager(const FileManager&) = delete;
        FileManager& operator=(const FileManager&) = delete;
        FileManager(FileManager&&) = default;
        FileManager& operator=(FileManager&&) = default;

        std::fstream& GetStream() { return *file_; }
        const std::fstream& GetStream() const { return *file_; }

        // 异常安全的批量写入
        template<typename Container>
        void SafeBatchWrite(const Container& data) {
            try {
                for (const auto& item : data) {
                    *file_ << item << "\n";
                    if (file_->fail()) {
                        throw std::runtime_error("写入失败");
                    }
                }
                file_->flush();  // 确保数据写入磁盘
            } catch (const std::exception& e) {
                file_->clear();  // 清除错误状态
                throw;  // 重新抛出异常
            }
        }
    };

    // 内存映射文件读取（概念演示）
    static void MemoryMappedFileDemo() {
        std::cout << "\n=== 内存映射文件概念演示 ===" << std::endl;

        // 注意：这里只是概念演示，实际的内存映射需要平台特定的API
        // 在Windows上使用CreateFileMapping/MapViewOfFile
        // 在Linux上使用mmap系统调用

        const std::string filename = "mmap_test.txt";

        // 创建测试文件
        {
            std::ofstream file(filename);
            for (int i = 0; i < 1000; ++i) {
                file << "Line " << i << ": This is test data for memory mapping.\n";
            }
        }

        // 传统文件读取
        auto start = std::chrono::high_resolution_clock::now();
        {
            std::ifstream file(filename);
            std::string line;
            int line_count = 0;
            while (std::getline(file, line)) {
                ++line_count;
            }
            std::cout << "传统方式读取行数: " << line_count << std::endl;
        }
        auto end = std::chrono::high_resolution_clock::now();
        auto traditional_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);

        std::cout << "传统文件读取时间: " << traditional_time.count() << " μs" << std::endl;
        std::cout << "注意: 实际的内存映射实现需要使用操作系统特定的API" << std::endl;
    }
};
```

### 4.3 二进制文件处理：结构化数据的序列化

二进制文件操作是系统级编程的核心技能，涉及数据布局、字节序和跨平台兼容性：

```cpp
#include <fstream>
#include <vector>
#include <cstring>
#include <type_traits>
#include <chrono>

class BinaryFileProcessor {
public:
    // 通用二进制序列化模板
    template<typename T>
    static typename std::enable_if<std::is_trivially_copyable<T>::value, void>::type
    WriteBinary(std::ostream& os, const T& data) {
        os.write(reinterpret_cast<const char*>(&data), sizeof(T));
    }

    template<typename T>
    static typename std::enable_if<std::is_trivially_copyable<T>::value, bool>::type
    ReadBinary(std::istream& is, T& data) {
        is.read(reinterpret_cast<char*>(&data), sizeof(T));
        return is.gcount() == sizeof(T);
    }

    // 复杂数据结构的序列化
    struct FileHeader {
        char magic[4] = {'M', 'Y', 'F', 'T'};  // 文件魔数
        uint32_t version = 1;                   // 版本号
        uint64_t timestamp;                     // 时间戳
        uint32_t record_count;                  // 记录数量
        uint32_t checksum;                      // 校验和

        // 计算校验和
        uint32_t CalculateChecksum() const {
            uint32_t sum = 0;
            const uint8_t* data = reinterpret_cast<const uint8_t*>(this);
            for (size_t i = 0; i < offsetof(FileHeader, checksum); ++i) {
                sum += data[i];
            }
            return sum;
        }

        bool IsValid() const {
            return std::memcmp(magic, "MYFT", 4) == 0 &&
                   checksum == CalculateChecksum();
        }
    };

    struct DataRecord {
        uint32_t id;
        double value;
        char name[32];

        DataRecord() = default;
        DataRecord(uint32_t id, double val, const std::string& n)
            : id(id), value(val) {
            std::strncpy(name, n.c_str(), sizeof(name) - 1);
            name[sizeof(name) - 1] = '\0';
        }
    };

    // 结构化二进制文件写入
    static void WriteStructuredBinaryFile(const std::string& filename,
                                        const std::vector<DataRecord>& records) {
        std::ofstream file(filename, std::ios::binary);
        if (!file) {
            throw std::runtime_error("无法创建文件: " + filename);
        }

        // 准备文件头
        FileHeader header;
        header.timestamp = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        header.record_count = static_cast<uint32_t>(records.size());
        header.checksum = header.CalculateChecksum();

        // 写入文件头
        WriteBinary(file, header);

        // 写入记录
        for (const auto& record : records) {
            WriteBinary(file, record);
        }

        std::cout << "成功写入 " << records.size() << " 条记录到 " << filename << std::endl;
    }

    // 结构化二进制文件读取
    static std::vector<DataRecord> ReadStructuredBinaryFile(const std::string& filename) {
        std::ifstream file(filename, std::ios::binary);
        if (!file) {
            throw std::runtime_error("无法打开文件: " + filename);
        }

        // 读取文件头
        FileHeader header;
        if (!ReadBinary(file, header)) {
            throw std::runtime_error("文件头读取失败");
        }

        if (!header.IsValid()) {
            throw std::runtime_error("无效的文件格式");
        }

        std::cout << "文件版本: " << header.version
                  << ", 记录数量: " << header.record_count << std::endl;

        // 读取记录
        std::vector<DataRecord> records;
        records.reserve(header.record_count);

        for (uint32_t i = 0; i < header.record_count; ++i) {
            DataRecord record;
            if (!ReadBinary(file, record)) {
                throw std::runtime_error("记录读取失败，索引: " + std::to_string(i));
            }
            records.push_back(record);
        }

        return records;
    }

    // 字节序处理（大端/小端转换）
    template<typename T>
    static T SwapEndian(T value) {
        static_assert(std::is_integral<T>::value, "只支持整数类型");

        T result = 0;
        for (size_t i = 0; i < sizeof(T); ++i) {
            result = (result << 8) | ((value >> (i * 8)) & 0xFF);
        }
        return result;
    }

    static void EndianessDemo() {
        std::cout << "\n=== 字节序处理演示 ===" << std::endl;

        uint32_t value = 0x12345678;
        std::cout << "原始值: 0x" << std::hex << value << std::endl;
        std::cout << "字节序转换后: 0x" << std::hex << SwapEndian(value) << std::dec << std::endl;

        // 检测系统字节序
        uint16_t test = 0x1234;
        uint8_t* bytes = reinterpret_cast<uint8_t*>(&test);
        if (bytes[0] == 0x12) {
            std::cout << "当前系统: 大端字节序" << std::endl;
        } else {
            std::cout << "当前系统: 小端字节序" << std::endl;
        }
    }
};
```

---

## Part 5: 字符串流的高级应用

### 5.1 字符串流的内存管理机制

字符串流 (stringstream) 提供了内存中的流操作，是数据转换和格式化的强大工具：

```cpp
#include <sstream>
#include <iostream>
#include <vector>
#include <iomanip>
#include <memory>

class StringStreamMastery {
public:
    // 字符串流的内存分配策略分析
    static void AnalyzeMemoryAllocation() {
        std::cout << "=== 字符串流内存分配分析 ===" << std::endl;

        std::ostringstream oss;

        // 观察缓冲区增长
        for (int i = 0; i < 10; ++i) {
            size_t old_size = oss.str().size();
            oss << "Data chunk " << i << " with some additional content. ";
            size_t new_size = oss.str().size();

            std::cout << "迭代 " << i << ": 大小从 " << old_size
                      << " 增长到 " << new_size << " (+=" << (new_size - old_size) << ")" << std::endl;
        }

        // 预分配策略
        std::cout << "\n=== 预分配策略对比 ===" << std::endl;

        // 方法1: 不预分配
        auto start = std::chrono::high_resolution_clock::now();
        {
            std::ostringstream oss_no_reserve;
            for (int i = 0; i < 10000; ++i) {
                oss_no_reserve << "Item " << i << " ";
            }
            volatile auto result = oss_no_reserve.str();  // 防止优化
        }
        auto end = std::chrono::high_resolution_clock::now();
        auto time_no_reserve = std::chrono::duration_cast<std::chrono::microseconds>(end - start);

        // 方法2: 预分配
        start = std::chrono::high_resolution_clock::now();
        {
            std::string buffer;
            buffer.reserve(100000);  // 预分配足够空间
            std::ostringstream oss_reserved;
            oss_reserved.str(std::move(buffer));

            for (int i = 0; i < 10000; ++i) {
                oss_reserved << "Item " << i << " ";
            }
            volatile auto result = oss_reserved.str();
        }
        end = std::chrono::high_resolution_clock::now();
        auto time_reserved = std::chrono::duration_cast<std::chrono::microseconds>(end - start);

        std::cout << "不预分配时间: " << time_no_reserve.count() << " μs" << std::endl;
        std::cout << "预分配时间: " << time_reserved.count() << " μs" << std::endl;
        std::cout << "性能提升: " << (double)time_no_reserve.count() / time_reserved.count() << "x" << std::endl;
    }

    // 高效的数据转换工具
    template<typename T>
    static std::string ToString(const T& value) {
        std::ostringstream oss;
        oss << value;
        return oss.str();
    }

    template<typename T>
    static bool FromString(const std::string& str, T& value) {
        std::istringstream iss(str);
        return (iss >> value) && iss.eof();
    }

    // 批量数据转换
    template<typename Container>
    static std::string JoinToString(const Container& container, const std::string& delimiter = ", ") {
        std::ostringstream oss;
        auto it = container.begin();
        if (it != container.end()) {
            oss << *it;
            ++it;
        }

        for (; it != container.end(); ++it) {
            oss << delimiter << *it;
        }

        return oss.str();
    }

    static std::vector<std::string> SplitString(const std::string& str, char delimiter = ' ') {
        std::vector<std::string> tokens;
        std::istringstream iss(str);
        std::string token;

        while (std::getline(iss, token, delimiter)) {
            if (!token.empty()) {  // 跳过空字符串
                tokens.push_back(token);
            }
        }

        return tokens;
    }

    // 复杂数据结构的序列化
    struct Person {
        std::string name;
        int age;
        double salary;

        // 序列化到字符串
        std::string Serialize() const {
            std::ostringstream oss;
            oss << name << "|" << age << "|" << std::fixed << std::setprecision(2) << salary;
            return oss.str();
        }

        // 从字符串反序列化
        static Person Deserialize(const std::string& data) {
            auto parts = SplitString(data, '|');
            if (parts.size() != 3) {
                throw std::invalid_argument("无效的序列化数据");
            }

            Person person;
            person.name = parts[0];

            if (!FromString(parts[1], person.age)) {
                throw std::invalid_argument("年龄解析失败");
            }

            if (!FromString(parts[2], person.salary)) {
                throw std::invalid_argument("薪资解析失败");
            }

            return person;
        }
    };

    static void DemonstrateAdvancedUsage() {
        std::cout << "\n=== 字符串流高级应用演示 ===" << std::endl;

        // 数据转换演示
        std::vector<int> numbers = {1, 2, 3, 4, 5};
        std::string joined = JoinToString(numbers, " -> ");
        std::cout << "数组连接: " << joined << std::endl;

        // 字符串分割演示
        std::string csv_data = "apple,banana,cherry,date";
        auto fruits = SplitString(csv_data, ',');
        std::cout << "CSV分割结果: ";
        for (const auto& fruit : fruits) {
            std::cout << "[" << fruit << "] ";
        }
        std::cout << std::endl;

        // 对象序列化演示
        Person person{"Alice", 30, 75000.50};
        std::string serialized = person.Serialize();
        std::cout << "序列化结果: " << serialized << std::endl;

        Person deserialized = Person::Deserialize(serialized);
        std::cout << "反序列化结果: " << deserialized.name
                  << ", " << deserialized.age
                  << ", $" << deserialized.salary << std::endl;
    }
};
```

### 5.2 自定义流缓冲区：扩展IO流系统

通过继承 `std::streambuf`，可以创建自定义的流缓冲区，实现特殊的IO行为：

```cpp
#include <streambuf>
#include <iostream>
#include <vector>
#include <algorithm>

// 自定义内存流缓冲区
class MemoryStreamBuf : public std::streambuf {
private:
    std::vector<char> buffer_;

public:
    explicit MemoryStreamBuf(size_t initial_size = 1024) : buffer_(initial_size) {
        // 设置输出缓冲区
        setp(buffer_.data(), buffer_.data() + buffer_.size());
        // 设置输入缓冲区
        setg(buffer_.data(), buffer_.data(), buffer_.data());
    }

    // 获取缓冲区内容
    std::string GetContent() const {
        return std::string(pbase(), pptr());
    }

    // 重置为输入模式
    void SwitchToInputMode() {
        setg(pbase(), pbase(), pptr());
    }

protected:
    // 输出缓冲区溢出时调用
    int_type overflow(int_type ch) override {
        if (ch != traits_type::eof()) {
            // 扩展缓冲区
            size_t old_size = buffer_.size();
            size_t new_size = old_size * 2;
            size_t current_pos = pptr() - pbase();

            buffer_.resize(new_size);

            // 重新设置指针
            setp(buffer_.data(), buffer_.data() + new_size);
            pbump(static_cast<int>(current_pos));

            // 写入字符
            *pptr() = static_cast<char>(ch);
            pbump(1);
        }
        return ch;
    }

    // 输入缓冲区下溢时调用
    int_type underflow() override {
        if (gptr() < egptr()) {
            return traits_type::to_int_type(*gptr());
        }
        return traits_type::eof();
    }
};

// 日志流缓冲区（带时间戳和级别）
class LogStreamBuf : public std::streambuf {
public:
    enum class LogLevel { DEBUG, INFO, WARNING, ERROR };

private:
    std::ostream& output_;
    LogLevel level_;
    std::string buffer_;

public:
    LogStreamBuf(std::ostream& output, LogLevel level)
        : output_(output), level_(level) {}

protected:
    int_type overflow(int_type ch) override {
        if (ch != traits_type::eof()) {
            buffer_ += static_cast<char>(ch);

            // 遇到换行符时输出完整的日志行
            if (ch == '\n') {
                FlushLogLine();
            }
        }
        return ch;
    }

    int sync() override {
        if (!buffer_.empty()) {
            FlushLogLine();
        }
        return 0;
    }

private:
    void FlushLogLine() {
        if (buffer_.empty()) return;

        // 获取当前时间
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);

        // 格式化时间戳
        output_ << "[" << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << "] ";

        // 输出日志级别
        switch (level_) {
            case LogLevel::DEBUG:   output_ << "[DEBUG] "; break;
            case LogLevel::INFO:    output_ << "[INFO]  "; break;
            case LogLevel::WARNING: output_ << "[WARN]  "; break;
            case LogLevel::ERROR:   output_ << "[ERROR] "; break;
        }

        // 输出日志内容
        output_ << buffer_;
        output_.flush();

        buffer_.clear();
    }
};

// 自定义流类
class LogStream : public std::ostream {
private:
    LogStreamBuf log_buf_;

public:
    LogStream(std::ostream& output, LogStreamBuf::LogLevel level)
        : std::ostream(&log_buf_), log_buf_(output, level) {}
};

class CustomStreamDemo {
public:
    static void DemonstrateMemoryStream() {
        std::cout << "=== 自定义内存流演示 ===" << std::endl;

        MemoryStreamBuf mem_buf;
        std::ostream mem_stream(&mem_buf);

        // 写入数据
        mem_stream << "Hello, " << "Custom Stream! " << 42 << std::endl;
        mem_stream << "This is line 2." << std::endl;

        std::cout << "内存流内容:\n" << mem_buf.GetContent() << std::endl;

        // 切换到输入模式
        mem_buf.SwitchToInputMode();
        std::istream input_stream(&mem_buf);

        std::string line;
        int line_num = 1;
        while (std::getline(input_stream, line)) {
            std::cout << "读取第" << line_num++ << "行: " << line << std::endl;
        }
    }

    static void DemonstrateLogStream() {
        std::cout << "\n=== 自定义日志流演示 ===" << std::endl;

        LogStream debug_log(std::cout, LogStreamBuf::LogLevel::DEBUG);
        LogStream info_log(std::cout, LogStreamBuf::LogLevel::INFO);
        LogStream error_log(std::cerr, LogStreamBuf::LogLevel::ERROR);

        debug_log << "这是一条调试信息" << std::endl;
        info_log << "应用程序启动成功" << std::endl;
        error_log << "发生了一个错误: 文件未找到" << std::endl;

        // 测试缓冲行为
        info_log << "这是一条";
        info_log << "完整的";
        info_log << "信息" << std::endl;  // 只有遇到换行符才会输出
    }
};
```

---

## Part 6: 现代C++中的IO流最佳实践

### 6.1 异常安全的IO操作

现代C++强调异常安全性，IO操作需要妥善处理各种异常情况：

```cpp
#include <fstream>
#include <stdexcept>
#include <memory>

class ExceptionSafeIO {
public:
    // RAII文件处理器
    class SafeFileHandler {
    private:
        std::unique_ptr<std::fstream> file_;
        std::string filename_;
        bool auto_backup_;

    public:
        SafeFileHandler(const std::string& filename, std::ios::openmode mode, bool auto_backup = false)
            : filename_(filename), auto_backup_(auto_backup) {

            // 如果需要备份，先创建备份文件
            if (auto_backup_ && (mode & std::ios::out)) {
                CreateBackup();
            }

            file_ = std::make_unique<std::fstream>(filename, mode);

            if (!file_->is_open()) {
                throw std::runtime_error("无法打开文件: " + filename);
            }

            // 启用异常抛出
            file_->exceptions(std::ios::badbit | std::ios::failbit);
        }

        ~SafeFileHandler() {
            try {
                if (file_ && file_->is_open()) {
                    file_->close();
                }
            } catch (...) {
                // 析构函数中不应该抛出异常
                // 可以记录日志或其他处理
            }
        }

        std::fstream& GetStream() { return *file_; }

        // 事务性写入：要么全部成功，要么全部失败
        template<typename DataContainer>
        void TransactionalWrite(const DataContainer& data) {
            std::string temp_filename = filename_ + ".tmp";

            try {
                // 写入临时文件
                {
                    std::ofstream temp_file(temp_filename);
                    temp_file.exceptions(std::ios::badbit | std::ios::failbit);

                    for (const auto& item : data) {
                        temp_file << item << "\n";
                    }

                    temp_file.flush();
                    temp_file.close();
                }

                // 原子性替换
                file_->close();
                if (std::rename(temp_filename.c_str(), filename_.c_str()) != 0) {
                    throw std::runtime_error("文件替换失败");
                }

                // 重新打开文件
                file_->open(filename_, std::ios::in | std::ios::out);

            } catch (...) {
                // 清理临时文件
                std::remove(temp_filename.c_str());
                throw;
            }
        }

    private:
        void CreateBackup() {
            std::string backup_filename = filename_ + ".bak";
            std::ifstream src(filename_, std::ios::binary);
            if (src) {
                std::ofstream dst(backup_filename, std::ios::binary);
                dst << src.rdbuf();
            }
        }
    };

    // 批量文件处理的异常安全版本
    static void SafeBatchProcess(const std::vector<std::string>& filenames) {
        std::vector<std::unique_ptr<SafeFileHandler>> handlers;

        try {
            // 打开所有文件
            for (const auto& filename : filenames) {
                handlers.emplace_back(
                    std::make_unique<SafeFileHandler>(filename, std::ios::in | std::ios::out, true)
                );
            }

            // 处理所有文件
            for (auto& handler : handlers) {
                auto& stream = handler->GetStream();
                stream.seekg(0, std::ios::end);
                auto size = stream.tellg();
                std::cout << "文件大小: " << size << " 字节" << std::endl;
            }

        } catch (const std::exception& e) {
            std::cerr << "批量处理失败: " << e.what() << std::endl;
            // handlers的析构函数会自动清理资源
            throw;
        }
    }
};
```

### 6.2 性能优化策略

高性能IO编程需要深入理解系统级优化技术：

```cpp
#include <fstream>
#include <vector>
#include <chrono>
#include <thread>
#include <future>

class PerformanceOptimizedIO {
public:
    // 零拷贝文件复制
    static void ZeroCopyFileCopy(const std::string& source, const std::string& destination) {
        std::ifstream src(source, std::ios::binary);
        std::ofstream dst(destination, std::ios::binary);

        if (!src || !dst) {
            throw std::runtime_error("文件打开失败");
        }

        // 使用流缓冲区的直接复制，避免额外的内存拷贝
        dst << src.rdbuf();
    }

    // 异步IO操作
    static std::future<std::string> AsyncReadFile(const std::string& filename) {
        return std::async(std::launch::async, [filename]() {
            std::ifstream file(filename);
            if (!file) {
                throw std::runtime_error("无法打开文件: " + filename);
            }

            std::ostringstream buffer;
            buffer << file.rdbuf();
            return buffer.str();
        });
    }

    // 并行文件处理
    static void ParallelFileProcessing(const std::vector<std::string>& filenames) {
        std::vector<std::future<size_t>> futures;

        // 启动并行任务
        for (const auto& filename : filenames) {
            futures.emplace_back(
                std::async(std::launch::async, [filename]() -> size_t {
                    std::ifstream file(filename);
                    if (!file) return 0;

                    size_t line_count = 0;
                    std::string line;
                    while (std::getline(file, line)) {
                        ++line_count;
                    }
                    return line_count;
                })
            );
        }

        // 收集结果
        for (size_t i = 0; i < futures.size(); ++i) {
            try {
                size_t lines = futures[i].get();
                std::cout << "文件 " << filenames[i] << " 包含 " << lines << " 行" << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "处理文件 " << filenames[i] << " 时出错: " << e.what() << std::endl;
            }
        }
    }

    // 内存映射文件处理（概念实现）
    class MemoryMappedFile {
    private:
        std::string filename_;
        size_t file_size_;

    public:
        explicit MemoryMappedFile(const std::string& filename) : filename_(filename) {
            // 获取文件大小
            std::ifstream file(filename, std::ios::binary | std::ios::ate);
            if (!file) {
                throw std::runtime_error("无法打开文件");
            }
            file_size_ = file.tellg();
        }

        // 模拟内存映射操作
        void ProcessInChunks(size_t chunk_size = 4096) {
            std::ifstream file(filename_, std::ios::binary);
            std::vector<char> buffer(chunk_size);

            size_t total_processed = 0;
            while (file.read(buffer.data(), chunk_size) || file.gcount() > 0) {
                size_t bytes_read = file.gcount();

                // 处理数据块
                ProcessChunk(buffer.data(), bytes_read);

                total_processed += bytes_read;

                // 显示进度
                double progress = (double)total_processed / file_size_ * 100;
                std::cout << "\r处理进度: " << std::fixed << std::setprecision(1)
                          << progress << "%" << std::flush;
            }
            std::cout << std::endl;
        }

    private:
        void ProcessChunk(const char* data, size_t size) {
            // 模拟数据处理
            volatile size_t checksum = 0;
            for (size_t i = 0; i < size; ++i) {
                checksum += static_cast<unsigned char>(data[i]);
            }
        }
    };
};
```

### 6.3 现代C++特性在IO中的应用

```cpp
#include <filesystem>
#include <string_view>
#include <optional>
#include <variant>

namespace fs = std::filesystem;

class ModernIOFeatures {
public:
    // 使用 std::filesystem 进行文件操作
    static void FilesystemOperations() {
        std::cout << "=== 现代文件系统操作 ===" << std::endl;

        fs::path project_dir = "my_project";

        try {
            // 创建目录结构
            fs::create_directories(project_dir / "src" / "include");
            fs::create_directories(project_dir / "build");
            fs::create_directories(project_dir / "docs");

            // 遍历目录
            for (const auto& entry : fs::recursive_directory_iterator(project_dir)) {
                std::cout << entry.path() << " (";
                if (entry.is_directory()) {
                    std::cout << "目录";
                } else {
                    std::cout << "文件, " << entry.file_size() << " 字节";
                }
                std::cout << ")" << std::endl;
            }

            // 文件信息
            auto status = fs::status(project_dir);
            std::cout << "权限: " << std::oct << static_cast<int>(status.permissions()) << std::dec << std::endl;

        } catch (const fs::filesystem_error& e) {
            std::cerr << "文件系统错误: " << e.what() << std::endl;
        }
    }

    // 使用 string_view 优化字符串处理
    static std::optional<double> ParseNumber(std::string_view str) {
        // 去除前后空白字符
        while (!str.empty() && std::isspace(str.front())) {
            str.remove_prefix(1);
        }
        while (!str.empty() && std::isspace(str.back())) {
            str.remove_suffix(1);
        }

        if (str.empty()) {
            return std::nullopt;
        }

        std::istringstream iss(std::string(str));
        double value;
        if (iss >> value && iss.eof()) {
            return value;
        }

        return std::nullopt;
    }

    // 使用 variant 处理多种数据类型
    using ConfigValue = std::variant<int, double, std::string, bool>;

    static ConfigValue ParseConfigValue(std::string_view str) {
        // 尝试解析为布尔值
        if (str == "true" || str == "True" || str == "TRUE") {
            return true;
        }
        if (str == "false" || str == "False" || str == "FALSE") {
            return false;
        }

        // 尝试解析为整数
        std::istringstream iss_int(std::string(str));
        int int_val;
        if (iss_int >> int_val && iss_int.eof()) {
            return int_val;
        }

        // 尝试解析为浮点数
        std::istringstream iss_double(std::string(str));
        double double_val;
        if (iss_double >> double_val && iss_double.eof()) {
            return double_val;
        }

        // 默认为字符串
        return std::string(str);
    }

    // 配置文件解析器
    static std::map<std::string, ConfigValue> ParseConfigFile(const std::string& filename) {
        std::map<std::string, ConfigValue> config;
        std::ifstream file(filename);

        if (!file) {
            throw std::runtime_error("无法打开配置文件: " + filename);
        }

        std::string line;
        int line_number = 0;

        while (std::getline(file, line)) {
            ++line_number;

            // 跳过空行和注释
            if (line.empty() || line[0] == '#') {
                continue;
            }

            // 查找等号
            auto eq_pos = line.find('=');
            if (eq_pos == std::string::npos) {
                std::cerr << "警告: 第" << line_number << "行格式错误" << std::endl;
                continue;
            }

            std::string_view key(line.data(), eq_pos);
            std::string_view value(line.data() + eq_pos + 1, line.length() - eq_pos - 1);

            // 去除空白字符
            while (!key.empty() && std::isspace(key.back())) {
                key.remove_suffix(1);
            }
            while (!value.empty() && std::isspace(value.front())) {
                value.remove_prefix(1);
            }

            config[std::string(key)] = ParseConfigValue(value);
        }

        return config;
    }

    // 配置值访问器
    template<typename T>
    static std::optional<T> GetConfigValue(const std::map<std::string, ConfigValue>& config,
                                         const std::string& key) {
        auto it = config.find(key);
        if (it == config.end()) {
            return std::nullopt;
        }

        if (std::holds_alternative<T>(it->second)) {
            return std::get<T>(it->second);
        }

        return std::nullopt;
    }
};
```

---

## 总结：C++ IO流系统的核心原则

### 🎯 **技术要点总结**

1. **统一抽象**：IO流提供了统一的接口处理不同数据源
2. **类型安全**：编译期类型检查避免格式化错误
3. **异常安全**：使用RAII和异常处理确保资源正确释放
4. **性能优化**：理解缓冲机制，合理使用内存映射和异步IO
5. **可扩展性**：通过自定义流缓冲区扩展功能

### 🚀 **现代C++最佳实践**

- **优先使用RAII**：自动管理文件资源
- **异常安全设计**：使用事务性操作和强异常安全保证
- **性能意识**：了解缓冲区行为，避免不必要的拷贝
- **现代特性应用**：结合filesystem、string_view、optional等特性
- **可测试性**：通过依赖注入和接口抽象提高代码可测试性

## 🎯 学习IO流的正确心态

### IO流并不复杂，关键是理解概念

很多人觉得C++ IO流复杂，其实是因为：

❌ **错误学习方式**：
- 一开始就看复杂的代码实现
- 纠结于模板和继承的细节
- 忽略了基本概念的理解

✅ **正确学习方式**：
- 先理解"流"的概念（数据流动）
- 掌握基本操作（`<<` 和 `>>`）
- 理解状态管理（成功/失败）
- 然后才是高级特性

### 核心概念总结

| 概念 | 简单理解 | 关键点 |
|------|----------|--------|
| **流** | 数据的管道 | 统一的接口处理不同数据源 |
| **输入流** | 从外部读数据到程序 | 用 `>>` 操作符 |
| **输出流** | 从程序写数据到外部 | 用 `<<` 操作符 |
| **缓冲** | 批量处理提高效率 | 像用桶接水而不是用杯子 |
| **状态** | 流的健康状况 | 检查操作是否成功 |
| **格式化** | 控制数据的显示方式 | 让数据按你想要的样子显示 |

### 实践建议

1. **从简单开始**：先掌握 `cout`、`cin`、基本文件操作
2. **理解概念**：每学一个新特性，先问"为什么需要它？"
3. **多练习**：概念理解了，代码自然就会写了
4. **循序渐进**：不要一开始就写复杂的序列化代码

### 常见误区

❌ **"IO流很抽象"** → ✅ IO流就是读写数据，很直观
❌ **"代码很复杂"** → ✅ 核心操作就是 `<<` 和 `>>`
❌ **"需要记很多函数"** → ✅ 理解概念后，函数名都很直观

C++ IO流系统是现代C++编程的基础设施。**理解概念比记住代码更重要**，掌握核心思想比纠结实现细节更有价值。

---

**记住**：编程不是背代码，而是理解问题、设计解决方案。IO流给了你一套优雅的工具，用好它们的关键是理解它们的设计思想。
