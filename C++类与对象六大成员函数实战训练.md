# C++类与对象六大成员函数实战训练

## 🎯 学习目标
掌握C++六大成员函数的实现与应用，为Qt开发打下坚实基础。

## 📚 理论回顾

### 六大成员函数概述
1. **默认构造函数** - 创建对象
2. **拷贝构造函数** - 用同类对象初始化新对象
3. **拷贝赋值运算符** - 已存在对象的赋值
4. **析构函数** - 对象销毁时的清理
5. **移动构造函数** (C++11) - 高效的资源转移
6. **移动赋值运算符** (C++11) - 高效的资源转移赋值

---

## 🏋️ 实战训练

### Level 1: 基础训练 - 简单类设计

**题目1: 设计一个Person类**
```cpp
// 要求：实现所有六大成员函数
class Person {
private:
    std::string name;
    int age;
    
public:
    // TODO: 实现所有六大成员函数
};
```

**参考答案：**
```cpp
#include <iostream>
#include <string>
#include <utility>

class Person {
private:
    std::string name;
    int age;
    
public:
    // 1. 默认构造函数
    Person() : name("Unknown"), age(0) {
        std::cout << "默认构造函数调用" << std::endl;
    }
    
    // 参数化构造函数
    Person(const std::string& n, int a) : name(n), age(a) {
        std::cout << "参数化构造函数调用: " << name << std::endl;
    }
    
    // 2. 拷贝构造函数
    Person(const Person& other) : name(other.name), age(other.age) {
        std::cout << "拷贝构造函数调用: " << name << std::endl;
    }
    
    // 3. 拷贝赋值运算符
    Person& operator=(const Person& other) {
        std::cout << "拷贝赋值运算符调用" << std::endl;
        if (this != &other) {  // 自赋值检查
            name = other.name;
            age = other.age;
        }
        return *this;
    }
    
    // 4. 析构函数
    ~Person() {
        std::cout << "析构函数调用: " << name << std::endl;
    }
    
    // 5. 移动构造函数 (C++11)
    Person(Person&& other) noexcept 
        : name(std::move(other.name)), age(other.age) {
        std::cout << "移动构造函数调用" << std::endl;
        other.age = 0;  // 重置被移动对象
    }
    
    // 6. 移动赋值运算符 (C++11)
    Person& operator=(Person&& other) noexcept {
        std::cout << "移动赋值运算符调用" << std::endl;
        if (this != &other) {
            name = std::move(other.name);
            age = other.age;
            other.age = 0;
        }
        return *this;
    }
    
    // 辅助函数
    void display() const {
        std::cout << "Name: " << name << ", Age: " << age << std::endl;
    }
    
    const std::string& getName() const { return name; }
    int getAge() const { return age; }
};
```

### Level 2: 进阶训练 - 资源管理类

**题目2: 设计一个智能数组类**
```cpp
// 要求：管理动态分配的整数数组，实现深拷贝
class SmartArray {
private:
    int* data;
    size_t size;
    
public:
    // TODO: 实现所有六大成员函数，确保正确的资源管理
};
```

### Level 3: 实战训练 - Qt风格类设计

**题目3: 模拟Qt的QObject基础类**
```cpp
// 要求：设计一个类似QObject的基础类，支持对象名称和父子关系
class QtStyleObject {
private:
    std::string objectName;
    QtStyleObject* parent;
    std::vector<QtStyleObject*> children;
    
public:
    // TODO: 实现合适的成员函数
    // 注意：Qt对象通常禁用拷贝，只允许移动
};
```

---

## 🎯 练习建议

### 日常练习
1. **每天15分钟**：手写一个简单类的六大成员函数
2. **调试技巧**：使用`std::cout`追踪函数调用顺序
3. **内存检查**：使用工具检查内存泄漏

### Qt开发相关重点
- 理解为什么Qt对象通常禁用拷贝
- 掌握智能指针的使用（`std::unique_ptr`, `std::shared_ptr`）
- 熟悉RAII（资源获取即初始化）原则

---

## 📋 检验清单

- [ ] 能够正确实现所有六大成员函数
- [ ] 理解深拷贝vs浅拷贝的区别
- [ ] 掌握移动语义的性能优势
- [ ] 了解何时禁用拷贝构造和赋值
- [ ] 能够处理自赋值情况
- [ ] 理解析构函数中的资源清理

---

*本训练文档专为Qt开发准备，重点关注实用性和最佳实践。*