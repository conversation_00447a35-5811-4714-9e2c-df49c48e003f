#include <iostream>
#include <string>
#include <vector>

// =============================================================================
// IS-A 关系演示（继承关系）- "是一个"的关系
// =============================================================================

class Animal {
protected:
    std::string name_;
public:
    Animal(const std::string& name) : name_(name) {}
    virtual void makeSound() const = 0;
    virtual ~Animal() = default;
};

// Dog IS-A Animal（狗是一种动物）
class Dog : public Animal {
public:
    Dog(const std::string& name) : Animal(name) {}
    void makeSound() const override {
        std::cout << name_ << " says: Woof!" << std::endl;
    }
    void wagTail() const {
        std::cout << name_ << " wags tail!" << std::endl;
    }
};

// Cat IS-A Animal（猫是一种动物）
class Cat : public Animal {
public:
    Cat(const std::string& name) : Animal(name) {}
    void makeSound() const override {
        std::cout << name_ << " says: Meow!" << std::endl;
    }
};

// =============================================================================
// HAS-A 关系演示（组合关系）- "有一个"的关系
// =============================================================================

class Engine {
private:
    std::string type_;
public:
    Engine(const std::string& type) : type_(type) {}
    void start() const {
        std::cout << type_ << " engine starting..." << std::endl;
    }
};

class Wheel {
private:
    int size_;
public:
    Wheel(int size) : size_(size) {}
    void rotate() const {
        std::cout << size_ << " inch wheel rotating" << std::endl;
    }
};

// Car HAS-A Engine, Car HAS-A Wheels（汽车有引擎，汽车有轮子）
class Car {
private:
    std::string model_;
    Engine engine_;              // HAS-A: 汽车有一个引擎
    std::vector<Wheel> wheels_;  // HAS-A: 汽车有多个轮子

public:
    Car(const std::string& model, const Engine& engine) 
        : model_(model), engine_(engine) {
        // 初始化4个轮子
        for(int i = 0; i < 4; i++) {
            wheels_.emplace_back(18);
        }
    }
    
    void start() {
        std::cout << model_ << " starting:" << std::endl;
        engine_.start();  // 委托给引擎对象
    }
    
    void drive() {
        std::cout << model_ << " driving:" << std::endl;
        for(const auto& wheel : wheels_) {
            wheel.rotate();  // 委托给轮子对象
        }
    }
};

// =============================================================================
// 混合示例：同时包含 IS-A 和 HAS-A
// =============================================================================

class Person {
protected:
    std::string name_;
public:
    Person(const std::string& name) : name_(name) {}
    virtual void introduce() const {
        std::cout << "I'm " << name_ << std::endl;
    }
    std::string getName() const { return name_; }
    virtual ~Person() = default;
};

// Student IS-A Person（学生是一种人）
class Student : public Person {
private:
    std::vector<std::string> courses_;  // HAS-A: 学生有多门课程
public:
    Student(const std::string& name) : Person(name) {}
    
    void introduce() const override {
        std::cout << "I'm student " << name_ << std::endl;
    }
    
    void addCourse(const std::string& course) {
        courses_.push_back(course);
        std::cout << name_ << " enrolled in " << course << std::endl;
    }
    
    void showCourses() const {
        std::cout << name_ << "'s courses: ";
        for(const auto& course : courses_) {
            std::cout << course << " ";
        }
        std::cout << std::endl;
    }
};

// =============================================================================
// 演示函数
// =============================================================================

void demonstrateIsA() {
    std::cout << "\n=== IS-A 关系演示 (继承) ===" << std::endl;
    
    Dog dog("旺财");
    Cat cat("咪咪");
    
    // 多态性：可以用基类指针指向派生类对象
    Animal* animals[] = {&dog, &cat};
    
    std::cout << "多态调用：" << std::endl;
    for(int i = 0; i < 2; i++) {
        animals[i]->makeSound();  // 多态调用
    }
    
    std::cout << "特有方法：" << std::endl;
    dog.wagTail();  // 狗特有的方法
}

void demonstrateHasA() {
    std::cout << "\n=== HAS-A 关系演示 (组合) ===" << std::endl;
    
    Engine v6("V6");
    Car myCar("BMW", v6);
    
    myCar.start();  // 汽车启动（内部调用引擎启动）
    myCar.drive();  // 汽车行驶（内部调用轮子转动）
}

void demonstrateMixed() {
    std::cout << "\n=== 混合关系演示 (IS-A + HAS-A) ===" << std::endl;
    
    Student student("张三");  // Student IS-A Person
    student.introduce();
    
    // HAS-A: 学生有课程
    student.addCourse("C++");
    student.addCourse("数据结构");
    student.showCourses();
}

int main() {
    std::cout << "IS-A vs HAS-A 关系对比演示" << std::endl;
    
    demonstrateIsA();
    demonstrateHasA();
    demonstrateMixed();
    
    std::cout << "\n=== 总结 ===" << std::endl;
    std::cout << "IS-A (继承): Dog IS-A Animal - 狗是一种动物" << std::endl;
    std::cout << "HAS-A (组合): Car HAS-A Engine - 汽车有一个引擎" << std::endl;
    std::cout << "选择原则: 能用组合就不用继承！" << std::endl;
    
    return 0;
}
