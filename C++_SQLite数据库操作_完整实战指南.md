# C++ SQLite数据库操作：从入门到实战的完整指南

> **实战导向**：本指南专注于C++中SQLite数据库的实际应用，从环境搭建到高级特性，提供完整的项目实战经验。

---

## Part 0: 核心概念速览——为什么选择SQLite？

### 0.1 SQLite的独特优势：嵌入式数据库的王者

#### 什么是SQLite？

SQLite不是传统的客户端-服务器数据库，而是一个**嵌入式数据库引擎**：

```mermaid
graph LR
    subgraph "传统数据库 (MySQL/PostgreSQL)"
        A1["🖥️ 客户端程序"] -->|"网络连接"| A2["🏢 数据库服务器"]
        A2 --> A3["💾 数据文件"]
    end
    
    subgraph "SQLite数据库"
        B1["🖥️ C++程序"] -->|"直接调用"| B2["📚 SQLite库"]
        B2 --> B3["📄 .db文件"]
    end
    
    classDef traditional fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef sqlite fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    
    class A1,A2,A3 traditional
    class B1,B2,B3 sqlite
```

#### SQLite的核心优势

| 特性 | SQLite | 传统数据库 | 适用场景 |
|------|--------|------------|----------|
| **部署复杂度** | 📦 零配置，单文件 | 🔧 需要安装配置服务器 | 桌面应用、移动应用 |
| **性能** | ⚡ 本地访问，极快 | 🌐 网络延迟 | 本地数据存储 |
| **资源占用** | 🪶 轻量级，几MB | 🏢 重量级，几GB | 嵌入式设备 |
| **并发性** | 👥 读多写少场景 | 👥👥👥 高并发 | 个人应用、小团队 |
| **事务支持** | ✅ 完整ACID | ✅ 完整ACID | 数据一致性要求 |

### 0.2 C++中使用SQLite的方式

#### 三种主要方式对比

```mermaid
graph TD
    A["C++ SQLite操作方式"] --> B["🔧 原生C API"]
    A --> C["🎯 SQLiteCpp包装库"]
    A --> D["🚀 现代ORM框架"]
    
    B --> B1["✅ 完全控制<br/>✅ 性能最优<br/>❌ 代码复杂<br/>❌ 容易出错"]
    
    C --> C1["✅ 简单易用<br/>✅ RAII安全<br/>✅ 异常处理<br/>❌ 需要额外依赖"]
    
    D --> D1["✅ 高级抽象<br/>✅ 类型安全<br/>❌ 学习成本高<br/>❌ 性能开销"]
    
    classDef native fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef wrapper fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef orm fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class B,B1 native
    class C,C1 wrapper
    class D,D1 orm
```

**本指南重点**：我们主要使用**SQLiteCpp**，它在易用性和性能之间达到了最佳平衡。

---

## Part 1: 环境搭建与基础操作

### 1.1 快速环境搭建

#### 方法1：使用vcpkg（推荐）

```bash
# 安装SQLiteCpp
vcpkg install sqlitecpp

# CMakeLists.txt配置
find_package(SQLiteCpp CONFIG REQUIRED)
target_link_libraries(your_target PRIVATE SQLiteCpp)
```

#### 方法2：手动编译

```bash
# 下载SQLite源码
wget https://www.sqlite.org/2023/sqlite-amalgamation-3420000.zip

# 下载SQLiteCpp
git clone https://github.com/SRombauts/SQLiteCpp.git
```

#### 最简单的测试程序

```cpp
#include <SQLiteCpp/SQLiteCpp.h>
#include <iostream>

int main() {
    try {
        // 创建/打开数据库
        SQLite::Database db("test.db", SQLite::OPEN_READWRITE | SQLite::OPEN_CREATE);
        
        std::cout << "SQLite版本: " << db.getLibVersion() << std::endl;
        std::cout << "数据库连接成功！" << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
}
```

### 1.2 数据库基础操作：CRUD的完整实现

#### 创建表结构

```cpp
#include <SQLiteCpp/SQLiteCpp.h>
#include <iostream>

class StudentDatabase {
private:
    SQLite::Database db;
    
public:
    StudentDatabase(const std::string& dbPath) 
        : db(dbPath, SQLite::OPEN_READWRITE | SQLite::OPEN_CREATE) {
        CreateTables();
    }
    
private:
    void CreateTables() {
        // 创建学生表
        const char* createStudentTable = R"(
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                age INTEGER NOT NULL,
                email TEXT UNIQUE,
                gpa REAL DEFAULT 0.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        )";
        
        // 创建课程表
        const char* createCourseTable = R"(
            CREATE TABLE IF NOT EXISTS courses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                course_name TEXT NOT NULL,
                credits INTEGER NOT NULL,
                instructor TEXT
            )
        )";
        
        // 创建选课关系表
        const char* createEnrollmentTable = R"(
            CREATE TABLE IF NOT EXISTS enrollments (
                student_id INTEGER,
                course_id INTEGER,
                grade REAL,
                enrollment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (student_id, course_id),
                FOREIGN KEY (student_id) REFERENCES students(id),
                FOREIGN KEY (course_id) REFERENCES courses(id)
            )
        )";
        
        db.exec(createStudentTable);
        db.exec(createCourseTable);
        db.exec(createEnrollmentTable);
        
        std::cout << "✅ 数据库表创建成功" << std::endl;
    }
};
```

#### 插入数据（Create）

```cpp
class StudentDatabase {
public:
    // 插入单个学生
    int AddStudent(const std::string& name, int age, const std::string& email, double gpa = 0.0) {
        try {
            SQLite::Statement query(db, 
                "INSERT INTO students (name, age, email, gpa) VALUES (?, ?, ?, ?)");
            
            query.bind(1, name);
            query.bind(2, age);
            query.bind(3, email);
            query.bind(4, gpa);
            
            query.exec();
            
            int studentId = static_cast<int>(db.getLastInsertRowid());
            std::cout << "✅ 学生添加成功，ID: " << studentId << std::endl;
            return studentId;
            
        } catch (const SQLite::Exception& e) {
            std::cerr << "❌ 添加学生失败: " << e.what() << std::endl;
            return -1;
        }
    }
    
    // 批量插入学生
    void AddStudentsBatch(const std::vector<std::tuple<std::string, int, std::string, double>>& students) {
        try {
            // 开始事务
            SQLite::Transaction transaction(db);
            
            SQLite::Statement query(db, 
                "INSERT INTO students (name, age, email, gpa) VALUES (?, ?, ?, ?)");
            
            for (const auto& [name, age, email, gpa] : students) {
                query.bind(1, name);
                query.bind(2, age);
                query.bind(3, email);
                query.bind(4, gpa);
                
                query.exec();
                query.reset();  // 重置语句以便重用
            }
            
            // 提交事务
            transaction.commit();
            std::cout << "✅ 批量添加 " << students.size() << " 个学生成功" << std::endl;
            
        } catch (const SQLite::Exception& e) {
            std::cerr << "❌ 批量添加失败: " << e.what() << std::endl;
        }
    }
};
```

#### 查询数据（Read）

```cpp
class StudentDatabase {
public:
    // 查询所有学生
    void ShowAllStudents() {
        try {
            SQLite::Statement query(db, "SELECT id, name, age, email, gpa FROM students ORDER BY id");
            
            std::cout << "\n=== 所有学生信息 ===" << std::endl;
            std::cout << "ID\t姓名\t\t年龄\t邮箱\t\t\tGPA" << std::endl;
            std::cout << std::string(60, '-') << std::endl;
            
            while (query.executeStep()) {
                int id = query.getColumn(0);
                std::string name = query.getColumn(1);
                int age = query.getColumn(2);
                std::string email = query.getColumn(3);
                double gpa = query.getColumn(4);
                
                std::cout << id << "\t" << name << "\t\t" << age << "\t" 
                          << email << "\t" << std::fixed << std::setprecision(2) << gpa << std::endl;
            }
            
        } catch (const SQLite::Exception& e) {
            std::cerr << "❌ 查询失败: " << e.what() << std::endl;
        }
    }
    
    // 按条件查询
    std::vector<Student> FindStudents(const std::string& condition, double minGpa = 0.0) {
        std::vector<Student> results;
        
        try {
            std::string sql = "SELECT id, name, age, email, gpa FROM students WHERE gpa >= ?";
            if (!condition.empty()) {
                sql += " AND (name LIKE ? OR email LIKE ?)";
            }
            
            SQLite::Statement query(db, sql);
            query.bind(1, minGpa);
            
            if (!condition.empty()) {
                std::string pattern = "%" + condition + "%";
                query.bind(2, pattern);
                query.bind(3, pattern);
            }
            
            while (query.executeStep()) {
                Student student;
                student.id = query.getColumn(0);
                student.name = query.getColumn(1);
                student.age = query.getColumn(2);
                student.email = query.getColumn(3);
                student.gpa = query.getColumn(4);
                
                results.push_back(student);
            }
            
        } catch (const SQLite::Exception& e) {
            std::cerr << "❌ 条件查询失败: " << e.what() << std::endl;
        }
        
        return results;
    }
};

// 学生数据结构
struct Student {
    int id;
    std::string name;
    int age;
    std::string email;
    double gpa;
    
    void Print() const {
        std::cout << "ID: " << id << ", 姓名: " << name 
                  << ", 年龄: " << age << ", 邮箱: " << email 
                  << ", GPA: " << std::fixed << std::setprecision(2) << gpa << std::endl;
    }

    // 更新数据（Update）
    bool UpdateStudent(int studentId, const std::string& name, int age, const std::string& email, double gpa) {
        try {
            SQLite::Statement query(db,
                "UPDATE students SET name = ?, age = ?, email = ?, gpa = ? WHERE id = ?");

            query.bind(1, name);
            query.bind(2, age);
            query.bind(3, email);
            query.bind(4, gpa);
            query.bind(5, studentId);

            int changes = query.exec();

            if (changes > 0) {
                std::cout << "✅ 学生信息更新成功，ID: " << studentId << std::endl;
                return true;
            } else {
                std::cout << "⚠️ 未找到ID为 " << studentId << " 的学生" << std::endl;
                return false;
            }

        } catch (const SQLite::Exception& e) {
            std::cerr << "❌ 更新失败: " << e.what() << std::endl;
            return false;
        }
    }

    // 删除数据（Delete）
    bool DeleteStudent(int studentId) {
        try {
            // 先检查学生是否存在
            SQLite::Statement checkQuery(db, "SELECT COUNT(*) FROM students WHERE id = ?");
            checkQuery.bind(1, studentId);
            checkQuery.executeStep();

            if (checkQuery.getColumn(0).getInt() == 0) {
                std::cout << "⚠️ 未找到ID为 " << studentId << " 的学生" << std::endl;
                return false;
            }

            // 开始事务（同时删除相关的选课记录）
            SQLite::Transaction transaction(db);

            // 删除选课记录
            SQLite::Statement deleteEnrollments(db, "DELETE FROM enrollments WHERE student_id = ?");
            deleteEnrollments.bind(1, studentId);
            deleteEnrollments.exec();

            // 删除学生记录
            SQLite::Statement deleteStudent(db, "DELETE FROM students WHERE id = ?");
            deleteStudent.bind(1, studentId);
            deleteStudent.exec();

            transaction.commit();
            std::cout << "✅ 学生及相关记录删除成功，ID: " << studentId << std::endl;
            return true;

        } catch (const SQLite::Exception& e) {
            std::cerr << "❌ 删除失败: " << e.what() << std::endl;
            return false;
        }
    }

    // 统计信息
    void ShowStatistics() {
        try {
            std::cout << "\n=== 数据库统计信息 ===" << std::endl;

            // 学生总数
            SQLite::Statement countStudents(db, "SELECT COUNT(*) FROM students");
            countStudents.executeStep();
            std::cout << "学生总数: " << countStudents.getColumn(0).getInt() << std::endl;

            // 平均GPA
            SQLite::Statement avgGpa(db, "SELECT AVG(gpa) FROM students WHERE gpa > 0");
            avgGpa.executeStep();
            if (!avgGpa.getColumn(0).isNull()) {
                std::cout << "平均GPA: " << std::fixed << std::setprecision(2)
                          << avgGpa.getColumn(0).getDouble() << std::endl;
            }

            // 年龄分布
            SQLite::Statement ageStats(db,
                "SELECT MIN(age) as min_age, MAX(age) as max_age, AVG(age) as avg_age FROM students");
            ageStats.executeStep();
            std::cout << "年龄范围: " << ageStats.getColumn(0).getInt()
                      << " - " << ageStats.getColumn(1).getInt()
                      << " (平均: " << std::fixed << std::setprecision(1)
                      << ageStats.getColumn(2).getDouble() << ")" << std::endl;

        } catch (const SQLite::Exception& e) {
            std::cerr << "❌ 统计查询失败: " << e.what() << std::endl;
        }
    }
};
```

---

## Part 2: 高级特性与最佳实践

### 2.1 事务管理：确保数据一致性

#### 事务的重要性

```mermaid
graph TD
    A["💰 银行转账操作"] --> B["扣除A账户100元"]
    B --> C["增加B账户100元"]

    D["❌ 无事务"] --> D1["扣除成功"] --> D2["💥 程序崩溃"] --> D3["增加失败<br/>💸 钱丢失了！"]

    E["✅ 有事务"] --> E1["开始事务"] --> E2["扣除成功"] --> E3["💥 程序崩溃"] --> E4["自动回滚<br/>💰 钱还在A账户"]

    classDef danger fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef safe fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class D,D1,D2,D3 danger
    class E,E1,E2,E3,E4 safe
```

#### 事务的ACID特性

| 特性 | 英文 | 含义 | SQLite中的实现 |
|------|------|------|----------------|
| **原子性** | Atomicity | 要么全部成功，要么全部失败 | 事务回滚机制 |
| **一致性** | Consistency | 数据库始终保持一致状态 | 约束检查、触发器 |
| **隔离性** | Isolation | 并发事务互不干扰 | 锁机制、WAL模式 |
| **持久性** | Durability | 提交的数据永久保存 | 同步写入磁盘 |

#### 事务的实际应用

```cpp
class StudentDatabase {
public:
    // 学生转专业（复杂操作需要事务保护）
    bool TransferStudent(int studentId, const std::string& newMajor, const std::vector<int>& newCourseIds) {
        try {
            // 开始事务
            SQLite::Transaction transaction(db);

            // 1. 更新学生专业信息
            SQLite::Statement updateMajor(db, "UPDATE students SET major = ? WHERE id = ?");
            updateMajor.bind(1, newMajor);
            updateMajor.bind(2, studentId);
            updateMajor.exec();

            // 2. 删除原有选课记录
            SQLite::Statement deleteOldCourses(db, "DELETE FROM enrollments WHERE student_id = ?");
            deleteOldCourses.bind(1, studentId);
            deleteOldCourses.exec();

            // 3. 添加新的选课记录
            SQLite::Statement addNewCourse(db,
                "INSERT INTO enrollments (student_id, course_id) VALUES (?, ?)");

            for (int courseId : newCourseIds) {
                addNewCourse.bind(1, studentId);
                addNewCourse.bind(2, courseId);
                addNewCourse.exec();
                addNewCourse.reset();
            }

            // 4. 记录操作日志
            SQLite::Statement addLog(db,
                "INSERT INTO operation_logs (student_id, operation, timestamp) VALUES (?, ?, datetime('now'))");
            addLog.bind(1, studentId);
            addLog.bind(2, "专业转换: " + newMajor);
            addLog.exec();

            // 提交事务
            transaction.commit();
            std::cout << "✅ 学生转专业操作成功" << std::endl;
            return true;

        } catch (const SQLite::Exception& e) {
            // 事务自动回滚
            std::cerr << "❌ 转专业操作失败，已回滚: " << e.what() << std::endl;
            return false;
        }
    }
};
```

### 2.2 预编译语句：提高性能和安全性

#### 为什么需要预编译语句？

```mermaid
graph TD
    subgraph "普通SQL执行"
        A1["SQL字符串"] --> A2["解析SQL"] --> A3["编译执行计划"] --> A4["执行"] --> A5["返回结果"]
    end

    subgraph "预编译语句执行"
        B1["SQL模板"] --> B2["一次解析"] --> B3["一次编译"] --> B4["多次执行<br/>只需绑定参数"] --> B5["返回结果"]
    end

    C["🔄 重复执行1000次"] --> A1
    C --> B1

    classDef normal fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef prepared fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A1,A2,A3,A4,A5 normal
    class B1,B2,B3,B4,B5 prepared
```

#### 预编译语句的优势

| 优势 | 普通SQL | 预编译语句 | 性能提升 |
|------|---------|------------|----------|
| **解析开销** | 每次都要解析 | 只解析一次 | 🚀 50-80% |
| **SQL注入** | 容易受攻击 | 天然防护 | 🛡️ 100%安全 |
| **内存使用** | 重复分配 | 复用对象 | 💾 节省30% |
| **代码可读性** | 字符串拼接 | 参数绑定 | 📖 更清晰 |

### 2.3 性能优化技巧

#### 索引优化

```cpp
class OptimizedDatabase {
public:
    void CreateOptimalIndexes() {
        try {
            // 为常用查询字段创建索引
            db.exec("CREATE INDEX IF NOT EXISTS idx_student_email ON students(email)");
            db.exec("CREATE INDEX IF NOT EXISTS idx_student_gpa ON students(gpa)");
            db.exec("CREATE INDEX IF NOT EXISTS idx_enrollment_student ON enrollments(student_id)");

            // 复合索引用于多字段查询
            db.exec("CREATE INDEX IF NOT EXISTS idx_student_age_gpa ON students(age, gpa)");

            std::cout << "✅ 索引创建完成" << std::endl;

        } catch (const SQLite::Exception& e) {
            std::cerr << "❌ 索引创建失败: " << e.what() << std::endl;
        }
    }

    // 分析查询性能
    void AnalyzeQuery(const std::string& sql) {
        try {
            SQLite::Statement query(db, "EXPLAIN QUERY PLAN " + sql);

            std::cout << "\n=== 查询执行计划 ===" << std::endl;
            while (query.executeStep()) {
                std::cout << "ID: " << query.getColumn(0).getInt()
                          << ", Parent: " << query.getColumn(1).getInt()
                          << ", Detail: " << query.getColumn(3).getText() << std::endl;
            }

        } catch (const SQLite::Exception& e) {
            std::cerr << "❌ 查询分析失败: " << e.what() << std::endl;
        }
    }
};
```

---

## Part 3: 实战项目：完整的学生管理系统

### 3.1 项目架构设计

```mermaid
graph TD
    A["🎯 学生管理系统"] --> B["📊 数据层"]
    A --> C["🔧 业务层"]
    A --> D["🖥️ 表示层"]

    B --> B1["SQLite数据库"]
    B --> B2["数据访问对象(DAO)"]
    B --> B3["连接池管理"]

    C --> C1["学生管理服务"]
    C --> C2["课程管理服务"]
    C --> C3["成绩管理服务"]

    D --> D1["命令行界面"]
    D --> D2["配置管理"]
    D --> D3["日志系统"]

    classDef data fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef business fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef ui fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class B,B1,B2,B3 data
    class C,C1,C2,C3 business
    class D,D1,D2,D3 ui
```

### 3.2 完整的学生管理系统实现

```cpp
#include <SQLiteCpp/SQLiteCpp.h>
#include <iostream>
#include <vector>
#include <memory>
#include <iomanip>
#include <sstream>

// 数据模型
struct Student {
    int id = 0;
    std::string name;
    int age = 0;
    std::string email;
    std::string major;
    double gpa = 0.0;
    std::string created_at;

    void Print() const {
        std::cout << std::setw(4) << id
                  << std::setw(15) << name
                  << std::setw(4) << age
                  << std::setw(25) << email
                  << std::setw(15) << major
                  << std::setw(6) << std::fixed << std::setprecision(2) << gpa << std::endl;
    }

    static void PrintHeader() {
        std::cout << std::setw(4) << "ID"
                  << std::setw(15) << "姓名"
                  << std::setw(4) << "年龄"
                  << std::setw(25) << "邮箱"
                  << std::setw(15) << "专业"
                  << std::setw(6) << "GPA" << std::endl;
        std::cout << std::string(70, '-') << std::endl;
    }
};

struct Course {
    int id = 0;
    std::string name;
    int credits = 0;
    std::string instructor;

    void Print() const {
        std::cout << std::setw(4) << id
                  << std::setw(20) << name
                  << std::setw(6) << credits
                  << std::setw(15) << instructor << std::endl;
    }
};

// 数据访问层
class StudentDAO {
private:
    SQLite::Database& db;

public:
    explicit StudentDAO(SQLite::Database& database) : db(database) {}

    // 创建表
    void CreateTables() {
        const char* createStudentTable = R"(
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                age INTEGER NOT NULL CHECK(age > 0 AND age < 150),
                email TEXT UNIQUE NOT NULL,
                major TEXT DEFAULT '未定',
                gpa REAL DEFAULT 0.0 CHECK(gpa >= 0.0 AND gpa <= 4.0),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        )";

        const char* createCourseTable = R"(
            CREATE TABLE IF NOT EXISTS courses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                credits INTEGER NOT NULL CHECK(credits > 0),
                instructor TEXT NOT NULL
            )
        )";

        const char* createEnrollmentTable = R"(
            CREATE TABLE IF NOT EXISTS enrollments (
                student_id INTEGER,
                course_id INTEGER,
                grade REAL CHECK(grade >= 0.0 AND grade <= 100.0),
                enrollment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (student_id, course_id),
                FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
            )
        )";

        db.exec(createStudentTable);
        db.exec(createCourseTable);
        db.exec(createEnrollmentTable);

        // 创建索引
        db.exec("CREATE INDEX IF NOT EXISTS idx_student_email ON students(email)");
        db.exec("CREATE INDEX IF NOT EXISTS idx_student_major ON students(major)");
        db.exec("CREATE INDEX IF NOT EXISTS idx_enrollment_student ON enrollments(student_id)");
    }

    // 添加学生
    int AddStudent(const Student& student) {
        SQLite::Statement query(db,
            "INSERT INTO students (name, age, email, major, gpa) VALUES (?, ?, ?, ?, ?)");

        query.bind(1, student.name);
        query.bind(2, student.age);
        query.bind(3, student.email);
        query.bind(4, student.major);
        query.bind(5, student.gpa);

        query.exec();
        return static_cast<int>(db.getLastInsertRowid());
    }

    // 查找学生
    std::vector<Student> FindStudents(const std::string& condition = "") {
        std::vector<Student> students;

        std::string sql = "SELECT id, name, age, email, major, gpa, created_at FROM students";
        if (!condition.empty()) {
            sql += " WHERE " + condition;
        }
        sql += " ORDER BY id";

        SQLite::Statement query(db, sql);

        while (query.executeStep()) {
            Student student;
            student.id = query.getColumn(0);
            student.name = query.getColumn(1);
            student.age = query.getColumn(2);
            student.email = query.getColumn(3);
            student.major = query.getColumn(4);
            student.gpa = query.getColumn(5);
            student.created_at = query.getColumn(6);

            students.push_back(student);
        }

        return students;
    }

    // 更新学生
    bool UpdateStudent(const Student& student) {
        SQLite::Statement query(db,
            "UPDATE students SET name = ?, age = ?, email = ?, major = ?, gpa = ? WHERE id = ?");

        query.bind(1, student.name);
        query.bind(2, student.age);
        query.bind(3, student.email);
        query.bind(4, student.major);
        query.bind(5, student.gpa);
        query.bind(6, student.id);

        return query.exec() > 0;
    }

    // 删除学生
    bool DeleteStudent(int studentId) {
        SQLite::Transaction transaction(db);

        // 删除选课记录
        SQLite::Statement deleteEnrollments(db, "DELETE FROM enrollments WHERE student_id = ?");
        deleteEnrollments.bind(1, studentId);
        deleteEnrollments.exec();

        // 删除学生
        SQLite::Statement deleteStudent(db, "DELETE FROM students WHERE id = ?");
        deleteStudent.bind(1, studentId);
        int changes = deleteStudent.exec();

        transaction.commit();
        return changes > 0;
    }

    // 统计信息
    void ShowStatistics() {
        std::cout << "\n=== 📊 数据库统计信息 ===" << std::endl;

        // 学生总数
        SQLite::Statement countQuery(db, "SELECT COUNT(*) FROM students");
        countQuery.executeStep();
        std::cout << "学生总数: " << countQuery.getColumn(0).getInt() << std::endl;

        // 专业分布
        SQLite::Statement majorQuery(db,
            "SELECT major, COUNT(*) as count FROM students GROUP BY major ORDER BY count DESC");
        std::cout << "\n专业分布:" << std::endl;
        while (majorQuery.executeStep()) {
            std::cout << "  " << majorQuery.getColumn(0).getText()
                      << ": " << majorQuery.getColumn(1).getInt() << " 人" << std::endl;
        }

        // GPA统计
        SQLite::Statement gpaQuery(db,
            "SELECT AVG(gpa) as avg_gpa, MIN(gpa) as min_gpa, MAX(gpa) as max_gpa FROM students WHERE gpa > 0");
        gpaQuery.executeStep();
        if (!gpaQuery.getColumn(0).isNull()) {
            std::cout << "\nGPA统计:" << std::endl;
            std::cout << "  平均GPA: " << std::fixed << std::setprecision(2)
                      << gpaQuery.getColumn(0).getDouble() << std::endl;
            std::cout << "  最低GPA: " << gpaQuery.getColumn(1).getDouble() << std::endl;
            std::cout << "  最高GPA: " << gpaQuery.getColumn(2).getDouble() << std::endl;
        }
    }
};

// 业务逻辑层
class StudentService {
private:
    std::unique_ptr<StudentDAO> dao;

public:
    explicit StudentService(SQLite::Database& db) {
        dao = std::make_unique<StudentDAO>(db);
        dao->CreateTables();
    }

    void ShowMenu() {
        std::cout << "\n🎓 学生管理系统" << std::endl;
        std::cout << "1. 添加学生" << std::endl;
        std::cout << "2. 查看所有学生" << std::endl;
        std::cout << "3. 搜索学生" << std::endl;
        std::cout << "4. 更新学生信息" << std::endl;
        std::cout << "5. 删除学生" << std::endl;
        std::cout << "6. 统计信息" << std::endl;
        std::cout << "7. 批量导入测试数据" << std::endl;
        std::cout << "0. 退出" << std::endl;
        std::cout << "请选择操作: ";
    }

    void AddStudent() {
        Student student;

        std::cout << "\n=== 添加学生 ===" << std::endl;
        std::cout << "姓名: ";
        std::getline(std::cin, student.name);

        std::cout << "年龄: ";
        std::cin >> student.age;
        std::cin.ignore();

        std::cout << "邮箱: ";
        std::getline(std::cin, student.email);

        std::cout << "专业: ";
        std::getline(std::cin, student.major);

        std::cout << "GPA: ";
        std::cin >> student.gpa;
        std::cin.ignore();

        try {
            int id = dao->AddStudent(student);
            std::cout << "✅ 学生添加成功，ID: " << id << std::endl;
        } catch (const SQLite::Exception& e) {
            std::cerr << "❌ 添加失败: " << e.what() << std::endl;
        }
    }

    void ShowAllStudents() {
        auto students = dao->FindStudents();

        std::cout << "\n=== 📋 所有学生信息 ===" << std::endl;
        if (students.empty()) {
            std::cout << "暂无学生记录" << std::endl;
            return;
        }

        Student::PrintHeader();
        for (const auto& student : students) {
            student.Print();
        }
        std::cout << "\n共 " << students.size() << " 名学生" << std::endl;
    }

    void SearchStudents() {
        std::string keyword;
        std::cout << "\n=== 🔍 搜索学生 ===" << std::endl;
        std::cout << "请输入搜索关键词（姓名或专业）: ";
        std::getline(std::cin, keyword);

        std::string condition = "name LIKE '%" + keyword + "%' OR major LIKE '%" + keyword + "%'";
        auto students = dao->FindStudents(condition);

        if (students.empty()) {
            std::cout << "未找到匹配的学生" << std::endl;
            return;
        }

        Student::PrintHeader();
        for (const auto& student : students) {
            student.Print();
        }
        std::cout << "\n找到 " << students.size() << " 名学生" << std::endl;
    }

    void ImportTestData() {
        std::vector<Student> testStudents = {
            {0, "张三", 20, "<EMAIL>", "计算机科学", 3.8},
            {0, "李四", 21, "<EMAIL>", "软件工程", 3.6},
            {0, "王五", 19, "<EMAIL>", "数据科学", 3.9},
            {0, "赵六", 22, "<EMAIL>", "人工智能", 3.4},
            {0, "钱七", 20, "<EMAIL>", "网络安全", 3.7}
        };

        std::cout << "\n=== 📥 导入测试数据 ===" << std::endl;
        int successCount = 0;

        for (const auto& student : testStudents) {
            try {
                dao->AddStudent(student);
                successCount++;
            } catch (const SQLite::Exception& e) {
                std::cerr << "导入 " << student.name << " 失败: " << e.what() << std::endl;
            }
        }

        std::cout << "✅ 成功导入 " << successCount << "/" << testStudents.size() << " 条记录" << std::endl;
    }

    void Run() {
        int choice;

        while (true) {
            ShowMenu();
            std::cin >> choice;
            std::cin.ignore(); // 清除换行符

            switch (choice) {
                case 1: AddStudent(); break;
                case 2: ShowAllStudents(); break;
                case 3: SearchStudents(); break;
                case 6: dao->ShowStatistics(); break;
                case 7: ImportTestData(); break;
                case 0:
                    std::cout << "👋 再见！" << std::endl;
                    return;
                default:
                    std::cout << "❌ 无效选择，请重试" << std::endl;
            }

            std::cout << "\n按回车键继续...";
            std::cin.get();
        }
    }
};

// 主程序
int main() {
    try {
        // 创建数据库连接
        SQLite::Database db("student_management.db",
                           SQLite::OPEN_READWRITE | SQLite::OPEN_CREATE);

        std::cout << "🎓 学生管理系统启动成功" << std::endl;
        std::cout << "SQLite版本: " << db.getLibVersion() << std::endl;

        // 启动服务
        StudentService service(db);
        service.Run();

    } catch (const std::exception& e) {
        std::cerr << "💥 系统错误: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
```

---

## Part 4: 最佳实践与性能优化

### 4.1 错误处理与异常安全

#### 异常处理的层次结构

```mermaid
graph TD
    A["SQLite异常类型"] --> B["SQLite::Exception"]
    B --> C["连接异常"]
    B --> D["SQL语法异常"]
    B --> E["约束违反异常"]
    B --> F["权限异常"]

    G["处理策略"] --> H["记录日志"]
    G --> I["用户友好提示"]
    G --> J["事务回滚"]
    G --> K["资源清理"]

    classDef exception fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef strategy fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A,B,C,D,E,F exception
    class G,H,I,J,K strategy
```

#### 健壮的错误处理实现

```cpp
class RobustDatabase {
private:
    SQLite::Database db;
    std::string logFile;

public:
    RobustDatabase(const std::string& dbPath) : db(dbPath, SQLite::OPEN_READWRITE | SQLite::OPEN_CREATE) {
        logFile = "database_errors.log";
        ConfigureDatabase();
    }

private:
    void ConfigureDatabase() {
        try {
            // 启用外键约束
            db.exec("PRAGMA foreign_keys = ON");

            // 设置WAL模式提高并发性能
            db.exec("PRAGMA journal_mode = WAL");

            // 设置同步模式
            db.exec("PRAGMA synchronous = NORMAL");

        } catch (const SQLite::Exception& e) {
            LogError("数据库配置失败", e.what());
            throw;
        }
    }

    void LogError(const std::string& operation, const std::string& error) {
        std::ofstream log(logFile, std::ios::app);
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);

        log << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "] "
            << "操作: " << operation << ", 错误: " << error << std::endl;
    }

public:
    // 安全的数据库操作包装器
    template<typename Func>
    auto SafeExecute(const std::string& operation, Func&& func) -> decltype(func()) {
        try {
            return func();
        } catch (const SQLite::Exception& e) {
            LogError(operation, e.what());

            // 根据错误类型提供用户友好的消息
            if (e.getErrorCode() == SQLITE_CONSTRAINT) {
                throw std::runtime_error("数据约束违反：请检查输入数据的有效性");
            } else if (e.getErrorCode() == SQLITE_BUSY) {
                throw std::runtime_error("数据库忙碌：请稍后重试");
            } else {
                throw std::runtime_error("数据库操作失败：" + std::string(e.what()));
            }
        }
    }
};
```

### 4.2 性能优化清单

#### 性能优化的关键点

| 优化项 | 说明 | 性能提升 | 实现难度 |
|--------|------|----------|----------|
| **预编译语句** | 重复使用SQL语句 | 🚀🚀🚀 | ⭐ |
| **事务批处理** | 批量操作使用事务 | 🚀🚀🚀 | ⭐ |
| **索引优化** | 为查询字段创建索引 | 🚀🚀 | ⭐⭐ |
| **WAL模式** | 提高并发读写性能 | 🚀🚀 | ⭐ |
| **连接池** | 复用数据库连接 | 🚀 | ⭐⭐⭐ |
| **查询优化** | 避免SELECT * 等 | 🚀 | ⭐⭐ |

#### 性能监控代码

```cpp
class PerformanceMonitor {
private:
    std::chrono::high_resolution_clock::time_point startTime;
    std::string operation;

public:
    explicit PerformanceMonitor(const std::string& op) : operation(op) {
        startTime = std::chrono::high_resolution_clock::now();
    }

    ~PerformanceMonitor() {
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

        std::cout << "⏱️ " << operation << " 耗时: " << duration.count() << " ms" << std::endl;

        if (duration.count() > 1000) {
            std::cout << "⚠️ 操作耗时较长，建议优化" << std::endl;
        }
    }
};

// 使用示例
void OptimizedBatchInsert(SQLite::Database& db, const std::vector<Student>& students) {
    PerformanceMonitor monitor("批量插入" + std::to_string(students.size()) + "条记录");

    SQLite::Transaction transaction(db);
    SQLite::Statement stmt(db, "INSERT INTO students (name, age, email, major, gpa) VALUES (?, ?, ?, ?, ?)");

    for (const auto& student : students) {
        stmt.bind(1, student.name);
        stmt.bind(2, student.age);
        stmt.bind(3, student.email);
        stmt.bind(4, student.major);
        stmt.bind(5, student.gpa);

        stmt.exec();
        stmt.reset();
    }

    transaction.commit();
}
```

---

## 🎯 总结：C++ SQLite开发的核心要点

### 核心概念回顾

1. **SQLite的优势**：零配置、高性能、ACID事务支持
2. **SQLiteCpp的价值**：RAII安全、异常处理、简化API
3. **事务的重要性**：确保数据一致性和操作原子性
4. **预编译语句**：提高性能和安全性的关键技术

### 开发最佳实践

✅ **推荐做法**：
- 使用SQLiteCpp等现代C++包装库
- 为重复操作使用预编译语句
- 批量操作时使用事务
- 为常用查询字段创建索引
- 实现完善的错误处理和日志记录

❌ **避免的陷阱**：
- 直接拼接SQL字符串（SQL注入风险）
- 忘记使用事务进行批量操作
- 不处理SQLite异常
- 在循环中重复编译SQL语句
- 忽略数据库性能监控

### 学习路径建议

1. **基础阶段**：掌握基本的CRUD操作
2. **进阶阶段**：学习事务、索引、性能优化
3. **实战阶段**：完成完整项目，如学生管理系统
4. **高级阶段**：研究并发控制、数据迁移、备份恢复

**记住**：数据库操作不仅仅是技术问题，更是数据安全和系统稳定性的保障。掌握SQLite，你就掌握了现代C++应用开发的重要技能！🚀
