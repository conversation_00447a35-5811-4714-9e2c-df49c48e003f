# C++速成学习计划 - 3天Qt开发必备

## 📅 学习时间安排（7月29日-8月1日）

### 🎯 学习目标（基于您的基础调整）
- 巩固内存管理，熟练使用new/delete和智能指针
- 深化继承多态理解，掌握Qt开发常用模式
- 熟练STL容器操作，为Qt集合类打基础
- 学习Qt核心机制，顺利过渡到Qt开发

---

## Day 1 (7月29日) - 内存管理强化 【6小时】

### 上午 (3小时) - 深度内存管理

#### 1. 内存管理进阶 (2小时)
**必读文档**: `C++内存管理详解.md` (重点章节3-5)
**重点强化**:
- new/delete的正确配对使用
- 数组new[] 和 delete[] 的区别
- 内存泄漏检测与避免
- 深拷贝 vs 浅拷贝问题

**实战练习**:
```cpp
// 解决常见内存管理问题
class StringWrapper {
private:
    char* data;
    size_t length;
public:
    StringWrapper(const char* str);
    StringWrapper(const StringWrapper& other);  // 拷贝构造
    StringWrapper& operator=(const StringWrapper& other);  // 赋值操作符
    ~StringWrapper();
};
```

#### 2. RAII原则深化 (1小时)
**重点掌握**:
- 资源获取即初始化的思想
- 异常安全的内存管理
- 自动资源管理

### 下午 (3小时) - 智能指针实战

#### 3. 智能指针熟练使用 (2小时)
**重点掌握**:
- unique_ptr 的完整用法（自定义删除器、移动语义）
- shared_ptr 的引用计数机制
- weak_ptr 解决循环引用
- make_unique 和 make_shared 的优势

#### 4. Qt内存管理预备 (1小时)
**学习内容**:
- Qt对象树的内存管理机制
- QObject父子关系
- Qt智能指针类 (QPointer, QScopedPointer)

---

## Day 2 (7月30日) - STL熟练与现代C++ 【6小时】

### 上午 (3小时) - STL容器深化

#### 5. STL容器熟练掌握 (2小时)
**必读文档**: `C++标准库STL完全指南.md` (容器部分)
**在您已有基础上强化**:
- vector 的内存管理与性能优化
- string 的高效操作技巧
- map vs unordered_map 的选择
- 容器的正确初始化方式

**实战练习**:
```cpp
// STL容器实际应用
std::vector<std::unique_ptr<Widget>> widgets;
std::map<std::string, std::function<void()>> callbacks;
```

#### 6. 迭代器与算法 (1小时)
**重点掌握**:
- 迭代器失效问题
- 常用算法的正确使用
- 范围for与传统迭代器的选择

### 下午 (3小时) - 现代C++特性

#### 7. C++11/14核心特性 (2小时)
**重点掌握**:
- auto 的正确使用场景
- lambda 表达式的完整语法
- 右值引用与移动语义基础
- 初始化列表

#### 8. 函数对象与回调 (1小时)
**为Qt信号槽做准备**:
- std::function 的使用
- std::bind 的应用
- 函数指针 vs 函数对象

---

## Day 3 (7月31日) - Qt核心机制与实战 【6小时】

### 上午 (3小时) - 继承多态深化

#### 9. 继承多态进阶 (2小时)
**必读文档**: `C++继承与多态_重构版_完整指南.md` (高级部分)
**在您已有基础上深化**:
- 虚函数的性能考虑与最佳实践
- 多重继承的问题与解决方案
- 接口设计模式在Qt中的应用
- 虚析构函数的重要性

#### 10. Qt设计模式预备 (1小时)
**重点理解**:
- 观察者模式（信号槽的基础）
- 工厂模式在Qt中的应用
- 单例模式的正确实现

### 下午 (3小时) - Qt核心机制

#### 11. Qt对象系统 (1.5小时)
**学习内容**:
- QObject 的元对象系统
- Q_OBJECT 宏的作用
- 属性系统（Q_PROPERTY）
- Qt的反射机制

#### 12. 信号与槽实战 (1.5小时)
**重点掌握**:
- 信号槽的五种连接方式
- 自定义信号和槽的编写
- 信号槽的线程安全性
- 旧式语法 vs 新式语法

---

## 🚀 每日学习建议（基于您的基础调整）

### 学习方法（针对您的基础）
1. **跳过基础概念**: 类与对象、继承基础可快速浏览
2. **重点突破薄弱环节**: 内存管理、智能指针深入学习
3. **实战导向**: 每个知识点都要考虑在Qt中的实际应用
4. **查漏补缺**: 遇到不熟悉的概念立即深入学习

### 编程环境准备
- 推荐使用Qt Creator（既可以练习C++又能熟悉Qt环境）
- 创建简单的C++项目验证知识点

### 时间分配建议（总计18小时）
- **Day 1**: 内存管理深化 (6小时)
- **Day 2**: STL熟练+现代C++ (6小时)  
- **Day 3**: 继承多态+Qt核心 (6小时)

---

## 📚 重点文档阅读优先级

### 必读 (第一优先级 - 基于您的薄弱环节)
1. `C++内存管理详解.md` (重点第3-6章)
2. `C++标准库STL完全指南.md` (容器与算法部分)
3. `C++继承与多态_重构版_完整指南.md` (高级特性部分)

### 选读 (第二优先级 - 巩固基础)
1. `C++类与对象_重构版_完整指南.md` (六大成员函数部分)
2. `C++异常处理与错误管理.md` (RAII与异常安全)
3. `C++移动语义与完美转发.md` (移动构造与移动赋值)

### 快速参考 (遇到问题时查阅)
1. `C++基础语法权威指南.md`
2. `C++_类型转换_从C风格到现代C++实践.md`

---

## 🎯 Qt开发必备知识清单（基于您的基础调整）

### Day 1 完成后应掌握
- [ ] 熟练使用new/delete，避免内存泄漏
- [ ] 理解并应用RAII原则
- [ ] 掌握unique_ptr和shared_ptr的使用场景
- [ ] 了解Qt对象树的内存管理机制

### Day 2 完成后应掌握
- [ ] 熟练使用STL容器（vector, map, string等）
- [ ] 掌握lambda表达式和auto关键字
- [ ] 理解std::function和回调机制
- [ ] 了解移动语义的基本概念

### Day 3 完成后应掌握
- [ ] 深入理解虚函数和多态机制
- [ ] 掌握Qt信号槽的连接和使用
- [ ] 理解QObject的元对象系统
- [ ] 能够设计简单的Qt类层次结构

---

## 🔥 紧急救援 - 针对您的最精简版本

### 如果时间特别紧张，重点强化这些
1. **内存管理**: 智能指针的使用（1天）
2. **STL熟练**: vector, map, string的高效使用（0.5天）
3. **Qt信号槽**: 连接方式和自定义信号槽（0.5天）

### 可以暂时跳过（您已有基础）
- 类与对象的基础语法
- 继承的基本概念
- STL的基础API调用

---

## 💡 学习提示

1. **不求完美理解**: 先能用起来，细节后续补充
2. **重视实践**: 每个概念都要写代码验证
3. **关联应用**: 时刻想着这些知识在Qt中怎么用
4. **做好笔记**: 记录重点和疑问，为后续学习做准备

## 📞 后续支持
- 学习过程中遇到问题可随时提问
- 完成每日学习后可进行知识点检测
- Qt学习开始后可提供C++与Qt结合的指导

**祝您学习顺利！加油！💪**
