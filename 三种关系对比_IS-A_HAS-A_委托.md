# IS-A vs HAS-A vs 委托 - 三种关系详解

## 🎯 核心概念对比

| 关系类型 | 含义 | 实现方式 | 关键词 | 耦合度 |
|----------|------|----------|--------|--------|
| **IS-A** | "是一个" | 继承 | `class Dog : public Animal` | 强耦合 |
| **HAS-A** | "有一个" | 组合/聚合 | `Engine engine_;` | 中等耦合 |
| **委托** | "使用一个" | 委托调用 | `calculator_.add(a, b);` | 松耦合 |

## 📝 代码示例对比

### 1. IS-A 关系（继承）

```cpp
// 基类
class Animal {
public:
    virtual void makeSound() const = 0;
    virtual ~Animal() = default;
};

// Dog IS-A Animal（狗是一种动物）
class Dog : public Animal {
public:
    void makeSound() const override {
        std::cout << "Woof!" << std::endl;
    }
};

// 使用：多态性
Animal* animal = new Dog();
animal->makeSound();  // 调用Dog的实现
```

**特点**：
- ✅ 支持多态性
- ✅ 代码复用（继承基类功能）
- ❌ 强耦合，编译时确定
- ❌ 单继承限制（C++支持多继承但复杂）

### 2. HAS-A 关系（组合）

```cpp
class Engine {
public:
    void start() const {
        std::cout << "Engine starting..." << std::endl;
    }
};

// Car HAS-A Engine（汽车有一个引擎）
class Car {
private:
    Engine engine_;  // 组合：汽车包含引擎

public:
    void start() {
        std::cout << "Car starting..." << std::endl;
        engine_.start();  // 直接调用成员对象
    }
};

// 使用
Car myCar;
myCar.start();  // 内部调用engine_.start()
```

**特点**：
- ✅ 松耦合（相对于继承）
- ✅ 灵活的对象组合
- ✅ 避免继承的复杂性
- ❌ 不支持多态性
- ❌ 需要转发接口

### 3. 委托关系（Delegation）

```cpp
class Calculator {
public:
    int add(int a, int b) const {
        return a + b;
    }
};

// MathProcessor 委托给 Calculator
class MathProcessor {
private:
    Calculator calc_;  // 委托对象

public:
    int processAddition(int x, int y) {
        std::cout << "委托计算..." << std::endl;
        return calc_.add(x, y);  // 委托调用
    }
    
    // 可以运行时更换委托对象
    void setCalculator(const Calculator& calc) {
        calc_ = calc;
    }
};
```

**特点**：
- ✅ 最松耦合
- ✅ 运行时可更换委托对象
- ✅ 职责分离清晰
- ✅ 支持多种设计模式
- ❌ 可能增加间接调用开销

## 🔍 实际应用场景

### IS-A 适用场景

```cpp
// ✅ 良好的继承设计
class Shape {
public:
    virtual double area() const = 0;
    virtual void draw() const = 0;
};

class Circle : public Shape {  // Circle IS-A Shape
    // 圆确实是一种形状
};

class Rectangle : public Shape {  // Rectangle IS-A Shape  
    // 矩形确实是一种形状
};
```

**使用原则**：
- 真正的"是一个"关系
- 需要多态性
- 符合里氏替换原则

### HAS-A 适用场景

```cpp
// ✅ 良好的组合设计
class Computer {
private:
    CPU cpu_;           // Computer HAS-A CPU
    Memory memory_;     // Computer HAS-A Memory
    HardDisk disk_;     // Computer HAS-A HardDisk

public:
    void boot() {
        cpu_.initialize();
        memory_.clear();
        disk_.loadOS();
    }
};
```

**使用原则**：
- 对象之间是"包含"关系
- 需要复用其他类的功能
- 避免继承的复杂性

### 委托适用场景

```cpp
// ✅ 策略模式中的委托
class SortContext {
private:
    std::unique_ptr<SortStrategy> strategy_;  // 委托对象

public:
    void setStrategy(std::unique_ptr<SortStrategy> strategy) {
        strategy_ = std::move(strategy);  // 运行时更换
    }
    
    void sort(std::vector<int>& data) {
        strategy_->sort(data);  // 委托调用
    }
};

// ✅ 装饰器模式中的委托
class TextDecorator {
private:
    std::unique_ptr<TextProcessor> processor_;  // 委托对象

public:
    std::string process(const std::string& text) {
        // 委托 + 增强
        std::string result = processor_->process(text);
        return enhance(result);
    }
};
```

**使用原则**：
- 需要运行时更换行为
- 实现设计模式（策略、装饰器、代理等）
- 职责分离和松耦合

## 🎨 设计模式中的应用

### 1. 策略模式（委托）

```cpp
class PaymentProcessor {
private:
    std::unique_ptr<PaymentStrategy> strategy_;  // 委托

public:
    void pay(double amount) {
        strategy_->processPayment(amount);  // 委托调用
    }
    
    void setPaymentMethod(std::unique_ptr<PaymentStrategy> strategy) {
        strategy_ = std::move(strategy);  // 运行时切换
    }
};
```

### 2. 装饰器模式（继承 + 委托）

```cpp
class Component {
public:
    virtual void operation() const = 0;
};

class Decorator : public Component {  // IS-A Component
private:
    std::unique_ptr<Component> component_;  // HAS-A Component (委托)

public:
    void operation() const override {
        component_->operation();  // 委托调用
        // 添加装饰功能
    }
};
```

### 3. 适配器模式（组合 + 委托）

```cpp
class Adapter : public Target {  // IS-A Target
private:
    Adaptee adaptee_;  // HAS-A Adaptee

public:
    void request() override {
        adaptee_.specificRequest();  // 委托调用
    }
};
```

## 🏆 选择指南

### 决策流程图

```
需要多态性？
├─ 是 → 真正的"是一个"关系？
│   ├─ 是 → 使用继承（IS-A）
│   └─ 否 → 考虑接口 + 组合
└─ 否 → 需要运行时更换行为？
    ├─ 是 → 使用委托
    └─ 否 → 使用组合（HAS-A）
```

### 优先级原则

1. **优先考虑委托**：最灵活，松耦合
2. **其次考虑组合**：避免继承复杂性
3. **最后考虑继承**：只在真正需要多态时使用

### 实际建议

```cpp
// ❌ 避免：为了代码复用而继承
class Timer {
public:
    void start() { /* ... */ }
    void stop() { /* ... */ }
};

class Bomb : public Timer {  // 炸弹不是计时器！
    // 错误的继承使用
};

// ✅ 推荐：使用组合或委托
class Bomb {
private:
    Timer timer_;  // HAS-A 或委托

public:
    void arm() { 
        timer_.start();  // 委托调用
    }
};
```

## 📚 总结

### 核心原则

1. **"能用委托就不用组合，能用组合就不用继承"**
2. **继承表示"是一个"，组合表示"有一个"，委托表示"使用一个"**
3. **优先选择松耦合的设计**

### 记忆口诀

- **IS-A**：我**是**你的一种特殊情况
- **HAS-A**：我**拥有**你作为我的一部分  
- **委托**：我**请你**帮我完成某件事

### 最佳实践

```cpp
// 推荐的现代C++设计
class ModernClass {
private:
    std::unique_ptr<Strategy> strategy_;      // 委托：灵活的策略
    std::vector<Component> components_;       // 组合：拥有的组件
    
public:
    // 委托模式：运行时可变
    void setStrategy(std::unique_ptr<Strategy> s) {
        strategy_ = std::move(s);
    }
    
    void execute() {
        strategy_->execute();  // 委托调用
        
        for (auto& comp : components_) {
            comp.process();  // 组合调用
        }
    }
};

// 继承只用于真正的多态场景
class AbstractBase {
public:
    virtual void polymorphicMethod() = 0;
    virtual ~AbstractBase() = default;
};

class ConcreteImpl : public AbstractBase {  // 真正的IS-A关系
public:
    void polymorphicMethod() override {
        // 多态实现
    }
};
```

记住：**设计的核心是职责分离和松耦合！** 🎯
