#include <iostream>
#include <string>
#include <vector>

// 演示链式调用 vs 循环/递归的区别
class ChainVsLoop {
private:
    std::string name_;
    int age_;
    std::string email_;
    std::vector<std::string> skills_;

public:
    ChainVsLoop() : age_(0) {}

    // ===== 链式调用：每次返回*this，支持连续操作 =====
    ChainVsLoop& setName(const std::string& name) {
        name_ = name;
        std::cout << "设置姓名: " << name << std::endl;
        return *this;  // 返回当前对象的引用，不是递归！
    }

    ChainVsLoop& setAge(int age) {
        age_ = age;
        std::cout << "设置年龄: " << age << std::endl;
        return *this;  // 每次都返回同一个对象
    }

    ChainVsLoop& setEmail(const std::string& email) {
        email_ = email;
        std::cout << "设置邮箱: " << email << std::endl;
        return *this;
    }

    ChainVsLoop& addSkill(const std::string& skill) {
        skills_.push_back(skill);
        std::cout << "添加技能: " << skill << std::endl;
        return *this;
    }

    void show() const {
        std::cout << "=== 个人信息 ===" << std::endl;
        std::cout << "姓名: " << name_ << std::endl;
        std::cout << "年龄: " << age_ << std::endl;
        std::cout << "邮箱: " << email_ << std::endl;
        std::cout << "技能: ";
        for (const auto& skill : skills_) {
            std::cout << skill << " ";
        }
        std::cout << std::endl << std::endl;
    }
};

// 模拟递归调用的类（真正的递归）
class RecursiveDemo {
private:
    int level_;

public:
    RecursiveDemo(int level = 0) : level_(level) {}

    // 真正的递归：函数调用自己
    void recursiveCount(int n) {
        if (n <= 0) {
            std::cout << "递归结束" << std::endl;
            return;
        }
        
        std::cout << "递归层级 " << n << " (对象level: " << level_ << ")" << std::endl;
        recursiveCount(n - 1);  // 函数调用自己，这才是递归！
    }
};

// 演示循环操作的类
class LoopDemo {
private:
    std::vector<int> numbers_;

public:
    // 使用循环进行批量操作
    void addNumbersWithLoop(int count) {
        std::cout << "使用循环添加 " << count << " 个数字:" << std::endl;
        
        for (int i = 1; i <= count; ++i) {  // 这是循环
            numbers_.push_back(i);
            std::cout << "添加数字: " << i << std::endl;
        }
    }

    void showNumbers() const {
        std::cout << "数字列表: ";
        for (int num : numbers_) {
            std::cout << num << " ";
        }
        std::cout << std::endl << std::endl;
    }
};

void demonstrateDifferences() {
    std::cout << "=== 链式调用 vs 循环 vs 递归的区别演示 ===" << std::endl;

    // 1. 链式调用：连续的函数调用，每次返回同一个对象
    std::cout << "\n【1. 链式调用演示】" << std::endl;
    std::cout << "特点：连续调用不同的函数，每次都返回同一个对象引用" << std::endl;
    
    ChainVsLoop person;
    // 这是链式调用：person.setName() 返回 person的引用，
    // 然后继续调用 .setAge()，再返回 person的引用，依此类推
    person.setName("张三")        // 返回person&
          .setAge(25)           // 在person上调用setAge，返回person&
          .setEmail("zhang@xx") // 在person上调用setEmail，返回person&
          .addSkill("C++")      // 在person上调用addSkill，返回person&
          .addSkill("Python");  // 在person上调用addSkill
    
    person.show();
    
    std::cout << "链式调用等价于：" << std::endl;
    ChainVsLoop person2;
    person2.setName("李四");       // 第1次调用
    person2.setAge(30);           // 第2次调用
    person2.setEmail("li@xx");    // 第3次调用
    person2.addSkill("Java");     // 第4次调用
    person2.show();

    // 2. 循环演示：重复执行相同或类似的操作
    std::cout << "\n【2. 循环演示】" << std::endl;
    std::cout << "特点：重复执行相同的代码块，通常有计数器控制" << std::endl;
    
    LoopDemo loopDemo;
    loopDemo.addNumbersWithLoop(5);  // 内部使用for循环
    loopDemo.showNumbers();

    // 3. 递归演示：函数调用自己
    std::cout << "\n【3. 递归演示】" << std::endl;
    std::cout << "特点：函数调用自己，通常有终止条件" << std::endl;
    
    RecursiveDemo recursiveDemo;
    recursiveDemo.recursiveCount(4);  // 函数会调用自己4次
}

// 深入理解：链式调用的内部机制
class ChainMechanism {
private:
    int value_;

public:
    ChainMechanism(int val = 0) : value_(val) {}

    // 每个函数都返回*this，实现链式调用
    ChainMechanism& add(int val) {
        value_ += val;
        std::cout << "执行add(" << val << "), 当前值: " << value_ 
                  << ", this地址: " << this << std::endl;
        return *this;  // 关键：返回当前对象的引用
    }

    ChainMechanism& multiply(int val) {
        value_ *= val;
        std::cout << "执行multiply(" << val << "), 当前值: " << value_ 
                  << ", this地址: " << this << std::endl;
        return *this;
    }

    ChainMechanism& subtract(int val) {
        value_ -= val;
        std::cout << "执行subtract(" << val << "), 当前值: " << value_ 
                  << ", this地址: " << this << std::endl;
        return *this;
    }

    int getValue() const { return value_; }
};

void demonstrateChainMechanism() {
    std::cout << "\n=== 链式调用的内部机制演示 ===" << std::endl;
    
    ChainMechanism calc(10);
    std::cout << "对象地址: " << &calc << std::endl;
    
    // 链式调用：每次都在同一个对象上操作
    calc.add(5)      // calc对象调用add，返回calc的引用
        .multiply(2) // 在calc对象上调用multiply，返回calc的引用  
        .subtract(3); // 在calc对象上调用subtract
    
    std::cout << "最终结果: " << calc.getValue() << std::endl;
    
    std::cout << "\n上面的链式调用等价于：" << std::endl;
    ChainMechanism calc2(10);
    calc2.add(5);      // 第1步：10 + 5 = 15
    calc2.multiply(2); // 第2步：15 * 2 = 30  
    calc2.subtract(3); // 第3步：30 - 3 = 27
    std::cout << "分步执行结果: " << calc2.getValue() << std::endl;
}

int main() {
    demonstrateDifferences();
    demonstrateChainMechanism();
    return 0;
}
