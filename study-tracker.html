<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C++ 学习进度跟踪器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            height: 100vh;
            display: flex;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .sidebar {
            width: 380px;
            background: white;
            border-right: 1px solid #e1e5e9;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow-y: auto;
            flex-shrink: 0;
        }

        .header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .header h1 {
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        .progress-info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .progress-info div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .stats-section {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #e1e5e9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .controls {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #e1e5e9;
        }

        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .btn:hover {
            background: #5a6268;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .chapters {
            flex: 1;
            padding: 30px;
            background: #f8f9fa;
        }

        .chapters-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .chapters-header h2 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .chapters-header p {
            color: #666;
            font-size: 16px;
            margin: 0;
        }

        .chapters-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
            padding: 0 15px;
        }

        .chapter {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            padding: 15px;
            transition: box-shadow 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            width: 100%;
            box-sizing: border-box;
        }

        .chapter:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .chapter.completed {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #28a745;
        }

        .chapter.active {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }

        .chapter-header {
            margin-bottom: 15px;
        }

        .chapter-title {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .chapter-title input[type="checkbox"] {
            margin-right: 12px;
            margin-top: 2px;
            transform: scale(1.3);
            cursor: pointer;
        }

        .chapter-title-text {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
        }

        .read-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,123,255,0.3);
            flex-shrink: 0;
        }

        .read-btn:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,123,255,0.4);
        }

        .btn-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 8px;
        }

        .chapter-info {
            font-size: 13px;
            color: #666;
            margin-top: 8px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #007bff;
        }

        .chapter-stage {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-top: 8px;
        }

        .stage-basic { background: #e8f5e8; color: #155724; }
        .stage-growth { background: #fff3cd; color: #856404; }
        .stage-advanced { background: #f3e5f5; color: #7b1fa2; }
        .stage-expert { background: #e3f2fd; color: #1976d2; }

        .content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow-y: auto;
        }

        .content-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px 30px;
            border-bottom: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .content-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        .current-chapter {
            font-weight: 600;
            color: white;
            font-size: 18px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1;
            position: relative;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .current-chapter::before {
            content: '📖';
            font-size: 20px;
        }

        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.2);
            z-index: 1;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .reader {
            flex: 1;
            background: white;
            position: relative;
        }

        .reader iframe {
            width: 100%;
            height: 100%;
            min-height: 800px;
            border: none;
            display: block;
        }

        .chapter-reader {
            width: 100%;
            height: 100%;
            min-height: calc(100vh - 60px);
            overflow: hidden;
            background: #f8f9fa;
            padding: 20px;
            box-sizing: border-box;
        }

        .chapter-reader iframe {
            width: 100%;
            height: calc(100vh - 100px);
            min-height: 600px;
            border: none;
            display: block;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            background: white;
        }

        .welcome {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 60px);
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            overflow-y: auto;
        }

        .welcome-content {
            max-width: 1200px;
            width: 95%;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            padding: 40px;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            text-align: left;
        }

        .welcome h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 28px;
        }

        .welcome p {
            line-height: 1.8;
            margin-bottom: 25px;
            font-size: 16px;
            color: #555;
        }

        .tip {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #2196f3;
            box-shadow: 0 2px 8px rgba(33,150,243,0.2);
        }

        .welcome h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 36px;
            font-weight: 700;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .welcome h3 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 20px;
            font-weight: 600;
        }

        .welcome ul {
            color: #666;
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 20px;
            padding-left: 20px;
        }

        .welcome li {
            margin-bottom: 8px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #dee2e6;
            transition: box-shadow 0.2s ease;
        }

        .feature-card:hover {
            box-shadow: 0 6px 15px rgba(0,0,0,0.1);
        }

        .feature-card h4 {
            color: #495057;
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .feature-card p {
            color: #6c757d;
            font-size: 14px;
            margin: 0;
        }

        .start-button {
            display: block;
            width: 300px;
            margin: 40px auto 20px;
            padding: 18px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .start-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .progress-stats {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 28px;
            font-weight: 700;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e1e5e9;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
        }

        .close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .close:hover {
            color: #000;
        }

        .note-input {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
        }

        .note-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .notes-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .note-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }

        .note-date {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .note-content {
            line-height: 1.6;
            white-space: pre-wrap;
        }

        /* 响应式设计优化 */
        @media (max-width: 768px) {
            .sidebar {
                width: 300px;
            }

            .chapter-reader {
                padding: 10px;
            }

            .chapter-reader iframe {
                border-radius: 8px;
                min-height: 500px;
            }

            .content-header {
                padding: 15px 20px;
            }

            .current-chapter {
                font-size: 16px;
            }

            .close-btn {
                padding: 8px 16px;
                font-size: 13px;
            }

            .welcome-content {
                width: 95%;
                padding: 20px;
            }

            .chapters-grid {
                grid-template-columns: 1fr;
                padding: 0 10px;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="header">
            <h1>🚀 C++ 学习进度</h1>
            <div class="progress-info">
                <div><span>完成进度:</span><span id="progress">0%</span></div>
                <div><span>已完成:</span><span id="completed">0/11</span></div>
                <div><span>学习天数:</span><span id="days">0</span></div>
            </div>
        </div>

        <!-- 学习统计 -->
        <div class="stats-section">
            <h3 style="margin-bottom: 0; font-size: 1.1em; color: #333;">📊 学习统计</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="studyDays">0</div>
                    <div class="stat-label">已学习天数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalHours">0h</div>
                    <div class="stat-label">总学习时长</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avgHours">0h</div>
                    <div class="stat-label">平均每日学习</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="currentStage">🔰 基础入门</div>
                    <div class="stat-label">当前阶段</div>
                </div>
            </div>
        </div>

        <!-- 导航按钮 -->
        <div class="controls">
            <div class="btn-group">
                <button class="btn" onclick="showHomePage()" style="background: #17a2b8;">🏠 首页</button>
                <button class="btn" onclick="showChapterList()" style="background: #6f42c1;">📚 章节</button>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls">
            <div class="btn-group">
                <button class="btn" onclick="addStudyDay()">📅 +1天</button>
                <button class="btn" onclick="updateStudyHours()">⏰ 学习时长</button>
                <button class="btn" onclick="showNotes()">📝 学习笔记</button>
                <button class="btn btn-success" onclick="exportProgress()">📊 导出进度</button>
                <button class="btn btn-danger" onclick="resetProgress()">🔄 重置</button>
            </div>
        </div>

        <div class="chapters" id="chapterList" style="display: none;">
            <div class="chapters-header">
                <h2>📚 C++ 学习章节</h2>
                <p>选择章节开始您的C++学习之旅</p>
            </div>

            <div class="chapters-grid">
                <div class="chapter" data-file="C++基础语法权威指南.html">
                    <div class="chapter-header">
                        <div class="chapter-title">
                            <input type="checkbox" onchange="updateProgress()">
                            <div class="chapter-title-text">第一章：C++基础语法权威指南</div>
                        </div>
                        <div class="btn-group">
                            <button class="read-btn" onclick="loadChapter(this)">📖 内嵌阅读</button>
                            <button class="read-btn" onclick="openInNewWindow(this)" style="background: #28a745;">🔗 新窗口</button>
                        </div>
                    </div>
                    <div class="chapter-info">⏱️ 预计时长：2-3天 | 🎯 建立C++基础认知</div>
                    <div class="chapter-stage stage-basic">🔰 基础阶段</div>
                </div>

                <div class="chapter" data-file="cpp_class_详解_完整版.html">
                    <div class="chapter-header">
                        <div class="chapter-title">
                            <input type="checkbox" onchange="updateProgress()">
                            <div class="chapter-title-text">第二章：C++类详解完整版</div>
                        </div>
                        <div class="btn-group">
                            <button class="read-btn" onclick="loadChapter(this)">📖 内嵌阅读</button>
                            <button class="read-btn" onclick="openInNewWindow(this)" style="background: #28a745;">🔗 新窗口</button>
                        </div>
                    </div>
                    <div class="chapter-info">⏱️ 预计时长：3-4天 | 🎯 掌握面向对象编程</div>
                    <div class="chapter-stage stage-basic">🔰 基础阶段</div>
                </div>

                <div class="chapter" data-file="C++内存管理详解.html">
                    <div class="chapter-header">
                        <div class="chapter-title">
                            <input type="checkbox" onchange="updateProgress()">
                            <div class="chapter-title-text">第三章：C++内存管理详解</div>
                        </div>
                        <div class="btn-group">
                            <button class="read-btn" onclick="loadChapter(this)">📖 内嵌阅读</button>
                            <button class="read-btn" onclick="openInNewWindow(this)" style="background: #28a745;">🔗 新窗口</button>
                        </div>
                    </div>
                    <div class="chapter-info">⏱️ 预计时长：2-3天 | 🎯 理解内存管理机制</div>
                    <div class="chapter-stage stage-basic">🔰 基础阶段</div>
                </div>

                <div class="chapter" data-file="CPP声明定义综合指南.html">
                    <div class="chapter-header">
                        <div class="chapter-title">
                            <input type="checkbox" onchange="updateProgress()">
                            <div class="chapter-title-text">第四章：C++声明定义综合指南</div>
                        </div>
                        <div class="btn-group">
                            <button class="read-btn" onclick="loadChapter(this)">📖 内嵌阅读</button>
                            <button class="read-btn" onclick="openInNewWindow(this)" style="background: #28a745;">🔗 新窗口</button>
                        </div>
                    </div>
                    <div class="chapter-info">⏱️ 预计时长：2天 | 🎯 掌握声明与定义规则</div>
                    <div class="chapter-stage stage-basic">🔰 基础阶段</div>
                </div>

                <div class="chapter" data-file="C++_继承与多态_从入门到专业实践.html">
                    <div class="chapter-header">
                        <div class="chapter-title">
                            <input type="checkbox" onchange="updateProgress()">
                            <div class="chapter-title-text">第五章：继承与多态专业实践</div>
                        </div>
                        <div class="btn-group">
                            <button class="read-btn" onclick="loadChapter(this)">📖 内嵌阅读</button>
                            <button class="read-btn" onclick="openInNewWindow(this)" style="background: #28a745;">🔗 新窗口</button>
                        </div>
                    </div>
                    <div class="chapter-info">⏱️ 预计时长：3-4天 | 🎯 深入面向对象高级特性</div>
                    <div class="chapter-stage stage-growth">📈 成长阶段</div>
                </div>

                <div class="chapter" data-file="C++标准库STL完全指南.html">
                    <div class="chapter-header">
                        <div class="chapter-title">
                            <input type="checkbox" onchange="updateProgress()">
                            <div class="chapter-title-text">第六章：STL标准库完全指南</div>
                        </div>
                        <div class="btn-group">
                            <button class="read-btn" onclick="loadChapter(this)">📖 内嵌阅读</button>
                            <button class="read-btn" onclick="openInNewWindow(this)" style="background: #28a745;">🔗 新窗口</button>
                        </div>
                    </div>
                    <div class="chapter-info">⏱️ 预计时长：4-5天 | 🎯 掌握STL容器和算法</div>
                    <div class="chapter-stage stage-growth">📈 成长阶段</div>
                </div>

                <div class="chapter" data-file="C++异常处理与错误管理.html">
                    <div class="chapter-header">
                        <div class="chapter-title">
                            <input type="checkbox" onchange="updateProgress()">
                            <div class="chapter-title-text">第七章：异常处理与错误管理</div>
                        </div>
                        <div class="btn-group">
                            <button class="read-btn" onclick="loadChapter(this)">📖 内嵌阅读</button>
                            <button class="read-btn" onclick="openInNewWindow(this)" style="background: #28a745;">🔗 新窗口</button>
                        </div>
                    </div>
                    <div class="chapter-info">⏱️ 预计时长：2天 | 🎯 学会优雅的错误处理</div>
                    <div class="chapter-stage stage-growth">📈 成长阶段</div>
                </div>

                <div class="chapter" data-file="C++移动语义与完美转发.html">
                    <div class="chapter-header">
                        <div class="chapter-title">
                            <input type="checkbox" onchange="updateProgress()">
                            <div class="chapter-title-text">第八章：移动语义与完美转发</div>
                        </div>
                        <div class="btn-group">
                            <button class="read-btn" onclick="loadChapter(this)">📖 内嵌阅读</button>
                            <button class="read-btn" onclick="openInNewWindow(this)" style="background: #28a745;">🔗 新窗口</button>
                        </div>
                    </div>
                    <div class="chapter-info">⏱️ 预计时长：3天 | 🎯 掌握现代C++核心特性</div>
                    <div class="chapter-stage stage-advanced">🚀 进阶阶段</div>
                </div>

                <div class="chapter" data-file="C++并发编程专业实践.html">
                    <div class="chapter-header">
                        <div class="chapter-title">
                            <input type="checkbox" onchange="updateProgress()">
                            <div class="chapter-title-text">第九章：并发编程专业实践</div>
                        </div>
                        <div class="btn-group">
                            <button class="read-btn" onclick="loadChapter(this)">📖 内嵌阅读</button>
                            <button class="read-btn" onclick="openInNewWindow(this)" style="background: #28a745;">🔗 新窗口</button>
                        </div>
                    </div>
                    <div class="chapter-info">⏱️ 预计时长：4-5天 | 🎯 掌握多线程编程技术</div>
                    <div class="chapter-stage stage-advanced">🚀 进阶阶段</div>
                </div>

                <div class="chapter" data-file="C++_模板全解_从入门到专业实践.html">
                    <div class="chapter-header">
                        <div class="chapter-title">
                            <input type="checkbox" onchange="updateProgress()">
                            <div class="chapter-title-text">第十章：模板全解专业实践</div>
                        </div>
                        <div class="btn-group">
                            <button class="read-btn" onclick="loadChapter(this)">📖 内嵌阅读</button>
                            <button class="read-btn" onclick="openInNewWindow(this)" style="background: #28a745;">🔗 新窗口</button>
                        </div>
                    </div>
                    <div class="chapter-info">⏱️ 预计时长：4-5天 | 🎯 精通C++泛型编程</div>
                    <div class="chapter-stage stage-advanced">🚀 进阶阶段</div>
                </div>

                <div class="chapter" data-file="C++_类型转换_从C风格到现代C++实践.html">
                    <div class="chapter-header">
                        <div class="chapter-title">
                            <input type="checkbox" onchange="updateProgress()">
                            <div class="chapter-title-text">第十一章：类型转换现代实践</div>
                        </div>
                        <div class="btn-group">
                            <button class="read-btn" onclick="loadChapter(this)">📖 内嵌阅读</button>
                            <button class="read-btn" onclick="openInNewWindow(this)" style="background: #28a745;">🔗 新窗口</button>
                        </div>
                    </div>
                    <div class="chapter-info">⏱️ 预计时长：2-3天 | 🎯 完善类型系统理解</div>
                    <div class="chapter-stage stage-expert">🏆 专家阶段</div>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="content-header" id="contentHeader" style="display: none;">
            <div class="current-chapter" id="currentChapter"></div>
            <button class="close-btn" onclick="closeReader()">✕ 关闭</button>
        </div>
        
        <div class="reader" id="reader">
            <!-- 首页内容 -->
            <div class="home-content" id="homeContent">
                <div style="padding: 30px; max-width: 1000px; margin: 0 auto;">
                    <div style="text-align: center; margin-bottom: 40px;">
                        <h1 style="color: #333; margin-bottom: 15px;">🔬 C++ 混合探究式学习系统</h1>
                        <p style="color: #666; font-size: 18px; line-height: 1.6;">先体验 → 后深入 → 再实践 → 最终掌握</p>
                    </div>

                    <!-- 学习理念 -->
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 12px; margin-bottom: 30px; color: white;">
                        <h2 style="margin-bottom: 20px; text-align: center;">💡 学习理念</h2>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; text-align: center;">
                            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;">
                                <div style="font-size: 32px; margin-bottom: 10px;">🎯</div>
                                <h4 style="margin-bottom: 8px;">快速体验</h4>
                                <p style="font-size: 14px; opacity: 0.9; margin: 0;">通过实例快速理解概念，建立整体认知</p>
                            </div>
                            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;">
                                <div style="font-size: 32px; margin-bottom: 10px;">🔍</div>
                                <h4 style="margin-bottom: 8px;">深度探究</h4>
                                <p style="font-size: 14px; opacity: 0.9; margin: 0;">深入理解原理和机制，掌握核心知识</p>
                            </div>
                            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;">
                                <div style="font-size: 32px; margin-bottom: 10px;">⚡</div>
                                <h4 style="margin-bottom: 8px;">实践应用</h4>
                                <p style="font-size: 14px; opacity: 0.9; margin: 0;">通过项目巩固知识，提升实战能力</p>
                            </div>
                        </div>
                    </div>

                    <!-- 四轮学习路径 -->
                    <div style="margin-bottom: 30px;">
                        <h2 style="text-align: center; margin-bottom: 25px; color: #333;">🗺️ 四轮学习路径设计</h2>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <!-- 第一轮 -->
                            <div style="padding: 20px; background: #e8f5e8; border-radius: 10px; border-left: 5px solid #28a745;">
                                <h3 style="color: #155724; margin-bottom: 15px;">🚀 第一轮：快速体验</h3>
                                <div style="color: #155724; line-height: 1.6;">
                                    <p><strong>时间：</strong>3-5天</p>
                                    <p><strong>目标：</strong>快速建立C++整体认知</p>
                                    <p><strong>路径：</strong>第1章 → 第2章(类基础) → 第6章(STL) → 第9章(并发)</p>
                                    <p><strong>重点：</strong>理解概念，建立框架</p>
                                </div>
                            </div>

                            <!-- 第二轮 -->
                            <div style="padding: 20px; background: #fff3cd; border-radius: 10px; border-left: 5px solid #ffc107;">
                                <h3 style="color: #856404; margin-bottom: 15px;">🔍 第二轮：深度探究</h3>
                                <div style="color: #856404; line-height: 1.6;">
                                    <p><strong>时间：</strong>15-20天</p>
                                    <p><strong>目标：</strong>深入理解核心概念</p>
                                    <p><strong>路径：</strong>第1-6章按序深入学习</p>
                                    <p><strong>重点：</strong>内存管理、面向对象</p>
                                </div>
                            </div>

                            <!-- 第三轮 -->
                            <div style="padding: 20px; background: #f3e5f5; border-radius: 10px; border-left: 5px solid #9c27b0;">
                                <h3 style="color: #7b1fa2; margin-bottom: 15px;">⚡ 第三轮：现代特性</h3>
                                <div style="color: #7b1fa2; line-height: 1.6;">
                                    <p><strong>时间：</strong>10-15天</p>
                                    <p><strong>目标：</strong>掌握现代C++特性</p>
                                    <p><strong>路径：</strong>第7-11章现代特性</p>
                                    <p><strong>重点：</strong>移动语义、并发、模板</p>
                                </div>
                            </div>

                            <!-- 第四轮 -->
                            <div style="padding: 20px; background: #e3f2fd; border-radius: 10px; border-left: 5px solid #2196f3;">
                                <h3 style="color: #1976d2; margin-bottom: 15px;">🏗️ 第四轮：项目实践</h3>
                                <div style="color: #1976d2; line-height: 1.6;">
                                    <p><strong>时间：</strong>7-10天</p>
                                    <p><strong>目标：</strong>综合运用所学知识</p>
                                    <p><strong>路径：</strong>实际项目开发</p>
                                    <p><strong>重点：</strong>代码组织、性能优化</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 学习轮次选择 -->
                    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin-bottom: 30px; text-align: center;">
                        <h3 style="margin-bottom: 20px; color: #333;">🎯 选择当前学习轮次</h3>
                        <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap; margin-bottom: 20px;">
                            <button class="btn learning-round-btn" data-round="1" onclick="setLearningRound(1)" style="background: #28a745; padding: 12px 20px;">🚀 第一轮：快速体验</button>
                            <button class="btn learning-round-btn" data-round="2" onclick="setLearningRound(2)" style="background: #ffc107; color: #856404; padding: 12px 20px;">🔍 第二轮：深度探究</button>
                            <button class="btn learning-round-btn" data-round="3" onclick="setLearningRound(3)" style="background: #9c27b0; padding: 12px 20px;">⚡ 第三轮：现代特性</button>
                            <button class="btn learning-round-btn" data-round="4" onclick="setLearningRound(4)" style="background: #2196f3; padding: 12px 20px;">🏗️ 第四轮：项目实践</button>
                        </div>
                        <div id="currentRoundTip" style="padding: 15px; background: #e3f2fd; border-radius: 6px; color: #1976d2;">
                            请选择您当前的学习轮次，系统将为您提供相应的学习建议
                        </div>
                    </div>

                    <!-- 学习进度概览 -->
                    <div style="background: white; padding: 25px; border-radius: 10px; border: 1px solid #e1e5e9; margin-bottom: 30px;">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #333;">📊 学习进度概览</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <div style="font-size: 28px; font-weight: bold; color: #007bff; margin-bottom: 5px;" id="homeProgress">0%</div>
                                <div style="color: #666;">完成进度</div>
                            </div>
                            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <div style="font-size: 28px; font-weight: bold; color: #28a745; margin-bottom: 5px;" id="homeCompleted">0/11</div>
                                <div style="color: #666;">已完成章节</div>
                            </div>
                            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <div style="font-size: 28px; font-weight: bold; color: #ffc107; margin-bottom: 5px;" id="homeDays">0</div>
                                <div style="color: #666;">学习天数</div>
                            </div>
                            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <div style="font-size: 28px; font-weight: bold; color: #dc3545; margin-bottom: 5px;" id="homeHours">0h</div>
                                <div style="color: #666;">总时长</div>
                            </div>
                        </div>
                    </div>

                    <!-- 开始学习按钮 -->
                    <div style="text-align: center;">
                        <button class="btn" onclick="showChapterList()" style="background: #28a745; color: white; padding: 15px 30px; font-size: 16px; border-radius: 8px;">
                            🚀 开始学习之旅
                        </button>
                    </div>
                </div>
            </div>

            <!-- 章节阅读区域 -->
            <div class="chapter-reader" id="chapterReader" style="display: none;">
                <!-- 这里会动态加载iframe -->
            </div>

            <!-- 欢迎界面 -->
            <div class="welcome" id="welcome" style="display: none;">
                <div class="welcome-content">
                    <h1>📚 C++ 完整学习指南</h1>
                    <p style="text-align: center; font-size: 18px; color: #667eea; margin-bottom: 30px;">从入门到专家的完整学习路径 | 11个核心主题 | 智能进度跟踪</p>

                    <div class="progress-stats">
                        <div class="stat-item">
                            <span class="stat-number" id="welcomeProgress">0%</span>
                            <div class="stat-label">学习进度</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="welcomeDays">0</span>
                            <div class="stat-label">学习天数</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="welcomeHours">0h</span>
                            <div class="stat-label">总学时</div>
                        </div>
                    </div>

                    <h3>🎯 学习特色</h3>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4>📚 系统化课程</h4>
                            <p>11个核心主题，从基础语法到高级特性，循序渐进的学习路径</p>
                        </div>
                        <div class="feature-card">
                            <h4>⚡ 本地加载</h4>
                            <p>所有文档都是本地HTML文件，无需网络，加载速度极快</p>
                        </div>
                        <div class="feature-card">
                            <h4>📊 智能跟踪</h4>
                            <p>自动记录学习进度，统计学习时长，可视化学习成果</p>
                        </div>
                        <div class="feature-card">
                            <h4>📝 笔记系统</h4>
                            <p>内置笔记功能，随时记录学习心得，支持导出分享</p>
                        </div>
                    </div>

                    <h3>📖 学习内容概览</h3>
                    <ul>
                        <li><strong>基础阶段：</strong>C++语法基础、类与对象、内存管理、声明定义</li>
                        <li><strong>进阶阶段：</strong>继承多态、STL标准库、异常处理、移动语义</li>
                        <li><strong>高级阶段：</strong>模板编程、类型转换、并发编程</li>
                    </ul>

                    <div class="tip">
                        <strong>💡 学习建议：</strong>
                        <br>• 建议每天学习1-2小时，保持连续性
                        <br>• 每完成一章后，及时做练习和总结
                        <br>• 遇到难点可以反复阅读，做好笔记
                        <br>• 理论结合实践，多写代码验证概念
                    </div>

                    <button class="start-button" onclick="showChapterList()">
                        🚀 开始学习之旅
                    </button>

                    <p style="text-align: center; color: #999; font-size: 14px; margin-top: 20px;">
                        点击左侧章节列表选择具体章节开始学习
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 学习笔记模态框 -->
    <div id="notesModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📝 学习笔记</h3>
                <button class="close" onclick="closeNotesModal()">&times;</button>
            </div>
            <div class="modal-body">
                <textarea id="noteInput" class="note-input" placeholder="在这里记录您的学习心得、重点知识、疑问等..."></textarea>
                <div class="note-actions">
                    <button class="btn" onclick="saveNote()">💾 保存笔记</button>
                    <button class="btn" onclick="clearNoteInput()">🗑️ 清空</button>
                </div>
                <hr style="margin: 20px 0;">
                <h4>📚 历史笔记</h4>
                <div id="notesList" class="notes-list">
                    <p style="text-align: center; color: #666; padding: 20px;">暂无笔记记录</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        let studyData = {
            startDate: new Date().toISOString().split('T')[0],
            completedChapters: [],
            studyDays: 0,
            totalHours: 0,
            notes: [],
            currentRound: 0  // 当前学习轮次
        };

        // 加载数据
        function loadData() {
            const saved = localStorage.getItem('cppStudyData');
            if (saved) {
                const loadedData = JSON.parse(saved);

                // 合并数据，确保所有字段都有默认值
                studyData = {
                    startDate: loadedData.startDate || new Date().toISOString().split('T')[0],
                    completedChapters: loadedData.completedChapters || [],
                    studyDays: loadedData.studyDays || 0,
                    totalHours: loadedData.totalHours || 0,
                    notes: loadedData.notes || [],
                    currentRound: loadedData.currentRound || 0
                };

                // 恢复复选框状态
                studyData.completedChapters.forEach(index => {
                    const chapters = document.querySelectorAll('.chapter');
                    if (chapters[index]) {
                        const checkbox = chapters[index].querySelector('input[type="checkbox"]');
                        checkbox.checked = true;
                        chapters[index].classList.add('completed');
                    }
                });

                updateProgress();
            }
        }

        // 保存数据
        function saveData() {
            localStorage.setItem('cppStudyData', JSON.stringify(studyData));
        }

        // 更新进度
        function updateProgress() {
            const chapters = document.querySelectorAll('.chapter');
            const checkboxes = document.querySelectorAll('.chapter input[type="checkbox"]');

            // 更新完成状态
            studyData.completedChapters = [];
            checkboxes.forEach((checkbox, index) => {
                if (checkbox.checked) {
                    studyData.completedChapters.push(index);
                    chapters[index].classList.add('completed');
                } else {
                    chapters[index].classList.remove('completed');
                }
            });

            // 计算进度
            const completed = studyData.completedChapters.length;
            const total = chapters.length;
            const percentage = Math.round((completed / total) * 100);

            // 计算学习天数
            const startDate = new Date(studyData.startDate);
            const today = new Date();
            const diffTime = Math.abs(today - startDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            // 更新显示
            document.getElementById('progress').textContent = percentage + '%';
            document.getElementById('completed').textContent = `${completed}/${total}`;
            document.getElementById('days').textContent = diffDays;

            // 更新统计信息
            updateStats();
            updateHomeProgress();

            // 保存数据
            saveData();
        }

        // 加载章节
        function loadChapter(button) {
            const chapter = button.closest('.chapter');
            const fileName = chapter.dataset.file;
            const chapterTitle = chapter.querySelector('.chapter-title-text').textContent.trim();

            console.log('加载章节:', fileName);

            // 直接切换到章节阅读模式
            document.getElementById('homeContent').style.display = 'none';
            document.getElementById('welcome').style.display = 'none';
            document.getElementById('chapterReader').style.display = 'block';
            document.getElementById('contentHeader').style.display = 'flex';

            // 更新头部标题
            const currentChapter = document.getElementById('currentChapter');
            currentChapter.textContent = chapterTitle;

            // 清除之前的iframe
            const chapterReader = document.getElementById('chapterReader');
            chapterReader.innerHTML = '';

            // 创建新的iframe
            const iframe = document.createElement('iframe');
            iframe.style.width = '100%';
            iframe.style.height = 'calc(100vh - 100px)';
            iframe.style.minHeight = '600px';
            iframe.style.border = 'none';
            iframe.style.display = 'block';
            iframe.style.borderRadius = '12px';
            iframe.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.12)';
            iframe.style.background = 'white';

            // 添加加载事件监听
            iframe.onload = function() {
                console.log('章节加载完成:', fileName);

                // 添加居中样式到iframe内容
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const writeElement = iframeDoc.getElementById('write');
                    if (writeElement) {
                        writeElement.style.maxWidth = '1200px';
                        writeElement.style.margin = '0 auto';
                        writeElement.style.paddingLeft = '20px';
                        writeElement.style.paddingRight = '20px';
                    }

                    // 也可以通过添加CSS样式的方式
                    const style = iframeDoc.createElement('style');
                    style.textContent = `
                        #write {
                            max-width: 1200px !important;
                            margin: 0 auto !important;
                            padding-left: 20px !important;
                            padding-right: 20px !important;
                        }
                        body {
                            background: #f8f9fa !important;
                        }
                    `;
                    iframeDoc.head.appendChild(style);
                } catch (e) {
                    console.log('无法访问iframe内容，可能是跨域限制');
                }
            };

            iframe.onerror = function() {
                console.error('加载章节失败:', fileName);
                chapterReader.innerHTML = `
                    <div style="
                        padding: 40px;
                        text-align: center;
                        color: #666;
                        background: white;
                        border-radius: 12px;
                        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
                        margin: 20px;
                    ">
                        <div style="font-size: 48px; margin-bottom: 20px;">😔</div>
                        <h3 style="color: #dc3545; margin-bottom: 15px;">加载失败</h3>
                        <p style="margin-bottom: 10px;">无法加载章节文件: <code>${fileName}</code></p>
                        <p style="color: #999;">请检查文件是否存在</p>
                        <button onclick="closeReader()" style="
                            margin-top: 20px;
                            padding: 10px 20px;
                            background: #667eea;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                        ">返回首页</button>
                    </div>
                `;
            };

            chapterReader.appendChild(iframe);
            iframe.src = fileName;

            // 高亮当前章节
            document.querySelectorAll('.chapter').forEach(ch => ch.classList.remove('active'));
            chapter.classList.add('active');
        }

        // 在新窗口打开章节
        function openInNewWindow(button) {
            const chapter = button.closest('.chapter');
            const fileName = chapter.dataset.file;
            const chapterTitle = chapter.querySelector('.chapter-title-text').textContent.trim();

            console.log('在新窗口打开章节:', fileName);

            // 在新窗口中打开文件
            window.open(fileName, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

            // 高亮当前章节
            document.querySelectorAll('.chapter').forEach(ch => ch.classList.remove('active'));
            chapter.classList.add('active');
        }

        // 关闭阅读器
        function closeReader() {
            const contentHeader = document.getElementById('contentHeader');
            const chapterReader = document.getElementById('chapterReader');
            const welcome = document.getElementById('welcome');
            const homeContent = document.getElementById('homeContent');
            const iframe = chapterReader.querySelector('iframe');

            // 直接切换显示
            contentHeader.style.display = 'none';
            chapterReader.style.display = 'none';

            // 显示欢迎界面或首页内容
            if (homeContent && homeContent.children.length > 0) {
                homeContent.style.display = 'block';
            } else {
                welcome.style.display = 'flex';
            }

            // 清理iframe
            if (iframe) {
                iframe.remove();
            }

            // 移除高亮
            document.querySelectorAll('.chapter').forEach(ch => ch.classList.remove('active'));
        }

        // 更新统计信息
        function updateStats() {
            // 确保数值安全
            const studyDays = Number(studyData.studyDays) || 0;
            const totalHours = Number(studyData.totalHours) || 0;

            document.getElementById('studyDays').textContent = studyDays;
            document.getElementById('totalHours').textContent = totalHours.toFixed(1) + 'h';

            // 计算平均每日学习时长
            const avgHours = studyDays > 0 ? (totalHours / studyDays).toFixed(1) : '0.0';
            document.getElementById('avgHours').textContent = avgHours + 'h';

            // 更新当前阶段
            const completed = studyData.completedChapters ? studyData.completedChapters.length : 0;
            let stage = '🔰 基础入门';
            if (completed >= 8) stage = '🏆 专家级别';
            else if (completed >= 5) stage = '🚀 进阶阶段';
            else if (completed >= 2) stage = '📈 成长阶段';

            document.getElementById('currentStage').textContent = stage;
        }

        // 增加学习天数
        function addStudyDay() {
            studyData.studyDays = (Number(studyData.studyDays) || 0) + 1;
            updateStats();
            saveData();
            alert(`学习天数已更新为 ${studyData.studyDays} 天！`);
        }

        // 更新学习时长
        function updateStudyHours() {
            const hours = prompt('请输入今天的学习时长（小时）：', '2');
            if (hours && !isNaN(hours) && parseFloat(hours) > 0) {
                studyData.totalHours = (Number(studyData.totalHours) || 0) + parseFloat(hours);
                updateStats();
                saveData();
                alert(`已添加 ${hours} 小时学习时长！总时长：${studyData.totalHours.toFixed(1)}小时`);
            } else if (hours !== null) {
                alert('请输入有效的学习时长（大于0的数字）');
            }
        }

        // 显示学习笔记
        function showNotes() {
            document.getElementById('notesModal').style.display = 'block';
            loadNotes();
        }

        // 关闭笔记模态框
        function closeNotesModal() {
            document.getElementById('notesModal').style.display = 'none';
        }

        // 保存笔记
        function saveNote() {
            const noteText = document.getElementById('noteInput').value.trim();
            if (noteText) {
                const note = {
                    id: Date.now(),
                    content: noteText,
                    date: new Date().toLocaleString('zh-CN')
                };
                studyData.notes.unshift(note);
                saveData();
                loadNotes();
                document.getElementById('noteInput').value = '';
                alert('笔记保存成功！');
            }
        }

        // 清空笔记输入
        function clearNoteInput() {
            document.getElementById('noteInput').value = '';
        }

        // 加载笔记列表
        function loadNotes() {
            const notesList = document.getElementById('notesList');
            if (studyData.notes.length === 0) {
                notesList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无笔记记录</p>';
                return;
            }

            const notesHtml = studyData.notes.map(note => `
                <div class="note-item">
                    <div class="note-date">${note.date}</div>
                    <div class="note-content">${note.content}</div>
                    <button class="btn btn-danger" style="margin-top: 10px; font-size: 11px;" onclick="deleteNote(${note.id})">删除</button>
                </div>
            `).join('');

            notesList.innerHTML = notesHtml;
        }

        // 删除笔记
        function deleteNote(noteId) {
            if (confirm('确定要删除这条笔记吗？')) {
                studyData.notes = studyData.notes.filter(note => note.id !== noteId);
                saveData();
                loadNotes();
            }
        }

        // 导出进度
        function exportProgress() {
            const exportData = {
                导出时间: new Date().toLocaleString('zh-CN'),
                学习开始日期: studyData.startDate,
                已学习天数: studyData.studyDays,
                总学习时长: studyData.totalHours + '小时',
                完成进度: Math.round((studyData.completedChapters.length / 11) * 100) + '%',
                已完成章节: studyData.completedChapters.length + '/11',
                笔记数量: studyData.notes.length + '条'
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `C++学习进度_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
        }

        // 重置进度
        function resetProgress() {
            if (confirm('确定要重置所有学习进度吗？此操作不可恢复！')) {
                localStorage.removeItem('cppStudyData');
                location.reload();
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('notesModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // 显示首页
        function showHomePage() {
            document.getElementById('chapterList').style.display = 'none';
            document.getElementById('homeContent').style.display = 'block';
            document.getElementById('chapterReader').style.display = 'none';
            document.getElementById('welcome').style.display = 'none';

            // 隐藏内容头部
            document.getElementById('contentHeader').style.display = 'none';

            // 更新首页的进度显示
            updateHomeProgress();
        }

        // 显示章节列表
        function showChapterList() {
            document.getElementById('chapterList').style.display = 'block';
            document.getElementById('homeContent').style.display = 'none';
            document.getElementById('chapterReader').style.display = 'none';
            document.getElementById('welcome').style.display = 'block';

            // 隐藏内容头部
            document.getElementById('contentHeader').style.display = 'none';

            // 更新欢迎界面的统计数据
            updateWelcomeStats();
        }

        // 更新欢迎界面统计数据
        function updateWelcomeStats() {
            const completed = studyData.completedChapters ? studyData.completedChapters.length : 0;
            const percentage = Math.round((completed / 11) * 100);
            const studyDays = Number(studyData.studyDays) || 0;
            const totalHours = Number(studyData.totalHours) || 0;

            // 更新统计显示
            const welcomeProgress = document.getElementById('welcomeProgress');
            const welcomeDays = document.getElementById('welcomeDays');
            const welcomeHours = document.getElementById('welcomeHours');

            if (welcomeProgress) welcomeProgress.textContent = percentage + '%';
            if (welcomeDays) welcomeDays.textContent = studyDays;
            if (welcomeHours) welcomeHours.textContent = totalHours.toFixed(1) + 'h';
        }

        // 更新首页进度显示
        function updateHomeProgress() {
            const completed = studyData.completedChapters ? studyData.completedChapters.length : 0;
            const percentage = Math.round((completed / 11) * 100);
            const studyDays = Number(studyData.studyDays) || 0;
            const totalHours = Number(studyData.totalHours) || 0;

            document.getElementById('homeProgress').textContent = percentage + '%';
            document.getElementById('homeCompleted').textContent = `${completed}/11`;
            document.getElementById('homeDays').textContent = studyDays;
            document.getElementById('homeHours').textContent = totalHours.toFixed(1) + 'h';

            // 更新学习轮次显示
            updateLearningRoundDisplay();
        }

        // 设置学习轮次
        function setLearningRound(round) {
            studyData.currentRound = round;
            saveData();
            updateLearningRoundDisplay();

            // 显示对应的学习建议
            const tips = {
                1: "🚀 第一轮：快速浏览各章节的基础概念，重点理解整体框架，不要纠结细节",
                2: "🔍 第二轮：深入学习每个概念的原理和机制，做好笔记，多思考为什么",
                3: "⚡ 第三轮：掌握现代C++特性，理解最佳实践，关注性能和安全性",
                4: "🏗️ 第四轮：通过实际项目综合运用知识，重点关注代码组织和工程实践"
            };

            document.getElementById('currentRoundTip').textContent = tips[round] || "请选择您当前的学习轮次";
        }

        // 更新学习轮次显示
        function updateLearningRoundDisplay() {
            // 重置所有按钮样式
            document.querySelectorAll('.learning-round-btn').forEach(btn => {
                btn.style.opacity = '0.6';
                btn.style.transform = 'scale(1)';
            });

            // 高亮当前轮次
            if (studyData.currentRound > 0) {
                const currentBtn = document.querySelector(`[data-round="${studyData.currentRound}"]`);
                if (currentBtn) {
                    currentBtn.style.opacity = '1';
                    currentBtn.style.transform = 'scale(1.05)';
                }
            }
        }

        // 批量更新章节按钮
        function updateChapterButtons() {
            const chapters = document.querySelectorAll('.chapter');
            chapters.forEach(chapter => {
                const readBtn = chapter.querySelector('.read-btn');
                // 检查是否已经是按钮组，避免重复处理
                if (readBtn && !readBtn.parentElement.classList.contains('btn-group') && readBtn.textContent.includes('📖 阅读')) {
                    const btnGroup = document.createElement('div');
                    btnGroup.className = 'btn-group';

                    // 创建内嵌阅读按钮
                    const embedBtn = readBtn.cloneNode(true);
                    embedBtn.textContent = '📖 内嵌阅读';

                    // 创建新窗口按钮
                    const newWindowBtn = readBtn.cloneNode(true);
                    newWindowBtn.textContent = '🔗 新窗口';
                    newWindowBtn.style.background = '#28a745';
                    newWindowBtn.onclick = function() { openInNewWindow(this); };

                    btnGroup.appendChild(embedBtn);
                    btnGroup.appendChild(newWindowBtn);

                    readBtn.parentElement.replaceChild(btnGroup, readBtn);
                }
            });
        }

        // 页面加载时初始化
        window.addEventListener('DOMContentLoaded', function() {
            loadData();
            updateStats();
            showHomePage(); // 默认显示首页
        });
    </script>

    <style>
        .chapter.active {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
    </style>
</body>
</html>
