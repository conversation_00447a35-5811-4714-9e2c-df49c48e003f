# C++ 并发编程：从入门到专业实践的权威指南

本指南旨在为您构建一个关于 C++ 并发编程的坚实、专业且现代的知识体系。我们将遵循一条从基础概念到高级实践的清晰路径，确保每一个知识点都得到深入的探讨，并融入权威书籍的核心思想与业界的最佳实践。

---

## Part 0: 快速入门——30分钟掌握并发编程核心

> **写给初学者**：本章将通过生动的实例，带您无痛入门C++并发编程的核心概念。

### 0.1 并发vs并行：理解多任务的本质

**核心概念**：
- **并发(Concurrency)**：多个任务在同一时间段内交替执行，看起来同时进行
- **并行(Parallelism)**：多个任务在同一时刻真正同时执行，需要多核处理器

```cpp
#include <iostream>
#include <thread>
#include <chrono>

// 简单的并发示例
void print_numbers(const std::string& prefix, int count) {
    for (int i = 1; i <= count; ++i) {
        std::cout << prefix << i << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

int main() {
    std::cout << "=== Sequential Execution ===" << std::endl;
    print_numbers("A", 5);
    print_numbers("B", 5);
    
    std::cout << "\n=== Concurrent Execution ===" << std::endl;
    // 创建两个线程并发执行
    std::thread t1(print_numbers, "Thread1-", 5);
    std::thread t2(print_numbers, "Thread2-", 5);
    
    // 等待线程完成
    t1.join();
    t2.join();
    
    std::cout << "All threads completed!" << std::endl;
    return 0;
}
```

### 0.2 线程基础：程序的多条执行路径

```cpp
#include <iostream>
#include <thread>
#include <vector>

// 线程函数：计算数组元素之和
void calculate_sum(const std::vector<int>& data, int start, int end, long long& result) {
    result = 0;
    for (int i = start; i < end; ++i) {
        result += data[i];
    }
    
    std::cout << "Thread " << std::this_thread::get_id() 
              << " calculated sum from " << start << " to " << end-1 
              << ": " << result << std::endl;
}

void demonstrate_basic_threading() {
    // 创建大量数据
    std::vector<int> data(1000000);
    for (int i = 0; i < data.size(); ++i) {
        data[i] = i + 1;
    }
    
    // 单线程计算
    auto start_time = std::chrono::high_resolution_clock::now();
    long long single_thread_sum = 0;
    calculate_sum(data, 0, data.size(), single_thread_sum);
    auto end_time = std::chrono::high_resolution_clock::now();
    auto single_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // 多线程计算
    const int num_threads = 4;
    std::vector<std::thread> threads;
    std::vector<long long> partial_sums(num_threads);
    int chunk_size = data.size() / num_threads;
    
    start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_threads; ++i) {
        int start = i * chunk_size;
        int end = (i == num_threads - 1) ? data.size() : (i + 1) * chunk_size;
        
        threads.emplace_back(calculate_sum, std::cref(data), start, end, std::ref(partial_sums[i]));
    }
    
    // 等待所有线程完成
    for (auto& t : threads) {
        t.join();
    }
    
    // 合并结果
    long long multi_thread_sum = 0;
    for (long long sum : partial_sums) {
        multi_thread_sum += sum;
    }
    
    end_time = std::chrono::high_resolution_clock::now();
    auto multi_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "\n=== Results ===" << std::endl;
    std::cout << "Single thread sum: " << single_thread_sum << " (Time: " << single_duration.count() << "ms)" << std::endl;
    std::cout << "Multi thread sum: " << multi_thread_sum << " (Time: " << multi_duration.count() << "ms)" << std::endl;
    std::cout << "Speedup: " << static_cast<double>(single_duration.count()) / multi_duration.count() << "x" << std::endl;
}
```

### 0.3 同步机制：协调多线程访问

```cpp
#include <iostream>
#include <thread>
#include <mutex>
#include <vector>

// 不安全的计数器
class UnsafeCounter {
private:
    int count = 0;
    
public:
    void increment() {
        ++count;  // 非原子操作，多线程不安全
    }
    
    int get() const { return count; }
};

// 安全的计数器
class SafeCounter {
private:
    int count = 0;
    mutable std::mutex mtx;  // 互斥锁
    
public:
    void increment() {
        std::lock_guard<std::mutex> lock(mtx);  // RAII锁管理
        ++count;  // 现在是线程安全的
    }
    
    int get() const {
        std::lock_guard<std::mutex> lock(mtx);
        return count;
    }
};

void test_counter_safety() {
    const int num_threads = 10;
    const int increments_per_thread = 10000;
    
    // 测试不安全的计数器
    UnsafeCounter unsafe_counter;
    std::vector<std::thread> unsafe_threads;
    
    for (int i = 0; i < num_threads; ++i) {
        unsafe_threads.emplace_back([&unsafe_counter, increments_per_thread]() {
            for (int j = 0; j < increments_per_thread; ++j) {
                unsafe_counter.increment();
            }
        });
    }
    
    for (auto& t : unsafe_threads) {
        t.join();
    }
    
    // 测试安全的计数器
    SafeCounter safe_counter;
    std::vector<std::thread> safe_threads;
    
    for (int i = 0; i < num_threads; ++i) {
        safe_threads.emplace_back([&safe_counter, increments_per_thread]() {
            for (int j = 0; j < increments_per_thread; ++j) {
                safe_counter.increment();
            }
        });
    }
    
    for (auto& t : safe_threads) {
        t.join();
    }
    
    std::cout << "Expected count: " << num_threads * increments_per_thread << std::endl;
    std::cout << "Unsafe counter: " << unsafe_counter.get() << std::endl;
    std::cout << "Safe counter: " << safe_counter.get() << std::endl;
}
```

### 0.4 异步编程：future和promise

```cpp
#include <iostream>
#include <future>
#include <chrono>
#include <random>

// 模拟耗时计算
int expensive_calculation(int n) {
    std::this_thread::sleep_for(std::chrono::seconds(1));
    return n * n;
}

// 模拟网络请求
std::string fetch_data(const std::string& url) {
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    return "Data from " + url;
}

void demonstrate_async_programming() {
    std::cout << "=== Async Programming Demo ===" << std::endl;
    
    // 1. 使用std::async启动异步任务
    auto future1 = std::async(std::launch::async, expensive_calculation, 10);
    auto future2 = std::async(std::launch::async, expensive_calculation, 20);
    auto future3 = std::async(std::launch::async, fetch_data, "https://api.example.com");
    
    std::cout << "Tasks started, doing other work..." << std::endl;
    
    // 在等待结果的同时可以做其他工作
    for (int i = 0; i < 3; ++i) {
        std::cout << "Main thread working... " << i << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(300));
    }
    
    // 获取结果
    std::cout << "Getting results:" << std::endl;
    std::cout << "Calculation 1 result: " << future1.get() << std::endl;
    std::cout << "Calculation 2 result: " << future2.get() << std::endl;
    std::cout << "Network data: " << future3.get() << std::endl;
    
    // 2. 使用promise和future进行线程间通信
    std::promise<int> promise;
    std::future<int> future = promise.get_future();
    
    std::thread worker([&promise]() {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        promise.set_value(42);  // 设置结果
    });
    
    std::cout << "Waiting for worker thread..." << std::endl;
    int result = future.get();  // 阻塞等待结果
    std::cout << "Worker thread result: " << result << std::endl;
    
    worker.join();
}
```

> **快速入门总结**：C++并发编程的核心是线程、同步和异步。线程提供并发执行能力，同步机制确保数据安全，异步编程提高程序响应性。掌握这些基础概念是进入并发编程世界的第一步。

> ---
> ⚠️ **【给初学者的黄金法则】**
> 1. **线程安全第一**：共享数据必须使用同步机制保护
> 2. **避免数据竞争**：多线程访问同一数据时要加锁
> 3. **使用RAII管理锁**：`std::lock_guard`自动管理锁的生命周期
> 4. **优先使用高级抽象**：`std::async`比手动创建线程更安全
> 5. **注意死锁问题**：获取多个锁时要保持一致的顺序
> ---

---

## 第一部分：线程管理的深度解析 (Thread Management Mastery)

### 1.1 线程的生命周期：从创建到销毁

**概念讲解**：
线程的生命周期管理是并发编程的基础。理解线程的创建、执行、同步和销毁过程，对于编写正确的并发程序至关重要。

**【深度解析】线程创建的多种方式**
```cpp
#include <iostream>
#include <thread>
#include <functional>

class ThreadCreationDemo {
private:
    int member_data = 100;
    
public:
    // 成员函数作为线程函数
    void member_function(int param) {
        std::cout << "Member function called with param: " << param 
                  << ", member_data: " << member_data << std::endl;
    }
    
    static void demonstrate_thread_creation() {
        std::cout << "=== Thread Creation Methods ===" << std::endl;
        
        // 1. 普通函数
        auto func = [](int n) {
            for (int i = 0; i < n; ++i) {
                std::cout << "Lambda thread: " << i << std::endl;
            }
        };
        std::thread t1(func, 3);
        
        // 2. 函数对象
        struct Functor {
            void operator()(const std::string& msg) {
                std::cout << "Functor thread: " << msg << std::endl;
            }
        };
        std::thread t2(Functor{}, "Hello from functor");
        
        // 3. 成员函数
        ThreadCreationDemo demo;
        std::thread t3(&ThreadCreationDemo::member_function, &demo, 42);
        
        // 4. std::bind
        auto bound_func = std::bind(&ThreadCreationDemo::member_function, &demo, std::placeholders::_1);
        std::thread t4(bound_func, 99);
        
        // 等待所有线程完成
        t1.join();
        t2.join();
        t3.join();
        t4.join();
        
        std::cout << "All threads completed" << std::endl;
    }
};
```

**【深度解析】线程的join和detach**
```cpp
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>

class ThreadLifecycleDemo {
public:
    static void demonstrate_join_detach() {
        std::cout << "=== Join vs Detach ===" << std::endl;

        // 1. join() - 等待线程完成
        std::thread joinable_thread([]() {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            std::cout << "Joinable thread completed" << std::endl;
        });

        std::cout << "Waiting for joinable thread..." << std::endl;
        joinable_thread.join();  // 主线程等待
        std::cout << "Joinable thread joined" << std::endl;

        // 2. detach() - 分离线程
        std::thread detached_thread([]() {
            std::this_thread::sleep_for(std::chrono::seconds(2));
            std::cout << "Detached thread completed" << std::endl;
        });

        detached_thread.detach();  // 分离线程，主线程不等待
        std::cout << "Thread detached, continuing..." << std::endl;

        // 注意：程序结束时分离的线程可能还在运行
        std::this_thread::sleep_for(std::chrono::seconds(3));
    }

    // RAII线程管理器
    class ThreadGuard {
    private:
        std::thread& t;

    public:
        explicit ThreadGuard(std::thread& t_) : t(t_) {}

        ~ThreadGuard() {
            if (t.joinable()) {
                t.join();
            }
        }

        ThreadGuard(const ThreadGuard&) = delete;
        ThreadGuard& operator=(const ThreadGuard&) = delete;
    };

    static void demonstrate_raii_thread_management() {
        std::cout << "\n=== RAII Thread Management ===" << std::endl;

        std::thread t([]() {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            std::cout << "RAII managed thread completed" << std::endl;
        });

        ThreadGuard guard(t);  // RAII管理

        // 即使发生异常，ThreadGuard的析构函数也会确保线程被join
        std::cout << "Thread is safely managed by RAII" << std::endl;

        // guard析构时自动join线程
    }
};
```

---

## 第二部分：同步机制的深度解析 (Synchronization Mechanisms)

### 2.1 互斥锁：基础同步原语

**概念讲解**：
互斥锁(Mutex)是最基本的同步原语，用于保护共享资源免受并发访问的影响。C++提供了多种类型的互斥锁，每种都有其特定的用途和性能特征。

**【深度解析】不同类型的互斥锁**
```cpp
#include <iostream>
#include <thread>
#include <mutex>
#include <shared_mutex>
#include <chrono>
#include <vector>

class MutexDemo {
private:
    mutable std::mutex basic_mutex;
    mutable std::recursive_mutex recursive_mutex;
    mutable std::timed_mutex timed_mutex;
    mutable std::shared_mutex shared_mutex;

    int shared_data = 0;

public:
    // 1. 基本互斥锁
    void basic_mutex_example() {
        std::lock_guard<std::mutex> lock(basic_mutex);
        ++shared_data;
        std::cout << "Basic mutex: " << shared_data << std::endl;
    }

    // 2. 递归互斥锁
    void recursive_function(int depth) {
        std::lock_guard<std::recursive_mutex> lock(recursive_mutex);

        if (depth > 0) {
            std::cout << "Recursive depth: " << depth << std::endl;
            recursive_function(depth - 1);  // 同一线程可以多次获取锁
        }
    }

    // 3. 定时互斥锁
    void timed_mutex_example() {
        if (timed_mutex.try_lock_for(std::chrono::milliseconds(100))) {
            std::cout << "Acquired timed mutex" << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            timed_mutex.unlock();
        } else {
            std::cout << "Failed to acquire timed mutex" << std::endl;
        }
    }

    // 4. 共享互斥锁（读写锁）
    void reader_function(int id) {
        std::shared_lock<std::shared_mutex> lock(shared_mutex);  // 共享锁（读锁）
        std::cout << "Reader " << id << " reading: " << shared_data << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    void writer_function(int id) {
        std::unique_lock<std::shared_mutex> lock(shared_mutex);  // 独占锁（写锁）
        ++shared_data;
        std::cout << "Writer " << id << " wrote: " << shared_data << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    static void demonstrate_mutex_types() {
        MutexDemo demo;

        std::cout << "=== Mutex Types Demo ===" << std::endl;

        // 测试基本互斥锁
        std::vector<std::thread> threads;
        for (int i = 0; i < 3; ++i) {
            threads.emplace_back(&MutexDemo::basic_mutex_example, &demo);
        }
        for (auto& t : threads) t.join();
        threads.clear();

        // 测试递归互斥锁
        std::thread recursive_thread(&MutexDemo::recursive_function, &demo, 3);
        recursive_thread.join();

        // 测试定时互斥锁
        for (int i = 0; i < 3; ++i) {
            threads.emplace_back(&MutexDemo::timed_mutex_example, &demo);
        }
        for (auto& t : threads) t.join();
        threads.clear();

        // 测试共享互斥锁
        for (int i = 0; i < 3; ++i) {
            threads.emplace_back(&MutexDemo::reader_function, &demo, i);
        }
        threads.emplace_back(&MutexDemo::writer_function, &demo, 1);
        for (int i = 0; i < 2; ++i) {
            threads.emplace_back(&MutexDemo::reader_function, &demo, i + 3);
        }

        for (auto& t : threads) t.join();
    }
};
```

### 2.2 条件变量：线程间的通信机制

**概念讲解**：
条件变量是一种同步原语，允许线程等待某个条件成立。它通常与互斥锁配合使用，实现高效的线程间通信。

**【深度解析】生产者-消费者模式**
```cpp
#include <iostream>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <chrono>

template<typename T>
class ThreadSafeQueue {
private:
    mutable std::mutex mtx;
    std::queue<T> data_queue;
    std::condition_variable data_cond;

public:
    void push(T item) {
        std::lock_guard<std::mutex> lock(mtx);
        data_queue.push(item);
        data_cond.notify_one();  // 通知等待的线程
    }

    bool try_pop(T& item) {
        std::lock_guard<std::mutex> lock(mtx);
        if (data_queue.empty()) {
            return false;
        }
        item = data_queue.front();
        data_queue.pop();
        return true;
    }

    void wait_and_pop(T& item) {
        std::unique_lock<std::mutex> lock(mtx);
        // 等待直到队列非空
        data_cond.wait(lock, [this] { return !data_queue.empty(); });
        item = data_queue.front();
        data_queue.pop();
    }

    bool empty() const {
        std::lock_guard<std::mutex> lock(mtx);
        return data_queue.empty();
    }

    size_t size() const {
        std::lock_guard<std::mutex> lock(mtx);
        return data_queue.size();
    }
};

void demonstrate_producer_consumer() {
    std::cout << "=== Producer-Consumer Demo ===" << std::endl;

    ThreadSafeQueue<int> queue;
    bool finished = false;
    std::mutex finished_mutex;

    // 生产者线程
    std::thread producer([&queue, &finished, &finished_mutex]() {
        for (int i = 0; i < 10; ++i) {
            queue.push(i);
            std::cout << "Produced: " << i << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        {
            std::lock_guard<std::mutex> lock(finished_mutex);
            finished = true;
        }
        std::cout << "Producer finished" << std::endl;
    });

    // 消费者线程
    std::thread consumer([&queue, &finished, &finished_mutex]() {
        int item;
        while (true) {
            if (queue.try_pop(item)) {
                std::cout << "Consumed: " << item << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(150));
            } else {
                // 检查是否完成
                {
                    std::lock_guard<std::mutex> lock(finished_mutex);
                    if (finished && queue.empty()) {
                        break;
                    }
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
        std::cout << "Consumer finished" << std::endl;
    });

    producer.join();
    consumer.join();
}
```

### 2.3 原子操作：无锁编程的基础

**概念讲解**：
原子操作是不可分割的操作，在多线程环境中可以安全地访问共享数据而无需使用锁。C++11引入的原子类型为无锁编程提供了基础。

**【深度解析】原子操作的应用**
```cpp
#include <iostream>
#include <thread>
#include <atomic>
#include <vector>
#include <chrono>

class AtomicDemo {
private:
    std::atomic<int> atomic_counter{0};
    std::atomic<bool> ready{false};
    std::atomic<std::string*> atomic_ptr{nullptr};

public:
    void demonstrate_atomic_operations() {
        std::cout << "=== Atomic Operations Demo ===" << std::endl;

        const int num_threads = 4;
        const int increments_per_thread = 10000;

        std::vector<std::thread> threads;

        // 使用原子操作的线程安全计数
        auto start_time = std::chrono::high_resolution_clock::now();

        for (int i = 0; i < num_threads; ++i) {
            threads.emplace_back([this, increments_per_thread]() {
                for (int j = 0; j < increments_per_thread; ++j) {
                    atomic_counter.fetch_add(1, std::memory_order_relaxed);
                }
            });
        }

        for (auto& t : threads) {
            t.join();
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        std::cout << "Expected: " << num_threads * increments_per_thread << std::endl;
        std::cout << "Actual: " << atomic_counter.load() << std::endl;
        std::cout << "Time: " << duration.count() << "ms" << std::endl;
    }

    void demonstrate_memory_ordering() {
        std::cout << "\n=== Memory Ordering Demo ===" << std::endl;

        std::atomic<int> data{0};
        std::atomic<bool> flag{false};

        // 写线程
        std::thread writer([&data, &flag]() {
            data.store(42, std::memory_order_relaxed);
            flag.store(true, std::memory_order_release);  // 释放语义
        });

        // 读线程
        std::thread reader([&data, &flag]() {
            while (!flag.load(std::memory_order_acquire)) {  // 获取语义
                std::this_thread::yield();
            }
            std::cout << "Data read: " << data.load(std::memory_order_relaxed) << std::endl;
        });

        writer.join();
        reader.join();
    }

    // 无锁栈实现（简化版）
    template<typename T>
    class LockFreeStack {
    private:
        struct Node {
            T data;
            Node* next;
            Node(T const& data_) : data(data_) {}
        };

        std::atomic<Node*> head;

    public:
        void push(T const& data) {
            Node* const new_node = new Node(data);
            new_node->next = head.load();
            while (!head.compare_exchange_weak(new_node->next, new_node));
        }

        bool pop(T& result) {
            Node* old_head = head.load();
            while (old_head && !head.compare_exchange_weak(old_head, old_head->next));
            if (old_head) {
                result = old_head->data;
                delete old_head;
                return true;
            }
            return false;
        }
    };

    void demonstrate_lock_free_stack() {
        std::cout << "\n=== Lock-Free Stack Demo ===" << std::endl;

        LockFreeStack<int> stack;

        // 多个线程同时push
        std::vector<std::thread> pushers;
        for (int i = 0; i < 4; ++i) {
            pushers.emplace_back([&stack, i]() {
                for (int j = 0; j < 5; ++j) {
                    stack.push(i * 10 + j);
                    std::cout << "Pushed: " << i * 10 + j << std::endl;
                }
            });
        }

        // 多个线程同时pop
        std::vector<std::thread> poppers;
        for (int i = 0; i < 2; ++i) {
            poppers.emplace_back([&stack, i]() {
                int value;
                for (int j = 0; j < 10; ++j) {
                    if (stack.pop(value)) {
                        std::cout << "Popper " << i << " got: " << value << std::endl;
                    }
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
            });
        }

        for (auto& t : pushers) t.join();
        for (auto& t : poppers) t.join();
    }
};
```

---

## 第三部分：异步编程的深度实践 (Asynchronous Programming)

### 3.1 std::async：简化的异步执行

**概念讲解**：
std::async提供了一种简单的方式来异步执行函数，它返回一个std::future对象，可以用来获取异步操作的结果。

**【深度解析】async的启动策略**
```cpp
#include <iostream>
#include <future>
#include <chrono>
#include <vector>

class AsyncDemo {
public:
    static int cpu_intensive_task(int n) {
        std::cout << "CPU task " << n << " started on thread "
                  << std::this_thread::get_id() << std::endl;

        // 模拟CPU密集型任务
        int result = 0;
        for (int i = 0; i < n * 1000000; ++i) {
            result += i % 1000;
        }

        std::cout << "CPU task " << n << " completed" << std::endl;
        return result;
    }

    static void demonstrate_launch_policies() {
        std::cout << "=== Launch Policies Demo ===" << std::endl;
        std::cout << "Main thread: " << std::this_thread::get_id() << std::endl;

        // 1. std::launch::async - 强制异步执行
        auto future1 = std::async(std::launch::async, cpu_intensive_task, 100);

        // 2. std::launch::deferred - 延迟执行（调用get时才执行）
        auto future2 = std::async(std::launch::deferred, cpu_intensive_task, 200);

        // 3. std::launch::async | std::launch::deferred - 让实现决定
        auto future3 = std::async(std::launch::async | std::launch::deferred, cpu_intensive_task, 300);

        std::cout << "All tasks launched" << std::endl;

        // 获取结果
        std::cout << "Result 1: " << future1.get() << std::endl;
        std::cout << "Result 2: " << future2.get() << std::endl;  // 这时才开始执行
        std::cout << "Result 3: " << future3.get() << std::endl;
    }

    static void demonstrate_parallel_algorithms() {
        std::cout << "\n=== Parallel Algorithms Demo ===" << std::endl;

        std::vector<int> data(1000000);
        std::iota(data.begin(), data.end(), 1);

        // 并行计算不同部分的和
        const int num_tasks = 4;
        int chunk_size = data.size() / num_tasks;

        std::vector<std::future<long long>> futures;

        auto start_time = std::chrono::high_resolution_clock::now();

        for (int i = 0; i < num_tasks; ++i) {
            int start = i * chunk_size;
            int end = (i == num_tasks - 1) ? data.size() : (i + 1) * chunk_size;

            futures.push_back(std::async(std::launch::async, [&data, start, end]() {
                long long sum = 0;
                for (int j = start; j < end; ++j) {
                    sum += data[j];
                }
                return sum;
            }));
        }

        // 收集结果
        long long total_sum = 0;
        for (auto& future : futures) {
            total_sum += future.get();
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        std::cout << "Parallel sum: " << total_sum << std::endl;
        std::cout << "Time: " << duration.count() << "ms" << std::endl;
    }
};
```

### 3.2 promise和future：精确的异步通信

**概念讲解**：
promise和future提供了一种在线程间传递单个值的机制。promise用于设置值，future用于获取值，它们形成了一个通信通道。

**【深度解析】promise/future的高级用法**
```cpp
#include <iostream>
#include <future>
#include <thread>
#include <exception>

class PromiseFutureDemo {
public:
    static void demonstrate_basic_promise_future() {
        std::cout << "=== Basic Promise/Future Demo ===" << std::endl;

        std::promise<int> promise;
        std::future<int> future = promise.get_future();

        // 在另一个线程中设置值
        std::thread worker([&promise]() {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            promise.set_value(42);
        });

        std::cout << "Waiting for result..." << std::endl;
        int result = future.get();
        std::cout << "Got result: " << result << std::endl;

        worker.join();
    }

    static void demonstrate_exception_handling() {
        std::cout << "\n=== Exception Handling Demo ===" << std::endl;

        std::promise<int> promise;
        std::future<int> future = promise.get_future();

        std::thread worker([&promise]() {
            try {
                // 模拟可能抛出异常的操作
                throw std::runtime_error("Something went wrong!");
                promise.set_value(100);
            } catch (...) {
                promise.set_exception(std::current_exception());
            }
        });

        try {
            int result = future.get();
            std::cout << "Result: " << result << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Caught exception: " << e.what() << std::endl;
        }

        worker.join();
    }

    static void demonstrate_shared_future() {
        std::cout << "\n=== Shared Future Demo ===" << std::endl;

        std::promise<std::string> promise;
        std::shared_future<std::string> shared_future = promise.get_future().share();

        // 多个线程等待同一个结果
        std::vector<std::thread> threads;

        for (int i = 0; i < 3; ++i) {
            threads.emplace_back([shared_future, i]() {
                std::cout << "Thread " << i << " waiting..." << std::endl;
                std::string result = shared_future.get();
                std::cout << "Thread " << i << " got: " << result << std::endl;
            });
        }

        // 延迟设置值
        std::this_thread::sleep_for(std::chrono::seconds(1));
        promise.set_value("Shared result");

        for (auto& t : threads) {
            t.join();
        }
    }

    // 实现一个简单的任务调度器
    class TaskScheduler {
    private:
        std::queue<std::function<void()>> tasks;
        std::mutex tasks_mutex;
        std::condition_variable tasks_cv;
        std::vector<std::thread> workers;
        bool stop = false;

    public:
        explicit TaskScheduler(size_t num_workers) {
            for (size_t i = 0; i < num_workers; ++i) {
                workers.emplace_back([this]() {
                    while (true) {
                        std::function<void()> task;

                        {
                            std::unique_lock<std::mutex> lock(tasks_mutex);
                            tasks_cv.wait(lock, [this] { return stop || !tasks.empty(); });

                            if (stop && tasks.empty()) return;

                            task = std::move(tasks.front());
                            tasks.pop();
                        }

                        task();
                    }
                });
            }
        }

        template<typename F, typename... Args>
        auto submit(F&& f, Args&&... args) -> std::future<typename std::result_of<F(Args...)>::type> {
            using return_type = typename std::result_of<F(Args...)>::type;

            auto task = std::make_shared<std::packaged_task<return_type()>>(
                std::bind(std::forward<F>(f), std::forward<Args>(args)...)
            );

            std::future<return_type> result = task->get_future();

            {
                std::unique_lock<std::mutex> lock(tasks_mutex);
                if (stop) {
                    throw std::runtime_error("submit on stopped TaskScheduler");
                }
                tasks.emplace([task]() { (*task)(); });
            }

            tasks_cv.notify_one();
            return result;
        }

        ~TaskScheduler() {
            {
                std::unique_lock<std::mutex> lock(tasks_mutex);
                stop = true;
            }

            tasks_cv.notify_all();

            for (std::thread& worker : workers) {
                worker.join();
            }
        }
    };

    static void demonstrate_task_scheduler() {
        std::cout << "\n=== Task Scheduler Demo ===" << std::endl;

        TaskScheduler scheduler(3);

        std::vector<std::future<int>> results;

        // 提交多个任务
        for (int i = 0; i < 6; ++i) {
            results.push_back(scheduler.submit([i]() {
                std::cout << "Task " << i << " running on thread "
                          << std::this_thread::get_id() << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                return i * i;
            }));
        }

        // 获取结果
        for (auto& result : results) {
            std::cout << "Task result: " << result.get() << std::endl;
        }
    }
};
```

---

## 附录：并发编程实践指南

### A.1 面试核心问题

1. **什么是数据竞争？如何避免？**
   > 数据竞争是指多个线程同时访问同一内存位置，且至少有一个是写操作，而没有适当的同步。避免方法：使用互斥锁、原子操作、或避免共享可变状态。

2. **死锁是什么？如何预防？**
   > 死锁是指两个或多个线程相互等待对方释放资源而无限期阻塞。预防方法：避免嵌套锁、按固定顺序获取锁、使用超时锁、设计无锁算法。

3. **std::async和std::thread的区别？**
   > std::async更高级，自动管理线程生命周期，支持延迟执行，返回future获取结果；std::thread更底层，需要手动管理join/detach，适合长期运行的线程。

4. **什么是内存序？为什么重要？**
   > 内存序定义了原子操作的同步和排序约束。它影响编译器和CPU的优化，确保多线程程序的正确性。不同的内存序提供不同级别的同步保证和性能特征。

5. **如何设计线程安全的单例模式？**
   > 使用std::call_once、静态局部变量（C++11保证线程安全）、或双重检查锁定模式。现代C++推荐使用静态局部变量方式。

### A.2 权威书籍拓展阅读

*   **《C++ Concurrency in Action (第2版)》**:
    *   **Anthony Williams著**: C++并发编程的权威指南

*   **《Effective Modern C++ (C++11/14)》**:
    *   **条款35-40**: 现代C++并发编程的最佳实践

*   **《C++ Core Guidelines》**:
    *   **CP (Concurrency and Parallelism)**: 并发编程的权威指导原则

### A.3 性能优化指南

**线程数量优化**：
- CPU密集型任务：线程数 = CPU核心数
- I/O密集型任务：线程数 > CPU核心数
- 混合型任务：根据I/O等待时间调整

**锁优化策略**：
- 减少锁的粒度和持有时间
- 使用读写锁分离读写操作
- 考虑无锁数据结构
- 避免锁竞争热点

### A.4 实践挑战

**[初级] 实现线程安全的计数器**
```cpp
// 要求：
// 1. 支持多线程安全的递增/递减
// 2. 提供原子操作和锁两种实现
// 3. 性能测试对比
// 4. 异常安全保证
```

**[中级] 设计生产者-消费者系统**
```cpp
// 要求：
// 1. 支持多生产者多消费者
// 2. 实现有界缓冲区
// 3. 优雅关闭机制
// 4. 性能监控和统计
```

**[高级] 实现并行任务调度器**
```cpp
// 要求：
// 1. 支持任务依赖关系
// 2. 动态负载均衡
// 3. 任务优先级调度
// 4. 异常处理和恢复
```

---

> **总结**：C++并发编程是现代软件开发的核心技能。从基础的线程管理到高级的无锁编程，每个层次都有其适用场景。掌握正确的同步机制、理解内存模型、善用异步编程模式，是编写高性能并发程序的关键。记住：并发编程的目标不仅是正确性，更要追求性能和可维护性的平衡。
```
```
