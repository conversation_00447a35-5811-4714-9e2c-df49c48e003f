#include <iostream>
#include <string>
#include <memory>
#include <vector>

// =============================================================================
// 委托模式演示 - Delegation Pattern
// =============================================================================

// 1. 基础委托示例
class Printer {
public:
    void print(const std::string& content) const {
        std::cout << "Printing: " << content << std::endl;
    }
    
    void printWithHeader(const std::string& content) const {
        std::cout << "=== DOCUMENT ===" << std::endl;
        std::cout << content << std::endl;
        std::cout << "=== END ===" << std::endl;
    }
};

// Document类通过委托使用Printer的功能
class Document {
private:
    std::string content_;
    Printer printer_;  // 委托对象

public:
    Document(const std::string& content) : content_(content) {}
    
    // 委托给printer_对象
    void print() const {
        printer_.print(content_);  // 委托调用
    }
    
    void printFormatted() const {
        printer_.printWithHeader(content_);  // 委托调用
    }
    
    void setContent(const std::string& content) {
        content_ = content;
    }
};

// =============================================================================
// 2. 策略模式中的委托
// =============================================================================

// 排序策略接口
class SortStrategy {
public:
    virtual void sort(std::vector<int>& data) const = 0;
    virtual std::string getName() const = 0;
    virtual ~SortStrategy() = default;
};

// 冒泡排序策略
class BubbleSort : public SortStrategy {
public:
    void sort(std::vector<int>& data) const override {
        std::cout << "Using Bubble Sort..." << std::endl;
        for (size_t i = 0; i < data.size(); ++i) {
            for (size_t j = 0; j < data.size() - 1 - i; ++j) {
                if (data[j] > data[j + 1]) {
                    std::swap(data[j], data[j + 1]);
                }
            }
        }
    }
    
    std::string getName() const override {
        return "Bubble Sort";
    }
};

// 快速排序策略
class QuickSort : public SortStrategy {
public:
    void sort(std::vector<int>& data) const override {
        std::cout << "Using Quick Sort..." << std::endl;
        quickSort(data, 0, data.size() - 1);
    }
    
    std::string getName() const override {
        return "Quick Sort";
    }

private:
    void quickSort(std::vector<int>& arr, int low, int high) const {
        if (low < high) {
            int pi = partition(arr, low, high);
            quickSort(arr, low, pi - 1);
            quickSort(arr, pi + 1, high);
        }
    }
    
    int partition(std::vector<int>& arr, int low, int high) const {
        int pivot = arr[high];
        int i = (low - 1);
        
        for (int j = low; j <= high - 1; j++) {
            if (arr[j] < pivot) {
                i++;
                std::swap(arr[i], arr[j]);
            }
        }
        std::swap(arr[i + 1], arr[high]);
        return (i + 1);
    }
};

// 排序器类 - 通过委托使用不同的排序策略
class Sorter {
private:
    std::unique_ptr<SortStrategy> strategy_;  // 委托对象

public:
    Sorter(std::unique_ptr<SortStrategy> strategy) 
        : strategy_(std::move(strategy)) {}
    
    // 委托给策略对象
    void sort(std::vector<int>& data) const {
        std::cout << "Delegating to: " << strategy_->getName() << std::endl;
        strategy_->sort(data);  // 委托调用
    }
    
    // 运行时更换委托对象
    void setStrategy(std::unique_ptr<SortStrategy> strategy) {
        strategy_ = std::move(strategy);
        std::cout << "Strategy changed to: " << strategy_->getName() << std::endl;
    }
};

// =============================================================================
// 3. 装饰器模式中的委托
// =============================================================================

// 咖啡接口
class Coffee {
public:
    virtual double cost() const = 0;
    virtual std::string description() const = 0;
    virtual ~Coffee() = default;
};

// 基础咖啡
class SimpleCoffee : public Coffee {
public:
    double cost() const override {
        return 2.0;
    }
    
    std::string description() const override {
        return "Simple Coffee";
    }
};

// 装饰器基类
class CoffeeDecorator : public Coffee {
protected:
    std::unique_ptr<Coffee> coffee_;  // 委托对象

public:
    CoffeeDecorator(std::unique_ptr<Coffee> coffee) 
        : coffee_(std::move(coffee)) {}
    
    // 默认委托给被装饰的对象
    double cost() const override {
        return coffee_->cost();
    }
    
    std::string description() const override {
        return coffee_->description();
    }
};

// 牛奶装饰器
class MilkDecorator : public CoffeeDecorator {
public:
    MilkDecorator(std::unique_ptr<Coffee> coffee) 
        : CoffeeDecorator(std::move(coffee)) {}
    
    double cost() const override {
        return coffee_->cost() + 0.5;  // 委托 + 额外功能
    }
    
    std::string description() const override {
        return coffee_->description() + " + Milk";  // 委托 + 额外功能
    }
};

// 糖装饰器
class SugarDecorator : public CoffeeDecorator {
public:
    SugarDecorator(std::unique_ptr<Coffee> coffee) 
        : CoffeeDecorator(std::move(coffee)) {}
    
    double cost() const override {
        return coffee_->cost() + 0.2;  // 委托 + 额外功能
    }
    
    std::string description() const override {
        return coffee_->description() + " + Sugar";  // 委托 + 额外功能
    }
};

// =============================================================================
// 4. 代理模式中的委托
// =============================================================================

// 图片接口
class Image {
public:
    virtual void display() const = 0;
    virtual ~Image() = default;
};

// 真实图片
class RealImage : public Image {
private:
    std::string filename_;

public:
    RealImage(const std::string& filename) : filename_(filename) {
        loadFromDisk();
    }
    
    void display() const override {
        std::cout << "Displaying " << filename_ << std::endl;
    }

private:
    void loadFromDisk() const {
        std::cout << "Loading " << filename_ << " from disk..." << std::endl;
    }
};

// 图片代理 - 延迟加载
class ImageProxy : public Image {
private:
    std::string filename_;
    mutable std::unique_ptr<RealImage> realImage_;  // 委托对象

public:
    ImageProxy(const std::string& filename) : filename_(filename) {}
    
    void display() const override {
        if (!realImage_) {
            std::cout << "Creating real image on first access..." << std::endl;
            realImage_ = std::make_unique<RealImage>(filename_);
        }
        realImage_->display();  // 委托调用
    }
};

// =============================================================================
// 演示函数
// =============================================================================

void demonstrateBasicDelegation() {
    std::cout << "\n=== 基础委托演示 ===" << std::endl;
    
    Document doc("Hello, World!");
    
    std::cout << "普通打印:" << std::endl;
    doc.print();  // 委托给printer_
    
    std::cout << "\n格式化打印:" << std::endl;
    doc.printFormatted();  // 委托给printer_
}

void demonstrateStrategyDelegation() {
    std::cout << "\n=== 策略模式委托演示 ===" << std::endl;
    
    std::vector<int> data = {64, 34, 25, 12, 22, 11, 90};
    
    // 使用冒泡排序策略
    Sorter sorter(std::make_unique<BubbleSort>());
    
    std::cout << "原始数据: ";
    for (int n : data) std::cout << n << " ";
    std::cout << std::endl;
    
    sorter.sort(data);  // 委托给BubbleSort
    
    std::cout << "排序后: ";
    for (int n : data) std::cout << n << " ";
    std::cout << std::endl;
    
    // 运行时更换策略
    data = {64, 34, 25, 12, 22, 11, 90};
    sorter.setStrategy(std::make_unique<QuickSort>());
    sorter.sort(data);  // 委托给QuickSort
}

void demonstrateDecoratorDelegation() {
    std::cout << "\n=== 装饰器模式委托演示 ===" << std::endl;

    // 创建基础咖啡
    std::unique_ptr<Coffee> coffee = std::make_unique<SimpleCoffee>();
    std::cout << coffee->description() << " - $" << coffee->cost() << std::endl;

    // 添加牛奶装饰
    coffee = std::make_unique<MilkDecorator>(std::move(coffee));
    std::cout << coffee->description() << " - $" << coffee->cost() << std::endl;

    // 添加糖装饰
    coffee = std::make_unique<SugarDecorator>(std::move(coffee));
    std::cout << coffee->description() << " - $" << coffee->cost() << std::endl;
}

void demonstrateProxyDelegation() {
    std::cout << "\n=== 代理模式委托演示 ===" << std::endl;
    
    // 使用代理，真实对象延迟创建
    ImageProxy proxy("large_image.jpg");
    
    std::cout << "代理创建完成，真实对象尚未创建" << std::endl;
    
    std::cout << "\n第一次访问:" << std::endl;
    proxy.display();  // 此时才创建真实对象并委托
    
    std::cout << "\n第二次访问:" << std::endl;
    proxy.display();  // 直接委托给已存在的真实对象
}

int main() {
    std::cout << "委托模式演示" << std::endl;
    
    demonstrateBasicDelegation();
    demonstrateStrategyDelegation();
    demonstrateDecoratorDelegation();
    demonstrateProxyDelegation();
    
    std::cout << "\n=== 委托模式总结 ===" << std::endl;
    std::cout << "委托的核心思想：将任务交给其他对象来完成" << std::endl;
    std::cout << "优势：" << std::endl;
    std::cout << "1. 松耦合：委托者和被委托者相对独立" << std::endl;
    std::cout << "2. 灵活性：可以运行时更换委托对象" << std::endl;
    std::cout << "3. 职责分离：每个类专注于自己的职责" << std::endl;
    std::cout << "4. 代码复用：多个类可以委托给同一个对象" << std::endl;
    
    return 0;
}
