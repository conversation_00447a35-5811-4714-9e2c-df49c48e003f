#include <iostream>

// 最接近您在力扣/牛客看到的实现
class FactorialTrick {
private:
    static int result;
    static int n;

public:
    // 重载[]运算符，这是关键技巧
    FactorialTrick& operator[](int) {
        result *= (n > 0) ? n : 1;  // 用三目运算符替代if
        n = (n > 1) ? (n - 1) : 0;  // 用三目运算符控制递减
        return *this;               // 返回引用支持链式调用
    }

    static int factorial(int num) {
        n = num;
        result = 1;
        
        // 🔥 这就是您看到的神奇代码！
        // 看起来像数组访问，实际是链式调用operator[]
        FactorialTrick()[0][1][2][3][4][5][6][7][8][9];
        
        return result;
    }
};

int FactorialTrick::result = 1;
int FactorialTrick::n = 0;

int main() {
    std::cout << "=== 力扣/牛客经典技巧：不用if和循环计算阶乘 ===" << std::endl;
    
    for (int i = 1; i <= 6; i++) {
        std::cout << i << "! = " << FactorialTrick::factorial(i) << std::endl;
    }
    
    std::cout << "\n🔥 核心技巧解析：" << std::endl;
    std::cout << "1. FactorialTrick()[0][1][2][3] 看起来像数组访问" << std::endl;
    std::cout << "2. 实际上是连续调用 operator[]" << std::endl;
    std::cout << "3. 每次调用都执行一次乘法运算" << std::endl;
    std::cout << "4. 通过返回*this实现链式调用" << std::endl;
    std::cout << "5. 用三目运算符替代if语句进行条件判断" << std::endl;
    
    return 0;
}
